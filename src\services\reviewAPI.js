// Review API Service
import { apiFetch } from './apiFetch';
import { getAuthToken } from './authService';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';

export const reviewAPI = {
  /**
   * Get reviews for a product
   */
  async getProductReviews(productId, page = 1, limit = 10, sortBy = 'created_at', sortOrder = 'DESC') {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sort_by: sortBy,
        sort_order: sortOrder
      });

      const response = await apiFetch(`${API_BASE_URL}/products/${productId}/reviews?${params}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch reviews: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Get product reviews failed:', error);
      throw error;
    }
  },

  /**
   * Create a new review
   */
  async createReview(productId, reviewData) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/products/${productId}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create review: ${response.status}`);
      }

      const data = await response.json();
      // Backend returns { success: true, data: { review: {...}, message: "..." } }
      // We need to return the review object specifically
      return data.data?.review || data.data || data;
    } catch (error) {
      console.error('Create review failed:', error);
      throw error;
    }
  },

  /**
   * Update a review
   */
  async updateReview(reviewId, reviewData) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(reviewData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to update review: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Update review failed:', error);
      throw error;
    }
  },

  /**
   * Delete a review
   */
  async deleteReview(reviewId) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete review: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Delete review failed:', error);
      throw error;
    }
  },

  /**
   * Vote on a review (helpful/not helpful)
   */
  async voteReview(reviewId, vote) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}/vote`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ vote })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to vote on review: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Vote review failed:', error);
      throw error;
    }
  },

  /**
   * Flag a review as inappropriate
   */
  async flagReview(reviewId, reason = 'inappropriate') {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}/flag`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to flag review: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Flag review failed:', error);
      throw error;
    }
  },

  /**
   * Upload images for a review
   */
  async uploadReviewImages(reviewId, images) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const formData = new FormData();

      // Add images to FormData
      for (let i = 0; i < images.length; i++) {
        formData.append('images[]', images[i]);
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}/images`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to upload images: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Upload review images failed:', error);
      throw error;
    }
  },

  /**
   * Delete a review image
   */
  async deleteReviewImage(reviewId, imageId) {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required');
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}/images/${imageId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete image: ${response.status}`);
      }

      const data = await response.json();
      return data.data || data;
    } catch (error) {
      console.error('Delete review image failed:', error);
      throw error;
    }
  },

  /**
   * Check if user has already reviewed a product
   */
  async checkUserReview(productId) {
    try {
      const token = getAuthToken();
      if (!token) {
        // If no token, user hasn't reviewed (not logged in)
        return { hasReviewed: false };
      }

      const response = await apiFetch(`${API_BASE_URL}/products/${productId}/user-review`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        if (response.status === 404) {
          // No review found
          return { hasReviewed: false };
        }
        throw new Error(`Failed to check user review: ${response.status}`);
      }

      const data = await response.json();
      return {
        hasReviewed: true,
        review: data.data || data
      };
    } catch (error) {
      console.error('Check user review failed:', error);
      // On error, assume user hasn't reviewed to be safe
      return { hasReviewed: false };
    }
  },

  /**
   * Check if user has voted on a review
   */
  async checkUserVote(reviewId) {
    try {
      const token = getAuthToken();
      if (!token) {
        return { hasVoted: false, vote: null };
      }

      const response = await apiFetch(`${API_BASE_URL}/reviews/${reviewId}/user-vote`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to check user vote: ${response.status}`);
      }

      const data = await response.json();
      return {
        hasVoted: data.data?.has_voted || false,
        vote: data.data?.vote || null
      };
    } catch (error) {
      console.error('Check user vote failed:', error);
      return { hasVoted: false, vote: null };
    }
  },

  /**
   * Get review statistics for a product (for use in product cards, wishlist, etc.)
   */
  async getProductReviewStats(productId) {
    try {
      const response = await apiFetch(`${API_BASE_URL}/products/${productId}/reviews?limit=1`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch review stats: ${response.status}`);
      }

      const data = await response.json();
      return data.data?.stats || {
        average_rating: 0,
        total_reviews: 0,
        rating_distribution: [0, 0, 0, 0, 0]
      };
    } catch (error) {
      console.error('Get product review stats failed:', error);
      return {
        average_rating: 0,
        total_reviews: 0,
        rating_distribution: [0, 0, 0, 0, 0]
      };
    }
  },

  /**
   * Get review statistics for multiple products (batch request)
   */
  async getMultipleProductReviewStats(productIds) {
    try {
      const promises = productIds.map(id => this.getProductReviewStats(id));
      const results = await Promise.all(promises);

      const statsMap = {};
      productIds.forEach((id, index) => {
        statsMap[id] = results[index];
      });

      return statsMap;
    } catch (error) {
      console.error('Get multiple product review stats failed:', error);
      return {};
    }
  }
};
