<?php
/**
 * Data Management Script for Wolffoxx Ecommerce
 * 
 * Easy client data replacement and management
 */

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

require_once __DIR__ . '/../config/database.php';

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

class DataManager
{
    /**
     * Backup current database
     */
    public static function backupDatabase($filename = null)
    {
        $filename = $filename ?: 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        $backupPath = __DIR__ . '/backups/' . $filename;
        
        // Create backups directory if it doesn't exist
        if (!is_dir(__DIR__ . '/backups')) {
            mkdir(__DIR__ . '/backups', 0755, true);
        }
        
        // Get database config
        $host = $_ENV['DB_HOST'] ?? 'localhost';
        $dbname = $_ENV['DB_NAME'] ?? 'wolffoxx_ecommerce';
        $username = $_ENV['DB_USER'] ?? 'root';
        $password = $_ENV['DB_PASS'] ?? '';
        
        // Create mysqldump command
        $command = "mysqldump -h{$host} -u{$username}";
        if ($password) {
            $command .= " -p{$password}";
        }
        $command .= " {$dbname} > {$backupPath}";
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0) {
            echo "✅ Database backed up to: {$backupPath}\n";
            return $backupPath;
        } else {
            echo "❌ Backup failed\n";
            return false;
        }
    }
    
    /**
     * Clear all product data (for client data replacement)
     */
    public static function clearProductData()
    {
        try {
            Database::beginTransaction();
            
            // Clear in correct order due to foreign key constraints
            Database::execute("DELETE FROM order_items");
            Database::execute("DELETE FROM cart_items");
            Database::execute("DELETE FROM product_images");
            Database::execute("DELETE FROM product_colors");
            Database::execute("DELETE FROM product_sizes");
            Database::execute("DELETE FROM products");
            
            // Reset auto increment
            Database::execute("ALTER TABLE products AUTO_INCREMENT = 1");
            Database::execute("ALTER TABLE product_images AUTO_INCREMENT = 1");
            Database::execute("ALTER TABLE product_colors AUTO_INCREMENT = 1");
            Database::execute("ALTER TABLE product_sizes AUTO_INCREMENT = 1");
            
            Database::commit();
            echo "✅ All product data cleared successfully\n";
            
        } catch (\Exception $e) {
            Database::rollback();
            echo "❌ Failed to clear product data: " . $e->getMessage() . "\n";
        }
    }
    
    /**
     * Import products from CSV file
     */
    public static function importProductsFromCSV($csvFile)
    {
        if (!file_exists($csvFile)) {
            echo "❌ CSV file not found: {$csvFile}\n";
            return;
        }
        
        $handle = fopen($csvFile, 'r');
        $header = fgetcsv($handle); // Skip header row
        
        $imported = 0;
        $errors = 0;
        
        while (($data = fgetcsv($handle)) !== FALSE) {
            try {
                // Map CSV columns to database fields
                $product = [
                    'name' => $data[0],
                    'category' => $data[1],
                    'price' => (float)$data[2],
                    'sale_price' => !empty($data[3]) ? (float)$data[3] : null,
                    'description' => $data[4],
                    'material' => $data[5] ?? '100% Cotton',
                    'fit' => $data[6] ?? 'Regular',
                    'is_new' => (bool)($data[7] ?? 0),
                    'is_bestseller' => (bool)($data[8] ?? 0),
                    'is_trending' => (bool)($data[9] ?? 0),
                    'stock_quantity' => (int)($data[10] ?? 50)
                ];
                
                self::insertProduct($product);
                $imported++;
                
            } catch (\Exception $e) {
                echo "❌ Error importing row {$imported}: " . $e->getMessage() . "\n";
                $errors++;
            }
        }
        
        fclose($handle);
        echo "✅ Import complete: {$imported} products imported, {$errors} errors\n";
    }
    
    /**
     * Insert single product
     */
    private static function insertProduct($product)
    {
        $sql = "INSERT INTO products (
            uuid, name, slug, description, price, sale_price, sku, category, 
            material, fit, stock_quantity, is_active, is_new, is_bestseller, 
            is_trending, average_rating
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, 4.5)";
        
        $params = [
            self::generateUUID(),
            $product['name'],
            self::generateSlug($product['name']),
            $product['description'],
            $product['price'],
            $product['sale_price'],
            self::generateSKU($product['category']),
            $product['category'],
            $product['material'],
            $product['fit'],
            $product['stock_quantity'],
            $product['is_new'],
            $product['is_bestseller'],
            $product['is_trending']
        ];
        
        Database::execute($sql, $params);
        return Database::lastInsertId();
    }
    
    /**
     * Generate product slug
     */
    private static function generateSlug($name)
    {
        return strtolower(preg_replace('/[^A-Za-z0-9-]+/', '-', $name));
    }
    
    /**
     * Generate SKU
     */
    private static function generateSKU($category)
    {
        $prefix = strtoupper(substr($category, 0, 2));
        $number = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        return $prefix . $number;
    }
    
    /**
     * Generate UUID
     */
    private static function generateUUID()
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * Create sample CSV template
     */
    public static function createCSVTemplate($filename = 'product_template.csv')
    {
        $templatePath = __DIR__ . '/' . $filename;
        
        $header = [
            'name', 'category', 'price', 'sale_price', 'description', 
            'material', 'fit', 'is_new', 'is_bestseller', 'is_trending', 'stock_quantity'
        ];
        
        $sampleData = [
            [
                'Premium Cotton T-Shirt',
                'T-Shirts',
                '29.99',
                '',
                'High-quality cotton t-shirt with comfortable fit',
                '100% Cotton',
                'Regular',
                '1',
                '0',
                '0',
                '50'
            ],
            [
                'Oversized Graphic Tee',
                'Oversized Tees',
                '49.99',
                '39.99',
                'Trendy oversized tee with unique graphic design',
                '100% Cotton',
                'Oversized',
                '1',
                '1',
                '1',
                '30'
            ]
        ];
        
        $file = fopen($templatePath, 'w');
        fputcsv($file, $header);
        
        foreach ($sampleData as $row) {
            fputcsv($file, $row);
        }
        
        fclose($file);
        echo "✅ CSV template created: {$templatePath}\n";
    }
    
    /**
     * Show database statistics
     */
    public static function showStats()
    {
        $stats = [
            'Products' => self::getCount('products'),
            'Product Images' => self::getCount('product_images'),
            'Product Colors' => self::getCount('product_colors'),
            'Product Sizes' => self::getCount('product_sizes'),
            'Categories' => self::getCount('product_categories'),
            'Users' => self::getCount('users'),
            'Orders' => self::getCount('orders'),
            'Cart Items' => self::getCount('cart_items')
        ];
        
        echo "\n📊 DATABASE STATISTICS:\n";
        echo "========================\n";
        foreach ($stats as $table => $count) {
            echo sprintf("%-15s: %d\n", $table, $count);
        }
        echo "\n";
    }
    
    private static function getCount($table)
    {
        $sql = "SELECT COUNT(*) as count FROM {$table}";
        $result = Database::execute($sql)->fetch();
        return (int)$result['count'];
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    echo "🛠️  WOLFFOXX DATA MANAGEMENT TOOL\n";
    echo "==================================\n\n";
    
    if ($argc < 2) {
        echo "Usage: php data_management.php [command] [options]\n\n";
        echo "Commands:\n";
        echo "  backup                    - Backup current database\n";
        echo "  clear                     - Clear all product data\n";
        echo "  import [csv_file]         - Import products from CSV\n";
        echo "  template                  - Create CSV template\n";
        echo "  stats                     - Show database statistics\n\n";
        exit;
    }
    
    $command = $argv[1];
    
    switch ($command) {
        case 'backup':
            DataManager::backupDatabase();
            break;
            
        case 'clear':
            echo "⚠️  This will delete ALL product data. Are you sure? (y/N): ";
            $confirm = trim(fgets(STDIN));
            if (strtolower($confirm) === 'y') {
                DataManager::clearProductData();
            } else {
                echo "Operation cancelled.\n";
            }
            break;
            
        case 'import':
            if (isset($argv[2])) {
                DataManager::importProductsFromCSV($argv[2]);
            } else {
                echo "❌ Please specify CSV file path\n";
            }
            break;
            
        case 'template':
            DataManager::createCSVTemplate();
            break;
            
        case 'stats':
            DataManager::showStats();
            break;
            
        default:
            echo "❌ Unknown command: {$command}\n";
    }
}
