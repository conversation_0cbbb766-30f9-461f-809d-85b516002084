<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - <?= htmlspecialchars($order['order_number']) ?></title>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            margin: 0;
            padding: 20px;
            background-color: #0a0a0a;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #0a0a0a 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
            position: relative;
        }
        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: #ffffff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 10px;
        }
        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -0.5px;
            background: linear-gradient(135deg, #ffffff 0%, #e5e7eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .order-number {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 8px 16px;
            border-radius: 6px;
            margin-top: 15px;
            display: inline-block;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .order-summary {
            background-color: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .item-details {
            flex: 1;
        }
        .item-name {
            font-weight: 600;
            color: #1f2937;
        }
        .item-variant {
            color: #6b7280;
            font-size: 14px;
        }
        .item-price {
            font-weight: 600;
            color: #1f2937;
        }
        .total-section {
            border-top: 2px solid #e5e7eb;
            padding-top: 15px;
            margin-top: 15px;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
        }
        .total-final {
            font-weight: 700;
            font-size: 18px;
            color: #1f2937;
        }
        .shipping-info {
            background-color: #f0f9ff;
            border-left: 4px solid #3b82f6;
            padding: 20px;
            margin: 20px 0;
        }
        .footer {
            background-color: #f8fafc;
            padding: 30px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }
        .footer a {
            color: #3b82f6;
            text-decoration: none;
        }
        .track-button {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
<!--            <div class="logo">-->
<!--                --><?php
//                $logoUrl = ($_ENV['BACKEND_URL'] ?? 'http://localhost:8000') . '/assets/logo30.png';
//                $logoPath = __DIR__ . '/../../public/assets/logo30.png';
//                ?>
<!--                --><?php //if (file_exists($logoPath)): ?>
<!--                    <img src="--><?php //= $logoUrl ?><!--"-->
<!--                         alt="Wolffoxx Logo"-->
<!--                         style="width: 60px; height: 60px; object-fit: contain;"-->
<!--                         onerror="this.style.display='none'; this.parentNode.innerHTML='<div style=\'font-size:32px;font-weight:900;color:#000;letter-spacing:-1px;\'>WF</div>';" />-->
<!--                --><?php //else: ?>
<!--                    <div style="font-size:32px;font-weight:900;color:#000;letter-spacing:-1px;">WF</div>-->
<!--                --><?php //endif; ?>
<!--            </div>-->
            <h1>Order Confirmed!</h1>
            <div class="order-number">Order #<?= htmlspecialchars($order['order_number']) ?></div>
        </div>
        
        <div class="content">
            <div class="greeting">
                Hi <?= htmlspecialchars($user['first_name'] ?? $user['name'] ?? 'Valued Customer') ?>,
            </div>

            <p>🎉 <strong>Thank you for your order!</strong> We've received your order and our team is already working on getting it ready for you. You'll receive another email with tracking information once your order ships.</p>

            <p>Here's a summary of your order:</p>
            
            <div class="order-summary">
                <h3 style="margin-top: 0;">Order Summary</h3>
                
                <?php 
                // Group order items by variant (product_id + color + size)
                $groupedItems = [];
                $processedKeys = []; // Track which unique items we've already processed
                
                foreach ($orderItems as $item) {
                    // Create a unique key based on product_id, color, and size
                    $key = sprintf(
                        '%s_%s_%s', 
                        $item['product_id'],
                        strtolower(trim($item['selected_color'] ?? '')),
                        strtolower(trim($item['selected_size'] ?? ''))
                    );
                    
                    // If we haven't processed this exact item variant yet
                    if (!in_array($key, $processedKeys)) {
                        // Use sale price if available and greater than 0, otherwise use unit price
                        $effectivePrice = (!empty($item['sale_price']) && $item['sale_price'] > 0)
                            ? $item['sale_price']
                            : ($item['price'] ?? $item['unit_price'] ?? 0);

                        $groupedItems[$key] = [
                            'product_id' => $item['product_id'],
                            'product_name' => $item['product_name'],
                            'selected_color' => $item['selected_color'] ?? null,
                            'selected_size' => $item['selected_size'] ?? null,
                            'product_image' => $item['product_image'] ?? ($item['images'][0] ?? null),
                            'price' => $effectivePrice,
                            'quantity' => (int)($item['quantity'] ?? 1),
                            'total_price' => $effectivePrice * (int)($item['quantity'] ?? 1)
                        ];
                        
                        // Mark this variant as processed
                        $processedKeys[] = $key;
                    }
                }
                
                // Display grouped items
                foreach ($groupedItems as $item): 
                    $img = $item['product_image'];
                ?>
                <div class="order-item" style="display:flex;align-items:center;gap:12px;padding:12px 0;border-bottom:1px solid #e5e7eb;">
                    <?php if ($img): ?>
                    <img src="<?= htmlspecialchars($img) ?>" 
                         alt="<?= htmlspecialchars($item['product_name']) ?>" 
                         style="width:64px;height:64px;object-fit:cover;border-radius:8px;border:1px solid #e5e7eb;" 
                         onerror="this.onerror=null; this.src='https://via.placeholder.com/64?text=No+Image';" />
                    <?php else: ?>
                    <div style="width:64px;height:64px;background:#f3f4f6;display:flex;align-items:center;justify-content:center;border-radius:8px;border:1px solid #e5e7eb;">
                        <span style="font-size:10px;color:#9ca3af;text-align:center;">No Image</span>
                    </div>
                    <?php endif; ?>
                    <div class="item-details" style="flex:1;">
                        <div class="item-name" style="font-weight:600;color:#1f2937;margin-bottom:4px;">
                            <?= htmlspecialchars($item['product_name']) ?>
                        </div>
                        <div class="item-variant" style="font-size:14px;color:#6b7280;margin-bottom:4px;">
                            <?php if (!empty($item['selected_color']) || !empty($item['selected_size'])): ?>
                                <?php if (!empty($item['selected_color'])): ?>
                                    <span>Color: <?= htmlspecialchars($item['selected_color']) ?></span>
                                <?php endif; ?>
                                <?php if (!empty($item['selected_size'])): ?>
                                    <?= !empty($item['selected_color']) ? ' | ' : '' ?>
                                    <span>Size: <?= htmlspecialchars($item['selected_size']) ?></span>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <div class="item-variant" style="font-size:14px;color:#6b7280;">
                            Qty: <?= $item['quantity'] ?>
                        </div>
                    </div>
                    <div class="item-price" style="font-weight:600;color:#1f2937;min-width:80px;text-align:right;">
                        ₹<?= number_format($item['total_price'], 2) ?>
                    </div>
                </div>
                <?php endforeach; ?>
                
                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span>₹<?= number_format($order['subtotal'], 2) ?></span>
                    </div>
                    <?php if ($order['tax_amount'] > 0): ?>
                    <div class="total-row">
                        <span>Tax (7%):</span>
                        <span>₹<?= number_format($order['tax_amount'], 2) ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="total-row">
                        <span>Shipping:</span>
                        <span><?= ($order['shipping_amount'] > 0) ? '₹' . number_format($order['shipping_amount'], 2) : 'Free' ?></span>
                    </div>
                    <?php if ($order['discount_amount'] > 0): ?>
                    <div class="total-row">
                        <span>Discount:</span>
                        <span>-₹<?= number_format($order['discount_amount'], 2) ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="total-row total-final">
                        <span>Total:</span>
                        <span>₹<?= number_format($order['total_amount'], 2) ?></span>
                    </div>
                </div>
            </div>
            
            <div class="shipping-info">
                <h4 style="margin-top: 0;">Shipping Information</h4>
                <?php 
                $shippingAddress = is_string($order['shipping_address']) ? 
                    json_decode($order['shipping_address'], true) : 
                    $order['shipping_address'];
                
                // If shipping address is empty, try to get from order fields
                if (empty($shippingAddress) || (is_array($shippingAddress) && empty(array_filter($shippingAddress)))) {
                    $shippingAddress = [
                        'name' => $order['customer_name'] ?? '',
                        'address_line_1' => $order['shipping_address_line1'] ?? '',
                        'address_line_2' => $order['shipping_address_line2'] ?? '',
                        'city' => $order['shipping_city'] ?? '',
                        'state' => $order['shipping_state'] ?? '',
                        'postal_code' => $order['shipping_postal_code'] ?? '',
                        'country' => $order['shipping_country'] ?? 'India',
                        'phone' => $order['customer_phone'] ?? ''
                    ];
                }
                ?>
                <p>
                    <?= !empty($shippingAddress['name']) ? htmlspecialchars($shippingAddress['name']) . '<br>' : '' ?>
                    <?= !empty($shippingAddress['address_line_1']) ? htmlspecialchars($shippingAddress['address_line_1']) . '<br>' : '' ?>
                    <?= !empty($shippingAddress['address_line_2']) ? htmlspecialchars($shippingAddress['address_line_2']) . '<br>' : '' ?>
                    <?= !empty($shippingAddress['city']) ? htmlspecialchars($shippingAddress['city']) . ', ' : '' ?>
                    <?= !empty($shippingAddress['state']) ? htmlspecialchars($shippingAddress['state']) . ' ' : '' ?>
                    <?= !empty($shippingAddress['postal_code']) ? htmlspecialchars($shippingAddress['postal_code']) . '<br>' : '' ?>
                    <?= !empty($shippingAddress['country']) ? htmlspecialchars($shippingAddress['country']) : '' ?>
                    <?php if (!empty($shippingAddress['phone'])): ?>
                        <br>Phone: <?= htmlspecialchars($shippingAddress['phone']) ?>
                    <?php endif; ?>
                </p>
            </div>
            
            <div style="text-align: center;">
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3000' ?>/orders/<?= $order['id'] ?>" class="track-button">
                    Track Your Order
                </a>
            </div>
            
            <p>If you have any questions about your order, please contact our customer support team.</p>
        </div>
        
        <div class="footer">
            <div class="brand">WOLFFOXX</div>
            <p>Premium streetwear for the modern generation</p>
            <p style="margin: 20px 0;">
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3173' ?>">Visit our website</a> |
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3173' ?>/contact">Contact Support</a> |
                <a href="<?= $_ENV['FRONTEND_URL'] ?? 'http://localhost:3173' ?>/orders">Order History</a>
            </p>
            <p>&copy; <?= date('Y') ?> Wolffoxx. All rights reserved.</p>
            <p style="margin-top: 15px; font-size: 12px; opacity: 0.7;">
                This email was sent to <?= htmlspecialchars($user['email'] ?? $order['customer_email']) ?>.
                Order placed on <?= date('F j, Y \a\t g:i A', strtotime($order['created_at'])) ?>.
            </p>
        </div>
    </div>
</body>
</html>
