// Email Service - Handles sending emails using backend SMTP configuration
import { getAuthToken } from './authService.js';

/**
 * Email Service for sending various types of emails
 */
export const emailService = {
  /**
   * Send an order confirmation email
   * @param {Object} orderDetails - Order details including customer info and items
   * @returns {Promise} - Promise that resolves when email is sent
   */
  async sendOrderConfirmationEmail(orderDetails) {
    try {
      console.log('📧 Sending order confirmation email for order:', orderDetails.order_number);
      
      // Validate required fields
      if (!orderDetails.order_number) {
        throw new Error('Order number is required for sending confirmation email');
      }
     
      if (!orderDetails.customer_email) {
        throw new Error('Customer email is required for sending confirmation email');
      }
      
      if (!orderDetails.customer_name) {
        throw new Error('Customer name is required for sending confirmation email');
      }
      
      if (!orderDetails.items || !Array.isArray(orderDetails.items) || orderDetails.items.length === 0) {
        throw new Error('Order items are required for sending confirmation email');
      }
      
      // Get authentication token
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication required to send order confirmation email');
      }
      
      // Format shipping address for email
      const formattedShippingAddress = orderDetails.shipping_address ? {
        line1: orderDetails.shipping_address.line1 || orderDetails.shipping_address.street || '',
        line2: orderDetails.shipping_address.line2 || '',
        city: orderDetails.shipping_address.city || '',
        state: orderDetails.shipping_address.state || '',
        postal_code: orderDetails.shipping_address.postal_code || orderDetails.shipping_address.zip || '',
        country: orderDetails.shipping_address.country || 'India'
      } : null;
      
      // Calculate estimated delivery date (7 days from now)
      const estimatedDelivery = new Date();
      estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
      
      // Prepare email data
      const emailData = {
         order_id: orderDetails.order_number,
        customer_email: orderDetails.customer_email,
        customer_name: orderDetails.customer_name,
        order_details: {
          items: orderDetails.items,
          total_amount: orderDetails.total_amount,
          order_date: new Date().toISOString(),
          estimated_delivery: estimatedDelivery.toISOString(),
          shipping_address: formattedShippingAddress
        },
        // Send a copy to admin
        cc_admin: true
      };
      
      console.log('📧 Email data:', emailData);
      
      // Send the email request to the backend
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'}/emails/order-confirmation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(emailData)
      });

      // Handle response
      if (!response.ok) {
        const errorText = await response.text();
        console.error('📧 Error sending order confirmation email:', errorText);
        throw new Error(`Failed to send email: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('📧 Email sent successfully:', data);
      return data;
    } catch (error) {
      console.error('📧 Failed to send order confirmation email:', error);
      // Don't throw the error - we don't want to block the order confirmation process
      // if email sending fails
      return {
        success: false,
        error: error.message
      };
    }
  }
};