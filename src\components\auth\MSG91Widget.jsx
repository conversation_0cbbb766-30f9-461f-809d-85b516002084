import React, { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';

const MSG91Widget = ({ phone, onSuccess, onError }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [widgetConfig, setWidgetConfig] = useState(null);
  const [isScriptLoaded, setIsScriptLoaded] = useState(false);
  const { apiRequest } = useAuth();

  useEffect(() => {
    // Get widget configuration from backend
    const getWidgetConfig = async () => {
      try {
        const response = await apiRequest('/auth/otp/widget-config');
        if (response.success) {
          setWidgetConfig(response.data);
          loadMSG91Script(response.data);
        } else {
          onError('Failed to load OTP widget configuration');
        }
      } catch (error) {
        console.error('Widget config error:', error);
        onError('Failed to load OTP widget');
      }
    };

    getWidgetConfig();
  }, []);

  const loadMSG91Script = (config) => {
    // Check if script is already loaded
    if (window.initSendOTP) {
      initializeWidget(config);
      return;
    }

    // Load MSG91 script
    const script = document.createElement('script');
    script.src = config.scriptUrl;
    script.type = 'text/javascript';
    script.onload = () => {
      setIsScriptLoaded(true);
      initializeWidget(config);
    };
    script.onerror = () => {
      onError('Failed to load MSG91 script');
    };
    document.body.appendChild(script);
  };

  const initializeWidget = (config) => {
    try {
      const configuration = {
        widgetId: config.widgetId,
        tokenAuth: config.authKey,
        identifier: phone,
        exposeMethods: true,
        success: (data) => {
          console.log('MSG91 Widget Success:', data);
          handleWidgetSuccess(data);
        },
        failure: (error) => {
          console.error('MSG91 Widget Error:', error);
          onError('OTP verification failed: ' + (error.message || 'Unknown error'));
        }
      };

      // Initialize the widget
      if (window.initSendOTP) {
        window.initSendOTP(configuration);
        setIsLoading(false);
      } else {
        onError('MSG91 widget not available');
      }
    } catch (error) {
      console.error('Widget initialization error:', error);
      onError('Failed to initialize OTP widget');
    }
  };

  const handleWidgetSuccess = async (data) => {
    try {
      // Extract access token from widget response
      const accessToken = data.access_token || data.accessToken || data.token;
      
      if (!accessToken) {
        onError('No access token received from OTP verification');
        return;
      }

      // Verify token with our backend
      const response = await apiRequest('/auth/otp/verify', {
        method: 'POST',
        body: JSON.stringify({
          access_token: accessToken
        })
      });

      if (response.success) {
        onSuccess(response.data);
      } else {
        onError(response.message || 'Token verification failed');
      }
    } catch (error) {
      console.error('Token verification error:', error);
      onError('Failed to verify OTP token');
    }
  };

  const sendOTP = () => {
    if (window.sendOtp && phone) {
      window.sendOtp(
        phone,
        (data) => {
          console.log('OTP sent successfully:', data);
        },
        (error) => {
          console.error('OTP send error:', error);
          onError('Failed to send OTP: ' + (error.message || 'Unknown error'));
        }
      );
    } else {
      onError('OTP service not available');
    }
  };

  const verifyOTP = (otp) => {
    if (window.verifyOtp && otp) {
      window.verifyOtp(
        otp,
        (data) => {
          console.log('OTP verified successfully:', data);
          handleWidgetSuccess(data);
        },
        (error) => {
          console.error('OTP verify error:', error);
          onError('Invalid OTP: ' + (error.message || 'Please try again'));
        }
      );
    } else {
      onError('OTP verification service not available');
    }
  };

  const retryOTP = () => {
    if (window.retryOtp) {
      window.retryOtp(
        null, // Use default channel
        (data) => {
          console.log('OTP resent successfully:', data);
        },
        (error) => {
          console.error('OTP retry error:', error);
          onError('Failed to resend OTP: ' + (error.message || 'Unknown error'));
        }
      );
    } else {
      onError('OTP retry service not available');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600">Loading OTP service...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Verify Your Phone Number
        </h3>
        <p className="text-gray-600 mb-4">
          We'll send an OTP to <span className="font-medium">{phone}</span>
        </p>
      </div>

      <div className="space-y-4">
        <button
          onClick={sendOTP}
          className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-blue-800 transition-all duration-200"
        >
          Send OTP
        </button>

        <div className="text-center">
          <span className="text-gray-500">or</span>
        </div>

        <OTPInput onVerify={verifyOTP} onRetry={retryOTP} />
      </div>
    </div>
  );
};

// Simple OTP Input Component
const OTPInput = ({ onVerify, onRetry }) => {
  const [otp, setOtp] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (otp.length === 6) {
      onVerify(otp);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Enter 6-digit OTP
        </label>
        <input
          type="text"
          value={otp}
          onChange={(e) => setOtp(e.target.value.replace(/\D/g, '').slice(0, 6))}
          placeholder="000000"
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono"
          maxLength={6}
        />
      </div>

      <div className="flex space-x-3">
        <button
          type="submit"
          disabled={otp.length !== 6}
          className="flex-1 bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          Verify OTP
        </button>
        
        <button
          type="button"
          onClick={onRetry}
          className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
        >
          Resend OTP
        </button>
      </div>
    </form>
  );
};

export default MSG91Widget;
