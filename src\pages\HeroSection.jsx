import React, { useState, useEffect, useRef } from 'react';
import { motion, useScroll, useTransform, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const Luxury3DHeroGallery = () => {
  const { scrollY } = useScroll();
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [currentIndex, setCurrentIndex] = useState(0);
  const containerRef = useRef(null);
  
  // Featured products - one from each category
  const featuredProducts = [
    {
      id: 1,
      name: "Premium Oversized Tee",
      description: "100% Cotton Premium Quality",
      price: "₹999",
      image: "https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=800&auto=format&fit=crop",
      category: "OVERSIZED TEES"
    },
    {
      id: 2,
      name: "Classic Cotton Tee",
      description: "Essential Wardrobe Staple",
      price: "₹699",
      image: "https://images.unsplash.com/photo-1571945153237-4929e783af4a?q=80&w=800&auto=format&fit=crop",
      category: "T-SHIRTS"
    },
    {
      id: 3,
      name: "Luxury Hoodie",
      description: "Premium French Terry",
      price: "₹2499",
      image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?q=80&w=800&auto=format&fit=crop",
      category: "HOODIES"
    },
    {
      id: 4,
      name: "Designer Oxford Shirt",
      description: "Tailored Precision",
      price: "₹1899",
      image: "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?q=80&w=800&auto=format&fit=crop",
      category: "SHIRTS"
    }
  ];

  // Mouse tracking for subtle 3D effects
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        setMousePosition({
          x: (e.clientX - centerX) / rect.width,
          y: (e.clientY - centerY) / rect.height,
        });
      }
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const rotateCarousel = (direction) => {
    if (direction === 'next') {
      setCurrentIndex((prev) => (prev + 1) % featuredProducts.length);
    } else {
      setCurrentIndex((prev) => (prev - 1 + featuredProducts.length) % featuredProducts.length);
    }
  };

  // Scroll-based transforms
  const galleryY = useTransform(scrollY, [0, 500], [0, -100]);
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -200]);

  return (
    <div ref={containerRef} className="min-h-screen bg-slate-900 relative overflow-hidden">
      {/* Hero Section */}
      <motion.section 
        className="min-h-screen flex items-center justify-center"
        style={{ y: backgroundY }}
      >
        <div className="container mx-auto px-6">
          {/* Section Header */}
          {/* <motion.div 
            className="text-center mb-20"
            initial={{ opacity: 0, y: 60 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.3 }}
          >
            <motion.h1 
              className="text-7xl md:text-9xl lg:text-[12rem] font-light tracking-wider mb-8 text-white"
              style={{
                transform: `rotateX(${mousePosition.y * 2}deg) rotateY(${mousePosition.x * 2}deg)`
              }}
            >
              WOLFFOXX
            </motion.h1>
            <motion.p 
              className="text-slate-400 text-xl font-light max-w-3xl mx-auto"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              Discover our curated collection of premium menswear, crafted with attention to detail and modern aesthetics.
            </motion.p>
          </motion.div> */}

          {/* 3D Product Gallery */}
          <motion.div 
            className="relative h-[800px] flex items-center justify-center"
            style={{ y: galleryY }}
          >
            {/* Carousel Container */}
            <div className="relative w-full max-w-6xl h-full perspective-1000">
              <AnimatePresence mode="wait">
                {featuredProducts.map((product, index) => {
                  const offset = index - currentIndex;
                  const absOffset = Math.abs(offset);
                  
                  return (
                    <motion.div
                      key={product.id}
                      className="absolute inset-0 flex flex-col items-center justify-center"
                      initial={{ 
                        opacity: 0,
                        rotateY: offset * 25,
                        z: -absOffset * 300,
                        scale: 0.7 - absOffset * 0.15
                      }}
                      animate={{ 
                        opacity: absOffset === 0 ? 1 : 0.4,
                        rotateY: offset * 25 + (mousePosition.x * 8),
                        z: -absOffset * 300,
                        scale: 0.7 - absOffset * 0.15,
                        x: offset * 400,
                      }}
                      exit={{ 
                        opacity: 0,
                        rotateY: 90,
                        scale: 0.3
                      }}
                      transition={{ 
                        duration: 0.9,
                        ease: "easeInOut",
                        type: "spring",
                        stiffness: 80
                      }}
                      style={{
                        transformStyle: 'preserve-3d',
                        zIndex: 10 - absOffset
                      }}
                    >
                      {/* Product Container */}
                      <motion.div
                        className="relative group cursor-pointer"
                        whileHover={{ 
                          scale: 1.08,
                          rotateY: mousePosition.x * 12,
                          rotateX: mousePosition.y * 6
                        }}
                        style={{
                          transformStyle: 'preserve-3d'
                        }}
                      >
                        {/* Product Image with 3D Effect */}
                        <motion.div
                          className="relative w-[500px] h-[600px] bg-gradient-to-br from-slate-800 to-slate-700 rounded-3xl shadow-2xl overflow-hidden border border-slate-600/30"
                          style={{
                            transform: `rotateX(${mousePosition.y * 4}deg) rotateY(${mousePosition.x * 4}deg)`,
                            transformStyle: 'preserve-3d',
                            boxShadow: '0 40px 80px rgba(0,0,0,0.4), 0 20px 40px rgba(0,0,0,0.2), 0 0 40px rgba(59, 130, 246, 0.1)'
                          }}
                        >
                          <motion.img
                            src={product.image}
                            alt={product.name}
                            className="w-full h-full object-cover"
                            style={{
                              transform: `translateZ(60px) scale(${1 + mousePosition.x * 0.03})`
                            }}
                          />
                          
                          {/* Subtle overlay */}
                          <div className="absolute inset-0 bg-gradient-to-t from-slate-900/20 to-transparent" />
                          
                          {/* 3D Reflection */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-br from-blue-400/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700"
                            style={{
                              transform: 'translateZ(15px)'
                            }}
                          />
                          
                          {/* Glow effect */}
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                            style={{
                              transform: 'translateZ(5px)'
                            }}
                          />
                        </motion.div>

                        {/* Enhanced Floating Shadow */}
                        <motion.div
                          className="absolute -bottom-12 left-1/2 w-80 h-24 bg-black/20 rounded-full blur-2xl"
                          style={{
                            transform: `translateX(-50%) translateZ(-150px) scale(${1 - absOffset * 0.3})`
                          }}
                          animate={{
                            scale: [1, 1.2, 1],
                            opacity: [0.3, 0.5, 0.3]
                          }}
                          transition={{
                            duration: 5,
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                      </motion.div>

                      {/* Product Information */}
                      <motion.div
                        className="mt-16 text-center space-y-4"
                        initial={{ opacity: 0, y: 40 }}
                        animate={{ 
                          opacity: absOffset === 0 ? 1 : 0.6,
                          y: 0 
                        }}
                        transition={{ duration: 0.6, delay: 0.3 }}
                      >
                        <motion.div
                          className="text-sm font-medium tracking-[0.3em] text-blue-400 mb-2"
                          style={{
                            opacity: absOffset === 0 ? 1 : 0
                          }}
                        >
                          {product.category}
                        </motion.div>
                        <h3 className="text-3xl font-light tracking-wide text-white mb-2">
                          {product.name}
                        </h3>
                        <p className="text-slate-400 font-light text-lg">
                          {product.description}
                        </p>
                        <p className="text-4xl font-light text-white mt-4">
                          {product.price}
                        </p>
                      </motion.div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            </div>

            {/* Navigation Arrows */}
            <motion.button
              onClick={() => rotateCarousel('prev')}
              className="absolute left-12 top-1/2 -translate-y-1/2 w-16 h-16 bg-slate-800/80 backdrop-blur-sm rounded-full shadow-2xl hover:bg-slate-700/80 transition-colors duration-300 flex items-center justify-center group z-20 border border-slate-600/30"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronLeft className="group-hover:-translate-x-0.5 transition-transform duration-300 text-white" size={28} />
            </motion.button>

            <motion.button
              onClick={() => rotateCarousel('next')}
              className="absolute right-12 top-1/2 -translate-y-1/2 w-16 h-16 bg-slate-800/80 backdrop-blur-sm rounded-full shadow-2xl hover:bg-slate-700/80 transition-colors duration-300 flex items-center justify-center group z-20 border border-slate-600/30"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronRight className="group-hover:translate-x-0.5 transition-transform duration-300 text-white" size={28} />
            </motion.button>

            {/* Product Dots */}
            <div className="absolute bottom-12 left-1/2 -translate-x-1/2 flex space-x-4 z-20">
              {featuredProducts.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex ? 'bg-blue-400 scale-125' : 'bg-slate-600'
                  }`}
                  whileHover={{ scale: 1.4 }}
                />
              ))}
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Enhanced Background Elements */}
      <div className="fixed inset-0 pointer-events-none">
        {/* Floating particles */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-400/20 rounded-full"
          animate={{
            y: [0, -30, 0],
            opacity: [0.2, 0.6, 0.2],
            scale: [1, 1.5, 1]
          }}
          transition={{ duration: 8, repeat: Infinity }}
        />
        <motion.div
          className="absolute top-3/4 right-1/3 w-1.5 h-1.5 bg-purple-400/20 rounded-full"
          animate={{
            y: [0, -25, 0],
            opacity: [0.1, 0.5, 0.1],
            scale: [1, 1.3, 1]
          }}
          transition={{ duration: 10, repeat: Infinity, delay: 3 }}
        />
        <motion.div
          className="absolute top-1/2 left-3/4 w-1 h-1 bg-cyan-400/20 rounded-full"
          animate={{
            y: [0, -20, 0],
            opacity: [0.15, 0.4, 0.15],
            scale: [1, 1.2, 1]
          }}
          transition={{ duration: 12, repeat: Infinity, delay: 6 }}
        />
        
        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900 via-transparent to-slate-900/50" />
      </div>
    </div>
  );
};

export default Luxury3DHeroGallery;