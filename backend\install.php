<?php

/**
 * Wolffoxx Backend Installation Script
 * 
 * Automated installation and setup script for the backend API.
 */

echo "🚀 Wolffoxx Backend Installation\n";
echo "================================\n\n";

// Check PHP version
if (version_compare(PHP_VERSION, '8.1.0', '<')) {
    echo "❌ Error: PHP 8.1 or higher is required. Current version: " . PHP_VERSION . "\n";
    exit(1);
}

echo "✅ PHP version check passed (" . PHP_VERSION . ")\n";

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'openssl', 'json', 'mbstring', 'fileinfo'];
$missingExtensions = [];

foreach ($requiredExtensions as $extension) {
    if (!extension_loaded($extension)) {
        $missingExtensions[] = $extension;
    }
}

if (!empty($missingExtensions)) {
    echo "❌ Error: Missing required PHP extensions: " . implode(', ', $missingExtensions) . "\n";
    exit(1);
}

echo "✅ PHP extensions check passed\n";

// Check if Composer is available
if (!file_exists(__DIR__ . '/vendor/autoload.php')) {
    echo "❌ Error: Composer dependencies not installed. Please run 'composer install' first.\n";
    exit(1);
}

echo "✅ Composer dependencies found\n";

// Load dependencies
require_once __DIR__ . '/vendor/autoload.php';

// Check for .env file
if (!file_exists(__DIR__ . '/.env')) {
    if (file_exists(__DIR__ . '/.env.example')) {
        echo "📋 Creating .env file from .env.example...\n";
        copy(__DIR__ . '/.env.example', __DIR__ . '/.env');
        echo "⚠️  Please edit .env file with your configuration before continuing.\n";
        echo "   Press Enter when ready to continue...\n";
        readline();
    } else {
        echo "❌ Error: .env.example file not found. Cannot create configuration.\n";
        exit(1);
    }
}

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "✅ Environment configuration loaded\n";

// Test database connection
echo "🔌 Testing database connection...\n";

try {
    use Wolffoxx\Config\Database;
    use Wolffoxx\Utils\Logger;

    Database::init();
    $connection = Database::getConnection();
    echo "✅ Database connection successful\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    echo "   Please check your database configuration in .env file.\n";
    exit(1);
}

// Create storage directories
echo "📁 Creating storage directories...\n";

$directories = [
    'storage',
    'storage/logs',
    'storage/uploads',
    'storage/uploads/profiles',
    'storage/uploads/products',
    'storage/cache'
];

foreach ($directories as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    if (!is_dir($fullPath)) {
        mkdir($fullPath, 0755, true);
        echo "   Created: {$dir}\n";
    } else {
        echo "   Exists: {$dir}\n";
    }
}

// Set permissions
echo "🔐 Setting permissions...\n";
foreach (['storage', 'storage/logs', 'storage/uploads'] as $dir) {
    $fullPath = __DIR__ . '/' . $dir;
    chmod($fullPath, 0777);
    echo "   Set 777 permissions on: {$dir}\n";
}

echo "✅ Storage directories created and configured\n";

// Run database migrations
echo "🗄️  Running database migrations...\n";

try {
    // Include migration runner
    include __DIR__ . '/scripts/migrate.php';
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test email configuration (optional)
echo "📧 Testing email configuration...\n";

try {
    use Wolffoxx\Services\EmailService;
    
    $emailService = new EmailService();
    if ($emailService->testConnection()) {
        echo "✅ Email configuration test passed\n";
    } else {
        echo "⚠️  Email configuration test failed (this is optional)\n";
    }
} catch (Exception $e) {
    echo "⚠️  Email test failed: " . $e->getMessage() . " (this is optional)\n";
}

// Generate JWT secret if not set
if (empty($_ENV['JWT_SECRET']) || $_ENV['JWT_SECRET'] === 'your-super-secret-jwt-key-change-this-in-production') {
    echo "🔑 Generating JWT secret...\n";
    
    $jwtSecret = bin2hex(random_bytes(32));
    
    // Update .env file
    $envContent = file_get_contents(__DIR__ . '/.env');
    $envContent = preg_replace(
        '/JWT_SECRET=.*/',
        'JWT_SECRET=' . $jwtSecret,
        $envContent
    );
    file_put_contents(__DIR__ . '/.env', $envContent);
    
    echo "✅ JWT secret generated and saved\n";
}

// Create admin user (optional)
echo "\n👤 Create admin user? (y/n): ";
$createAdmin = trim(fgets(STDIN));

if (strtolower($createAdmin) === 'y' || strtolower($createAdmin) === 'yes') {
    echo "Creating admin user...\n";
    
    echo "Email: ";
    $email = trim(fgets(STDIN));
    
    echo "Password: ";
    $password = trim(fgets(STDIN));
    
    echo "First Name: ";
    $firstName = trim(fgets(STDIN));
    
    echo "Last Name: ";
    $lastName = trim(fgets(STDIN));
    
    try {
        use Wolffoxx\Models\User;
        
        $userModel = new User();
        
        // Check if email already exists
        if ($userModel->emailExists($email)) {
            echo "❌ Error: Email already exists\n";
        } else {
            $adminUser = $userModel->createUser([
                'email' => $email,
                'password' => $password,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'is_admin' => true,
                'is_active' => true
            ]);
            
            if ($adminUser) {
                echo "✅ Admin user created successfully\n";
            } else {
                echo "❌ Failed to create admin user\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ Error creating admin user: " . $e->getMessage() . "\n";
    }
}

// Installation complete
echo "\n🎉 Installation completed successfully!\n\n";

echo "📋 Next steps:\n";
echo "1. Configure your web server to point to this directory\n";
echo "2. Ensure URL rewriting is enabled (.htaccess for Apache)\n";
echo "3. Test the API health endpoint: GET /api/v1/health\n";
echo "4. Review the API documentation in README.md\n";
echo "5. Configure CORS origins in .env for production\n\n";

echo "🔗 API Base URL: " . ($_ENV['APP_URL'] ?? 'http://localhost:8000') . "/api/v1/\n";
echo "📖 Documentation: README.md\n";
echo "📊 Health Check: GET /api/v1/health\n\n";

echo "Happy coding! 🚀\n";
