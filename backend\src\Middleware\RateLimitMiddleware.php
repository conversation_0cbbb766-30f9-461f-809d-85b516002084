<?php

namespace Wolffoxx\Middleware;

use Wolffoxx\Utils\Logger;
use Wolffoxx\Utils\Response;

/**
 * Rate Limiting Middleware
 * 
 * Implements rate limiting to prevent abuse and ensure
 * fair usage of the API.
 */
class RateLimitMiddleware implements MiddlewareInterface
{
    private Logger $logger;
    private string $storageFile;
    private int $maxRequests;
    private int $windowSeconds;

    public function __construct()
    {
        $this->logger = new Logger('rate_limit');
        $this->storageFile = __DIR__ . '/../../storage/rate_limits.json';
        $this->maxRequests = (int)($_ENV['RATE_LIMIT_REQUESTS'] ?? 100);
        $this->windowSeconds = (int)($_ENV['RATE_LIMIT_WINDOW'] ?? 3600);

        // Ensure storage directory exists
        $storageDir = dirname($this->storageFile);
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }
    }

    /**
     * Handle rate limiting
     */
    public function handle(array $params = []): bool
    {
        $clientId = $this->getClientIdentifier();
        $currentTime = time();

        $this->logger->debug('Rate limit check', [
            'client_id' => $clientId,
            'max_requests' => $this->maxRequests,
            'window_seconds' => $this->windowSeconds
        ]);

        // Load existing rate limit data
        $rateLimits = $this->loadRateLimits();

        // Clean up expired entries
        $this->cleanupExpiredEntries($rateLimits, $currentTime);

        // Get or create client entry
        if (!isset($rateLimits[$clientId])) {
            $rateLimits[$clientId] = [
                'requests' => [],
                'blocked_until' => null
            ];
        }

        $clientData = &$rateLimits[$clientId];

        // Check if client is currently blocked
        if ($clientData['blocked_until'] && $currentTime < $clientData['blocked_until']) {
            $remainingTime = $clientData['blocked_until'] - $currentTime;
            
            $this->logger->warning('Client is rate limited', [
                'client_id' => $clientId,
                'remaining_time' => $remainingTime
            ]);

            Response::rateLimitExceeded('Rate limit exceeded. Try again later.', [
                'Retry-After' => $remainingTime,
                'X-RateLimit-Limit' => $this->maxRequests,
                'X-RateLimit-Remaining' => 0,
                'X-RateLimit-Reset' => $clientData['blocked_until']
            ]);
            
            return false;
        }

        // Remove requests outside the current window
        $windowStart = $currentTime - $this->windowSeconds;
        $clientData['requests'] = array_filter(
            $clientData['requests'],
            fn($timestamp) => $timestamp > $windowStart
        );

        // Check if limit is exceeded
        $requestCount = count($clientData['requests']);
        
        if ($requestCount >= $this->maxRequests) {
            // Block client for the remaining window time
            $oldestRequest = min($clientData['requests']);
            $clientData['blocked_until'] = $oldestRequest + $this->windowSeconds;
            
            $this->saveRateLimits($rateLimits);

            $this->logger->warning('Rate limit exceeded', [
                'client_id' => $clientId,
                'request_count' => $requestCount,
                'blocked_until' => $clientData['blocked_until']
            ]);

            Response::rateLimitExceeded('Rate limit exceeded', [
                'Retry-After' => $this->windowSeconds,
                'X-RateLimit-Limit' => $this->maxRequests,
                'X-RateLimit-Remaining' => 0,
                'X-RateLimit-Reset' => $clientData['blocked_until']
            ]);
            
            return false;
        }

        // Add current request
        $clientData['requests'][] = $currentTime;
        $clientData['blocked_until'] = null;

        // Save updated data
        $this->saveRateLimits($rateLimits);

        // Add rate limit headers
        $remaining = $this->maxRequests - count($clientData['requests']);
        $resetTime = $windowStart + $this->windowSeconds;

        header("X-RateLimit-Limit: {$this->maxRequests}");
        header("X-RateLimit-Remaining: {$remaining}");
        header("X-RateLimit-Reset: {$resetTime}");

        $this->logger->debug('Rate limit passed', [
            'client_id' => $clientId,
            'request_count' => count($clientData['requests']),
            'remaining' => $remaining
        ]);

        return true;
    }

    /**
     * Get client identifier for rate limiting
     */
    private function getClientIdentifier(): string
    {
        // Try to get authenticated user ID first
        $userId = $GLOBALS['current_user_id'] ?? null;
        if ($userId) {
            return "user_{$userId}";
        }

        // Fall back to IP address
        $ipAddress = $this->getClientIpAddress();
        return "ip_{$ipAddress}";
    }

    /**
     * Get client IP address
     */
    private function getClientIpAddress(): string
    {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }

    /**
     * Load rate limit data from storage
     */
    private function loadRateLimits(): array
    {
        if (!file_exists($this->storageFile)) {
            return [];
        }

        $content = file_get_contents($this->storageFile);
        if ($content === false) {
            $this->logger->error('Failed to read rate limit file');
            return [];
        }

        $data = json_decode($content, true);
        if ($data === null) {
            $this->logger->error('Failed to decode rate limit data');
            return [];
        }

        return $data;
    }

    /**
     * Save rate limit data to storage
     */
    private function saveRateLimits(array $rateLimits): void
    {
        $content = json_encode($rateLimits, JSON_PRETTY_PRINT);
        
        if (file_put_contents($this->storageFile, $content, LOCK_EX) === false) {
            $this->logger->error('Failed to save rate limit data');
        }
    }

    /**
     * Clean up expired entries to prevent storage bloat
     */
    private function cleanupExpiredEntries(array &$rateLimits, int $currentTime): void
    {
        $windowStart = $currentTime - $this->windowSeconds;
        $cleanupThreshold = $currentTime - ($this->windowSeconds * 2); // Keep data for 2 windows

        foreach ($rateLimits as $clientId => $clientData) {
            // Remove very old entries completely
            if (empty($clientData['requests']) && 
                (!$clientData['blocked_until'] || $clientData['blocked_until'] < $cleanupThreshold)) {
                unset($rateLimits[$clientId]);
                continue;
            }

            // Clean up old requests
            if (!empty($clientData['requests'])) {
                $rateLimits[$clientId]['requests'] = array_filter(
                    $clientData['requests'],
                    fn($timestamp) => $timestamp > $windowStart
                );
            }

            // Clear expired blocks
            if ($clientData['blocked_until'] && $currentTime >= $clientData['blocked_until']) {
                $rateLimits[$clientId]['blocked_until'] = null;
            }
        }
    }

    /**
     * Get rate limit status for a client
     */
    public function getRateLimitStatus(string $clientId = null): array
    {
        $clientId = $clientId ?: $this->getClientIdentifier();
        $currentTime = time();
        $rateLimits = $this->loadRateLimits();

        if (!isset($rateLimits[$clientId])) {
            return [
                'limit' => $this->maxRequests,
                'remaining' => $this->maxRequests,
                'reset_time' => $currentTime + $this->windowSeconds,
                'blocked' => false
            ];
        }

        $clientData = $rateLimits[$clientId];
        $windowStart = $currentTime - $this->windowSeconds;
        
        // Filter recent requests
        $recentRequests = array_filter(
            $clientData['requests'],
            fn($timestamp) => $timestamp > $windowStart
        );

        $requestCount = count($recentRequests);
        $remaining = max(0, $this->maxRequests - $requestCount);
        $blocked = $clientData['blocked_until'] && $currentTime < $clientData['blocked_until'];

        return [
            'limit' => $this->maxRequests,
            'remaining' => $remaining,
            'reset_time' => $windowStart + $this->windowSeconds,
            'blocked' => $blocked,
            'blocked_until' => $clientData['blocked_until']
        ];
    }
}
