<?php

/**
 * Import Products from products.js to Database
 * 
 * Quick script to populate database with frontend products.js data
 */

// Load environment variables
$envFile = __DIR__ . '/../.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value, " \t\n\r\0\x0B\"'");
            $_ENV[$key] = $value;
            putenv("$key=$value");
        }
    }
}

require_once __DIR__ . '/../config/database.php';

use Wolffoxx\Config\Database;

// Initialize database
Database::init();

echo "🚀 Importing products from products.js to database...\n";

try {
    // Clear existing products
    echo "🧹 Clearing existing products...\n";
    Database::execute("DELETE FROM product_images");
    Database::execute("DELETE FROM product_colors");
    Database::execute("DELETE FROM product_sizes");
    Database::execute("DELETE FROM products");
    Database::execute("ALTER TABLE products AUTO_INCREMENT = 1");

    // Read products.js file
    $productsFile = __DIR__ . '/../../src/data/products.js';
    if (!file_exists($productsFile)) {
        throw new Exception("products.js file not found at: $productsFile");
    }

    $content = file_get_contents($productsFile);
    
    // Extract the products array using regex
    preg_match('/const products = (\[.*?\]);/s', $content, $matches);
    if (!$matches) {
        throw new Exception("Could not extract products array from products.js");
    }

    // Convert JS array to PHP (simple approach)
    $jsArray = $matches[1];
    
    // Replace JS syntax with PHP syntax
    $jsArray = preg_replace('/(\w+):/m', '"$1":', $jsArray); // Convert object keys
    $jsArray = str_replace("'", '"', $jsArray); // Convert single quotes to double
    $jsArray = preg_replace('/,(\s*[}\]])/', '$1', $jsArray); // Remove trailing commas
    
    $products = json_decode($jsArray, true);
    
    if (!$products) {
        throw new Exception("Failed to parse products array. JSON error: " . json_last_error_msg());
    }

    echo "📦 Found " . count($products) . " products to import...\n";

    $imported = 0;
    foreach ($products as $product) {
        try {
            // Generate UUID and SKU
            $uuid = sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
                mt_rand(0, 0xffff), mt_rand(0, 0xffff),
                mt_rand(0, 0xffff),
                mt_rand(0, 0x0fff) | 0x4000,
                mt_rand(0, 0x3fff) | 0x8000,
                mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
            );
            
            $sku = strtoupper(substr($product['category'], 0, 2)) . str_pad($product['id'], 4, '0', STR_PAD_LEFT);
            $slug = strtolower(preg_replace('/[^A-Za-z0-9-]+/', '-', $product['name']));

            // Insert product
            $sql = "INSERT INTO products (
                uuid, name, slug, description, price, sale_price, sku, category,
                material, fit, stock_quantity, is_active, is_new, is_bestseller,
                is_trending, average_rating, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, ?, ?, ?, ?, NOW(), NOW())";

            $params = [
                $uuid,
                $product['name'],
                $slug,
                $product['description'],
                $product['price'],
                null, // sale_price
                $sku,
                $product['category'],
                $product['material'] ?? '100% Cotton',
                $product['fit'] ?? 'Regular',
                50, // stock_quantity
                $product['isNew'] ?? false,
                $product['isBestSeller'] ?? false,
                $product['isTrending'] ?? false,
                $product['rating'] ?? 4.5
            ];

            Database::execute($sql, $params);
            $productId = Database::lastInsertId();

            // Insert product images
            if (isset($product['images']) && is_array($product['images'])) {
                foreach ($product['images'] as $index => $imageUrl) {
                    $imageSql = "INSERT INTO product_images (product_id, image_url, alt_text, sort_order, is_primary, created_at) 
                                VALUES (?, ?, ?, ?, ?, NOW())";
                    Database::execute($imageSql, [
                        $productId,
                        $imageUrl,
                        $product['name'] . ' - Image ' . ($index + 1),
                        $index,
                        $index === 0 ? 1 : 0
                    ]);
                }
            }

            // Insert product colors
            if (isset($product['colors']) && is_array($product['colors'])) {
                foreach ($product['colors'] as $color) {
                    $colorSql = "INSERT INTO product_colors (product_id, color_name, color_value, created_at) 
                                VALUES (?, ?, ?, NOW())";
                    Database::execute($colorSql, [
                        $productId,
                        $color['name'],
                        $color['value']
                    ]);
                }
            }

            // Insert product sizes
            if (isset($product['sizes']) && is_array($product['sizes'])) {
                foreach ($product['sizes'] as $size) {
                    $sizeSql = "INSERT INTO product_sizes (product_id, size_name, created_at) 
                               VALUES (?, ?, NOW())";
                    Database::execute($sizeSql, [$productId, $size]);
                }
            }

            $imported++;
            echo "✅ Imported: {$product['name']} (ID: $productId)\n";

        } catch (Exception $e) {
            echo "❌ Failed to import {$product['name']}: " . $e->getMessage() . "\n";
        }
    }

    echo "\n🎉 Import complete! Imported $imported products.\n";

    // Show statistics
    $stats = [
        'Products' => Database::execute("SELECT COUNT(*) as count FROM products")->fetch()['count'],
        'Images' => Database::execute("SELECT COUNT(*) as count FROM product_images")->fetch()['count'],
        'Colors' => Database::execute("SELECT COUNT(*) as count FROM product_colors")->fetch()['count'],
        'Sizes' => Database::execute("SELECT COUNT(*) as count FROM product_sizes")->fetch()['count']
    ];

    echo "\n📊 DATABASE STATISTICS:\n";
    foreach ($stats as $table => $count) {
        echo "   $table: $count\n";
    }

} catch (Exception $e) {
    echo "❌ Import failed: " . $e->getMessage() . "\n";
    exit(1);
}
