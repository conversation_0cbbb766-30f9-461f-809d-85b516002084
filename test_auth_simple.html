<!DOCTYPE html>
<html>
<head>
    <title>Simple Auth Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        button { margin: 5px; padding: 10px 15px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>Simple Authentication Test</h1>
    
    <div>
        <h3>Step 1: Login</h3>
        <button onclick="login()">Login with Test User</button>
        <div id="login-result" class="result"></div>
    </div>

    <div>
        <h3>Step 2: Test Order Creation</h3>
        <button onclick="testOrder()">Create Test Order</button>
        <div id="order-result" class="result"></div>
    </div>

    <div>
        <h3>Step 3: Check Token</h3>
        <button onclick="checkToken()">Check Current Token</button>
        <div id="token-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        async function login() {
            try {
                // Step 1: Send OTP
                console.log('Sending OTP...');
                const otpResponse = await fetch(`${API_BASE}/auth/otp/send`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ phone: '9876543210' })
                });
                
                const otpData = await otpResponse.json();
                console.log('OTP Response:', otpData);

                if (!otpResponse.ok) {
                    displayResult('login-result', { error: 'Failed to send OTP', details: otpData }, true);
                    return;
                }

                // Step 2: Verify OTP (using default OTP 123456)
                console.log('Verifying OTP...');
                const verifyResponse = await fetch(`${API_BASE}/auth/otp/verify`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        phone: '9876543210', 
                        otp: '123456',
                        user_details: {
                            first_name: 'Test',
                            last_name: 'User',
                            email: '<EMAIL>'
                        }
                    })
                });

                const verifyData = await verifyResponse.json();
                console.log('Verify Response:', verifyData);

                if (verifyResponse.ok && verifyData.tokens) {
                    // Store tokens
                    localStorage.setItem('wolffoxx_tokens', JSON.stringify(verifyData.tokens));
                    localStorage.setItem('wolffoxx_user', JSON.stringify(verifyData.user));
                    
                    displayResult('login-result', {
                        message: 'Login successful!',
                        user: verifyData.user,
                        tokens: {
                            access_token: verifyData.tokens.access_token ? verifyData.tokens.access_token.substring(0, 50) + '...' : 'none',
                            refresh_token: verifyData.tokens.refresh_token ? verifyData.tokens.refresh_token.substring(0, 50) + '...' : 'none'
                        }
                    }, false);
                } else {
                    displayResult('login-result', { error: 'Login failed', details: verifyData }, true);
                }
            } catch (error) {
                displayResult('login-result', { error: error.message }, true);
            }
        }

        async function testOrder() {
            try {
                const tokens = localStorage.getItem('wolffoxx_tokens');
                if (!tokens) {
                    displayResult('order-result', { error: 'Please login first' }, true);
                    return;
                }

                const parsedTokens = JSON.parse(tokens);
                const token = parsedTokens.access_token || parsedTokens.accessToken;

                if (!token) {
                    displayResult('order-result', { error: 'No access token found' }, true);
                    return;
                }

                console.log('Using token:', token.substring(0, 50) + '...');

                const orderData = {
                    customer_name: 'Test Customer',
                    shipping_address: {
                        street: '123 Test Street',
                        city: 'Test City',
                        state: 'Test State',
                        zip: '12345',
                        country: 'India'
                    },
                    billing_address: {
                        street: '123 Test Street',
                        city: 'Test City',
                        state: 'Test State',
                        zip: '12345',
                        country: 'India'
                    },
                    payment_method: 'test_payment',
                    shipping_amount: 0,
                    order_notes: 'Test order'
                };

                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(orderData)
                });

                const data = await response.json();
                console.log('Order Response:', data);

                displayResult('order-result', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, !response.ok);
            } catch (error) {
                displayResult('order-result', { error: error.message }, true);
            }
        }

        function checkToken() {
            try {
                const tokens = localStorage.getItem('wolffoxx_tokens');
                const user = localStorage.getItem('wolffoxx_user');

                if (!tokens) {
                    displayResult('token-result', { error: 'No tokens found in localStorage' }, true);
                    return;
                }

                const parsedTokens = JSON.parse(tokens);
                const parsedUser = user ? JSON.parse(user) : null;

                displayResult('token-result', {
                    tokens: {
                        access_token: parsedTokens.access_token ? parsedTokens.access_token.substring(0, 50) + '...' : 'none',
                        refresh_token: parsedTokens.refresh_token ? parsedTokens.refresh_token.substring(0, 50) + '...' : 'none',
                        accessToken: parsedTokens.accessToken ? parsedTokens.accessToken.substring(0, 50) + '...' : 'none',
                        refreshToken: parsedTokens.refreshToken ? parsedTokens.refreshToken.substring(0, 50) + '...' : 'none'
                    },
                    user: parsedUser
                }, false);
            } catch (error) {
                displayResult('token-result', { error: error.message }, true);
            }
        }

        // Auto-check token on load
        window.onload = function() {
            checkToken();
        };
    </script>
</body>
</html>
