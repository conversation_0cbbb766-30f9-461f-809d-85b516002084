import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const categories = [
  {
    id: 'oversized-tees',
    name: 'Oversized Te<PERSON>',
    image: 'https://images.unsplash.com/photo-1562572159-4efc207f5aff?q=80&w=700&auto=format&fit=crop',
    description: 'Iconic relaxed-fit tees with dropped shoulders'
  },
  {
    id: 't-shirts',
    name: 'T-Shirts',
    image: 'https://images.unsplash.com/photo-1527628217451-b2414a1ee733?q=80&w=700&auto=format&fit=crop',
    description: 'Premium quality everyday essentials'
  },
  {
    id: 'hoodies',
    name: 'Hoodies',
    image: 'https://images.unsplash.com/photo-1512514076443-1eef59c260b0?q=80&w=700&auto=format&fit=crop',
    description: 'Cozy and stylish statement outerwear'
  },
  {
    id: 'shirts',
    name: 'Plain Drops',
    image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?q=80&w=700&auto=format&fit=crop ',
    description: 'Premium basics with perfect fit'
  }
];

export default function CategoryTags() {
  return (
    <section className="py-16 bg-gray-900">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-3">SHOP BY STYLE</h2>
          <p className="text-gray-400 max-w-2xl mx-auto">Find your perfect fit with our collection of premium oversized tees</p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -10 }}
              className="group relative rounded-lg overflow-hidden h-80"
            >
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900 to-transparent z-10" />
              
              <img 
                src={category.image} 
                alt={category.name} 
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              
              <div className="absolute inset-0 flex flex-col justify-end p-6 z-20">
                <h3 className="text-2xl font-['Bebas_Neue',sans-serif] text-white mb-2">{category.name}</h3>
                <p className="text-gray-300 mb-4 text-sm">{category.description}</p>
                <Link 
                  to={`/category/${category.id}`}
                  className="inline-block bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white text-center py-2 px-4 rounded-md transition-colors w-full"
                >
                  Shop Now
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}