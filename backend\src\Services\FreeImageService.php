<?php

namespace Wolffoxx\Services;

use Wolffoxx\Utils\Logger;

/**
 * Free Image Service
 * 
 * Handles image storage using free services like GitHub,
 * Cloudinary free tier, and server storage with free CDN.
 */
class FreeImageService
{
    private Logger $logger;
    private string $githubRepo;
    private string $githubToken;
    private string $cloudinaryUrl;
    private string $localUploadPath;

    public function __construct()
    {
        $this->logger = new Logger('free_image');
        
        // GitHub configuration (for product images)
        $this->githubRepo = $_ENV['GITHUB_IMAGES_REPO'] ?? 'username/wolffoxx-images';
        $this->githubToken = $_ENV['GITHUB_TOKEN'] ?? '';
        
        // Cloudinary configuration (free tier)
        $this->cloudinaryUrl = $_ENV['CLOUDINARY_URL'] ?? '';
        
        // Local storage (for user uploads)
        $this->localUploadPath = __DIR__ . '/../../storage/uploads/';
        
        $this->createDirectories();
    }

    /**
     * Upload product image to GitHub (FREE)
     */
    public function uploadProductImageToGitHub(array $file, int $productId, string $category = 'general'): array
    {
        try {
            // Validate file
            if (!$this->validateImage($file)) {
                throw new \Exception('Invalid image file');
            }

            // Generate filename
            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = strtolower($category) . '_' . $productId . '_' . time() . '.' . $extension;
            $githubPath = 'products/' . strtolower($category) . '/' . $filename;

            // Read file content
            $imageContent = file_get_contents($file['tmp_name']);
            $base64Content = base64_encode($imageContent);

            // GitHub API URL
            $apiUrl = "https://api.github.com/repos/{$this->githubRepo}/contents/{$githubPath}";

            // Prepare GitHub API request
            $data = [
                'message' => "Add product image: {$filename}",
                'content' => $base64Content,
                'branch' => 'main'
            ];

            $headers = [
                'Authorization: token ' . $this->githubToken,
                'User-Agent: Wolffoxx-Backend',
                'Content-Type: application/json'
            ];

            // Make API request
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PUT');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 201) {
                throw new \Exception('GitHub upload failed: ' . $response);
            }

            // Generate public URLs
            $baseUrl = "https://raw.githubusercontent.com/{$this->githubRepo}/main/{$githubPath}";
            $cdnUrl = "https://cdn.jsdelivr.net/gh/{$this->githubRepo}@main/{$githubPath}";

            $this->logger->info('Product image uploaded to GitHub', [
                'product_id' => $productId,
                'filename' => $filename,
                'github_path' => $githubPath
            ]);

            return [
                'success' => true,
                'urls' => [
                    'original' => $baseUrl,
                    'cdn' => $cdnUrl, // jsDelivr CDN (FREE)
                ],
                'metadata' => [
                    'filename' => $filename,
                    'path' => $githubPath,
                    'size' => filesize($file['tmp_name']),
                    'type' => $extension
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('GitHub image upload failed', [
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Upload to Cloudinary (FREE tier)
     */
    public function uploadToCloudinary(array $file, string $folder = 'products'): array
    {
        try {
            if (empty($this->cloudinaryUrl)) {
                throw new \Exception('Cloudinary not configured');
            }

            // Parse Cloudinary URL
            $cloudinaryConfig = parse_url($this->cloudinaryUrl);
            $cloudName = ltrim($cloudinaryConfig['path'], '/');
            $apiKey = $cloudinaryConfig['user'];
            $apiSecret = $cloudinaryConfig['pass'];

            // Generate upload parameters
            $timestamp = time();
            $publicId = $folder . '/' . pathinfo($file['name'], PATHINFO_FILENAME) . '_' . $timestamp;
            
            $params = [
                'timestamp' => $timestamp,
                'public_id' => $publicId,
                'folder' => $folder
            ];

            // Generate signature
            $signature = $this->generateCloudinarySignature($params, $apiSecret);
            $params['signature'] = $signature;
            $params['api_key'] = $apiKey;

            // Prepare file upload
            $params['file'] = new \CURLFile($file['tmp_name'], $file['type'], $file['name']);

            // Upload to Cloudinary
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://api.cloudinary.com/v1_1/{$cloudName}/image/upload");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $params);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('Cloudinary upload failed: ' . $response);
            }

            $result = json_decode($response, true);

            $this->logger->info('Image uploaded to Cloudinary', [
                'public_id' => $result['public_id'],
                'url' => $result['secure_url']
            ]);

            return [
                'success' => true,
                'urls' => [
                    'original' => $result['secure_url'],
                    'thumbnail' => str_replace('/upload/', '/upload/w_300,h_300,c_fill/', $result['secure_url']),
                    'medium' => str_replace('/upload/', '/upload/w_600,h_600,c_fill/', $result['secure_url']),
                    'large' => str_replace('/upload/', '/upload/w_1200,h_1200,c_fill/', $result['secure_url'])
                ],
                'metadata' => [
                    'public_id' => $result['public_id'],
                    'format' => $result['format'],
                    'width' => $result['width'],
                    'height' => $result['height'],
                    'bytes' => $result['bytes']
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('Cloudinary upload failed', [
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Upload to local server with Cloudflare CDN
     */
    public function uploadToLocal(array $file, string $type = 'products'): array
    {
        try {
            if (!$this->validateImage($file)) {
                throw new \Exception('Invalid image file');
            }

            $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $filename = $type . '_' . time() . '_' . uniqid() . '.' . $extension;
            $directory = $this->localUploadPath . $type . '/';
            $filepath = $directory . $filename;

            // Ensure directory exists
            if (!is_dir($directory)) {
                mkdir($directory, 0755, true);
            }

            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new \Exception('Failed to save file');
            }

            // Generate URLs
            $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost:8000';
            $imageUrl = $baseUrl . '/storage/uploads/' . $type . '/' . $filename;

            $this->logger->info('Image uploaded to local storage', [
                'filename' => $filename,
                'type' => $type,
                'size' => filesize($filepath)
            ]);

            return [
                'success' => true,
                'urls' => [
                    'original' => $imageUrl
                ],
                'metadata' => [
                    'filename' => $filename,
                    'path' => '/storage/uploads/' . $type . '/' . $filename,
                    'size' => filesize($filepath),
                    'type' => $extension
                ]
            ];

        } catch (\Exception $e) {
            $this->logger->error('Local upload failed', [
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Smart upload - chooses best free option
     */
    public function smartUpload(array $file, string $type = 'products', int $productId = null): array
    {
        // For product images, try GitHub first (completely free)
        if ($type === 'products' && !empty($this->githubToken)) {
            $result = $this->uploadProductImageToGitHub($file, $productId ?? time());
            if ($result['success']) {
                return $result;
            }
        }

        // Try Cloudinary free tier
        if (!empty($this->cloudinaryUrl)) {
            $result = $this->uploadToCloudinary($file, $type);
            if ($result['success']) {
                return $result;
            }
        }

        // Fallback to local storage
        return $this->uploadToLocal($file, $type);
    }

    /**
     * Validate image file
     */
    private function validateImage(array $file): bool
    {
        // Check upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        // Check file size (max 5MB)
        if ($file['size'] > 5242880) {
            return false;
        }

        // Check file type
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedTypes)) {
            return false;
        }

        // Verify it's actually an image
        $imageInfo = getimagesize($file['tmp_name']);
        return $imageInfo !== false;
    }

    /**
     * Generate Cloudinary signature
     */
    private function generateCloudinarySignature(array $params, string $apiSecret): string
    {
        ksort($params);
        $stringToSign = '';
        
        foreach ($params as $key => $value) {
            if ($key !== 'file') {
                $stringToSign .= $key . '=' . $value . '&';
            }
        }
        
        $stringToSign = rtrim($stringToSign, '&') . $apiSecret;
        return sha1($stringToSign);
    }

    /**
     * Create necessary directories
     */
    private function createDirectories(): void
    {
        $directories = ['products', 'profiles', 'temp'];
        
        foreach ($directories as $dir) {
            $fullPath = $this->localUploadPath . $dir;
            if (!is_dir($fullPath)) {
                mkdir($fullPath, 0755, true);
            }
        }
    }

    /**
     * Get storage statistics
     */
    public function getStorageStats(): array
    {
        return [
            'github_configured' => !empty($this->githubToken),
            'cloudinary_configured' => !empty($this->cloudinaryUrl),
            'local_storage_available' => is_writable($this->localUploadPath),
            'recommended_setup' => $this->getRecommendedSetup()
        ];
    }

    /**
     * Get recommended setup for free storage
     */
    private function getRecommendedSetup(): string
    {
        if (!empty($this->githubToken)) {
            return 'GitHub (Best - Completely Free)';
        } elseif (!empty($this->cloudinaryUrl)) {
            return 'Cloudinary Free Tier (Good - 25GB Free)';
        } else {
            return 'Local Storage + Cloudflare CDN (Basic - Server Storage)';
        }
    }
}
