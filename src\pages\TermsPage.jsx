import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { ArrowLeft, Shield, Package, RefreshCw, CreditCard, Truck, Scale } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function TermsPage() {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  const sections = [
    {
      id: 'acceptance',
      title: 'Acceptance of Terms',
      icon: Scale,
      content: `By accessing and using the WOLFFOXX website and services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.`
    },
    {
      id: 'products',
      title: 'Products & Availability',
      icon: Package,
      content: `All products are subject to availability. We reserve the right to discontinue any product at any time. Product colors may vary slightly from those shown on your monitor due to display settings. All measurements are approximate and may vary by up to 2cm. We make every effort to display product information accurately, but we cannot guarantee that your monitor's display of colors accurately reflects the actual product colors.`
    },
    {
      id: 'orders',
      title: 'Orders & Payment',
      icon: CreditCard,
      content: `By placing an order, you are making an offer to purchase products subject to these terms. All orders are subject to acceptance and availability. We reserve the right to refuse or cancel any order for any reason. Payment must be received in full before products are shipped. We accept major credit cards, PayPal, and other payment methods as displayed at checkout.`
    },
    {
      id: 'shipping',
      title: 'Shipping & Delivery',
      icon: Truck,
      content: `Shipping costs and delivery times vary by location and shipping method selected. Standard shipping typically takes 3-7 business days. Express shipping options are available at checkout. We are not responsible for delays caused by shipping carriers or customs processing. Risk of loss and title for products pass to you upon delivery to the shipping carrier.`
    },
    {
      id: 'returns',
      title: 'Returns & Exchanges',
      icon: RefreshCw,
      content: `We accept returns within 30 days of purchase for unworn, unwashed items with original tags attached. Items must be in original condition. Return shipping costs are the responsibility of the customer unless the item was defective or incorrect. Refunds will be processed within 5-10 business days after we receive your return. Sale items and final sale items are not eligible for return.`
    },
    {
      id: 'privacy',
      title: 'Privacy & Data Protection',
      icon: Shield,
      content: `Your privacy is important to us. We collect and use personal information in accordance with our Privacy Policy. By using our services, you consent to the collection and use of information as outlined in our Privacy Policy. We implement appropriate security measures to protect your personal information.`
    }
  ];

  return (
    <div ref={containerRef} className="min-h-screen bg-black text-[#d4d4d4]">
      {/* Back to Home Button - Top Left - TRULY FIXED */}
      {/*<div*/}
      {/*  style={{*/}
      {/*    position: 'fixed',*/}
      {/*    top: '80px',*/}
      {/*    left: '16px',*/}
      {/*    zIndex: 99999,*/}
      {/*    transform: 'translate3d(0, 0, 0)'*/}
      {/*  }}*/}
      {/*>*/}
      {/*  <Link*/}
      {/*    to="/"*/}
      {/*    className="inline-flex items-center gap-2 text-gray-300 hover:text-white transition-colors duration-300 group"*/}
      {/*  >*/}
      {/*    <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform" />*/}
      {/*    <span className="hidden sm:inline">Back to Home</span>*/}
      {/*  </Link>*/}
      {/*</div>*/}
        {/* Hero Section */}
        <motion.div
        className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800"
        style={{ y }}
      >
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.02%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6">
              TERMS & CONDITIONS
            </h1>
            <p className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl mx-auto">
              Please read these terms and conditions carefully before using our services.
            </p>
            <div className="mt-8 text-sm text-gray-400">
              Last updated: {new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Content Sections */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20">
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-12 p-6 sm:p-8 bg-[#1a1a1a] backdrop-blur-sm rounded-2xl border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">Welcome to WOLFFOXX</h2>
            <p className="text-slate-300 leading-relaxed">
              These Terms and Conditions ("Terms") govern your use of the WOLFFOXX website and services.
              WOLFFOXX is a premium streetwear brand specializing in oversized clothing and urban fashion.
              By accessing our website or making a purchase, you agree to comply with these terms.
            </p>
          </motion.div>

          {/* Terms Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <motion.div
                key={section.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 hover:border-orange-500/30 transition-colors shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-orange-500/10 rounded-lg flex items-center justify-center hover:bg-orange-500/20 transition-colors group" style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}>
                    <section.icon size={24} className="text-orange-400 group-hover:text-orange-300 transition-colors" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-semibold text-white">
                    {section.title}
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed pl-16">
                  {section.content}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Additional Legal Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-16 space-y-8"
          >
            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Limitation of Liability</h3>
              <p className="text-gray-300 leading-relaxed">
                WOLFFOXX shall not be liable for any indirect, incidental, special, consequential, or punitive damages,
                including without limitation, loss of profits, data, use, goodwill, or other intangible losses,
                resulting from your use of our services.
              </p>
            </div>

            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Governing Law</h3>
              <p className="text-gray-300 leading-relaxed">
                These Terms shall be interpreted and governed by the laws of the jurisdiction in which WOLFFOXX operates,
                without regard to its conflict of law provisions. Any disputes arising from these terms will be resolved
                through binding arbitration.
              </p>
            </div>

            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Changes to Terms</h3>
              <p className="text-gray-300 leading-relaxed">
                We reserve the right to modify these Terms at any time. Changes will be effective immediately upon posting
                on our website. Your continued use of our services after any changes constitutes acceptance of the new Terms.
              </p>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="mt-16 text-center p-8 bg-[#1a1a1a] backdrop-blur-sm rounded-2xl border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <h3 className="text-2xl font-semibold text-white mb-4">Questions About These Terms?</h3>
            <p className="text-gray-300 mb-6">
              If you have any questions about these Terms and Conditions, please contact us.
            </p>
            <Link
              to="/about"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25"
            >
              Contact Us
              <ArrowLeft size={18} className="rotate-180" />
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
