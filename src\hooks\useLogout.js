import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';

/**
 * Custom hook for handling complete logout process
 * Clears localStorage and lets contexts handle data clearing automatically
 */
export const useLogout = () => {
  const { logout } = useAuth();
  const navigate = useNavigate();

  const handleCompleteLogout = async (redirectTo = '/') => {
    try {
      // Clear localStorage first to prevent data leakage
      localStorage.removeItem('cart');
      localStorage.removeItem('wishlist');
      localStorage.removeItem('wolffoxx-outfits');
      localStorage.removeItem('savedOutfits');
      localStorage.removeItem('currentOutfit');

      // Then logout from auth (this also clears auth localStorage)
      await logout();

      // Navigate to specified route
      navigate(redirectTo);
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local data and navigate
      localStorage.removeItem('cart');
      localStorage.removeItem('wishlist');
      localStorage.removeItem('wolffoxx-outfits');
      localStorage.removeItem('savedOutfits');
      localStorage.removeItem('currentOutfit');
      navigate(redirectTo);
    }
  };

  return { handleCompleteLogout };
};
