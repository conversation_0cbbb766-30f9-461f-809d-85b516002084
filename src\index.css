/* Text Utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Product Card Animations */
.product-card img {
  transition: transform 0.5s ease;
}

.product-card:hover img {
  transform: scale(1.05);
}

.product-card .arrow-button {
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
}

.product-card:hover .arrow-button {
  opacity: 1;
  transform: translateY(0);
}

.color-dot {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.color-dot.active {
  transform: scale(1.25);
  border-color: #000;
}

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html.dark {
  color-scheme: dark;
}

body, #root, .app-bg {
  background: #000000 !important;
  color: #d4d4d4;
}

body {
  color: #d4d4d4;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #181818;
}

::-webkit-scrollbar-thumb {
  background: #333;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background: #444;
}

/* Animation classes */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px 0 rgba(99, 102, 241, 0.4);
  }
  50% {
    box-shadow: 0 0 15px 0 rgba(99, 102, 241, 0.7);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Hide scrollbar for horizontal scrollers */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
  -webkit-overflow-scrolling: touch;  /* iOS momentum scrolling */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Smooth scrolling for mobile horizontal scrollers */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Mobile touch scrolling improvements */
@media (max-width: 640px) {
  .scrollbar-hide {
    scroll-snap-type: x proximity;
    scroll-padding: 1rem;
  }

  .scrollbar-hide > div {
    scroll-snap-align: start;
  }
}

/* Enhanced scroll animations */
@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Login page specific animations */
@keyframes loginGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(255, 136, 0, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(255, 136, 0, 0.2), 0 0 60px rgba(255, 136, 0, 0.1);
  }
}

@keyframes loginPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

/* Animation classes */
.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-login-glow {
  animation: loginGlow 3s ease-in-out infinite;
}

.animate-login-pulse {
  animation: loginPulse 2s ease-in-out infinite;
}

/* Enhanced glass morphism utilities */
.glass-morphism {
  backdrop-filter: blur(16px) saturate(180%);
  background-color: rgba(17, 25, 40, 0.75);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

.glass-morphism-light {
  backdrop-filter: blur(12px) saturate(150%);
  background-color: rgba(17, 25, 40, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.08);
}

/* Scroll-triggered animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced hover effects for mobile scrollers */
.mobile-scroller-item {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.mobile-scroller-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Enhanced scroll indicators */
.scroll-indicator {
  position: relative;
  overflow: hidden;
}

.scroll-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.scroll-indicator:hover::before {
  width: 200%;
  height: 200%;
}

.scroll-indicator:active {
  transform: scale(0.9);
}

/* Pulse animation for active indicator */
.scroll-indicator.active {
  animation: pulse-indicator 2s infinite;
}

@keyframes pulse-indicator {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.1);
  }
}
