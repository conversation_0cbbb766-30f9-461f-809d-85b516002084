<?php

namespace Wolffoxx\Utils;

use Wolffoxx\Middleware\MiddlewareInterface;

/**
 * Simple Router for API endpoints
 *
 * Handles route registration, parameter extraction,
 * and middleware execution.
 */
class Router
{
    private array $routes = [];
    private array $groups = [];

    public function __construct()
    {
        // Router initialized without logger for simplicity
    }

    /**
     * Register GET route
     */
    public function get(string $path, $handler, array $middleware = []): void
    {
        $this->addRoute('GET', $path, $handler, $middleware);
    }

    /**
     * Register POST route
     */
    public function post(string $path, $handler, array $middleware = []): void
    {
        $this->addRoute('POST', $path, $handler, $middleware);
    }

    /**
     * Register PUT route
     */
    public function put(string $path, $handler, array $middleware = []): void
    {
        $this->addRoute('PUT', $path, $handler, $middleware);
    }

    /**
     * Register DELETE route
     */
    public function delete(string $path, $handler, array $middleware = []): void
    {
        $this->addRoute('DELETE', $path, $handler, $middleware);
    }

    /**
     * Register PATCH route
     */
    public function patch(string $path, $handler, array $middleware = []): void
    {
        $this->addRoute('PATCH', $path, $handler, $middleware);
    }

    /**
     * Register route group with common prefix and middleware
     */
    public function group(string $prefix, callable $callback, array $middleware = []): void
    {
        $previousGroup = $this->groups;
        $this->groups[] = [
            'prefix' => $prefix,
            'middleware' => $middleware
        ];

        $callback($this);

        $this->groups = $previousGroup;
    }

    /**
     * Add route to routes array
     */
    private function addRoute(string $method, string $path, $handler, array $middleware = []): void
    {
        // Apply group prefixes and middleware
        $fullPath = $path;
        $allMiddleware = $middleware;

        foreach ($this->groups as $group) {
            $fullPath = $group['prefix'] . $fullPath;
            $allMiddleware = array_merge($group['middleware'], $allMiddleware);
        }

        // Convert path parameters to regex
        $pattern = $this->convertPathToRegex($fullPath);

        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'pattern' => $pattern,
            'handler' => $handler,
            'middleware' => $allMiddleware
        ];

        // Route registered successfully
    }

    /**
     * Convert path with parameters to regex pattern
     */
    private function convertPathToRegex(string $path): string
    {
        // Escape special regex characters first
        $pattern = preg_quote($path, '/');

        // Replace escaped parameter placeholders with regex groups
        $pattern = preg_replace('/\\\{([^}]+)\\\}/', '(?P<$1>[^/]+)', $pattern);

        return '/^' . $pattern . '$/';
    }

    /**
     * Dispatch request to appropriate handler
     */
    public function dispatch(string $method, string $uri): void
    {
        // Find matching route
        $matchedRoute = null;
        $parameters = [];

        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }

            // Try exact match first
            if ($route['path'] === $uri) {
                $matchedRoute = $route;
                break;
            }

            // Try simple parameter matching for routes like /products/{id}
            if (strpos($route['path'], '{') !== false) {
                $routeParts = explode('/', trim($route['path'], '/'));
                $uriParts = explode('/', trim($uri, '/'));

                if (count($routeParts) === count($uriParts)) {
                    $isMatch = true;
                    $routeParams = [];

                    for ($i = 0; $i < count($routeParts); $i++) {
                        if (strpos($routeParts[$i], '{') === 0 && strpos($routeParts[$i], '}') === strlen($routeParts[$i]) - 1) {
                            // This is a parameter
                            $paramName = trim($routeParts[$i], '{}');
                            $routeParams[$paramName] = $uriParts[$i];
                        } elseif ($routeParts[$i] !== $uriParts[$i]) {
                            // Not a match
                            $isMatch = false;
                            break;
                        }
                    }

                    if ($isMatch) {
                        $matchedRoute = $route;
                        $parameters = $routeParams;
                        break;
                    }
                }
            }
        }

        if (!$matchedRoute) {
            Response::error('Route not found', 404);
            return;
        }

        try {
            // Execute middleware
            foreach ($matchedRoute['middleware'] as $middlewareClass) {
                $this->executeMiddleware($middlewareClass, $parameters);
            }

            // Execute handler
            $this->executeHandler($matchedRoute['handler'], $parameters);

        } catch (\Exception $e) {
            if ($_ENV['APP_ENV'] === 'development') {
                Response::error([
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ], 500);
            } else {
                Response::error('Internal server error', 500);
            }
        }
    }

    /**
     * Execute middleware
     */
    private function executeMiddleware(string $middlewareClass, array $parameters): void
    {
        // Parse middleware class and parameters
        $parts = explode(':', $middlewareClass);
        $className = $parts[0];
        $middlewareParams = array_slice($parts, 1);

        if (!class_exists($className)) {
            throw new \Exception("Middleware class not found: {$className}");
        }

        $middleware = new $className();

        if (!$middleware instanceof MiddlewareInterface) {
            throw new \Exception("Middleware must implement MiddlewareInterface: {$className}");
        }

        // Executing middleware

        $result = $middleware->handle($middlewareParams);

        if (!$result) {
            throw new \Exception("Middleware blocked request: {$className}");
        }
    }

    /**
     * Execute route handler
     */
    private function executeHandler($handler, array $parameters): void
    {
        if (is_callable($handler)) {
            // Direct callable
            $handler($parameters);
        } elseif (is_string($handler)) {
            // Controller@method format
            $this->executeControllerMethod($handler, $parameters);
        } else {
            throw new \Exception('Invalid handler type');
        }
    }

    /**
     * Execute controller method
     */
    private function executeControllerMethod(string $handler, array $parameters): void
    {
        $parts = explode('@', $handler);

        if (count($parts) !== 2) {
            throw new \Exception("Invalid controller format: {$handler}");
        }

        [$controllerClass, $method] = $parts;

        if (!class_exists($controllerClass)) {
            throw new \Exception("Controller class not found: {$controllerClass}");
        }

        $controller = new $controllerClass();

        if (!method_exists($controller, $method)) {
            throw new \Exception("Method not found: {$controllerClass}@{$method}");
        }

        // Executing controller method

        $controller->$method($parameters);
    }

    /**
     * Get all registered routes (for debugging)
     */
    public function getRoutes(): array
    {
        return $this->routes;
    }
}
