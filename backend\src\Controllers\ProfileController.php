<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\User;
use Wolffoxx\Models\UserAddress;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware as Auth;

/**
 * Profile Controller
 * 
 * Handles user profile completion and validation
 */
class ProfileController
{
    private User $userModel;
    private UserAddress $addressModel;

    public function __construct()
    {
        $this->userModel = new User();
        $this->addressModel = new UserAddress();
    }

    /**
     * Check profile completion status
     * GET /api/v1/profile/completion-status
     */
    public function getCompletionStatus(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $status = $this->userModel->getProfileCompletionStatus($user['id']);

            Response::success([
                'is_complete' => $status['is_complete'],
                'missing_fields' => $status['missing_fields'],
                'user' => $status['user'],
                'requires_modal' => !$status['is_complete']
            ]);

        } catch (\Exception $e) {
            error_log('Get profile completion status failed: ' . $e->getMessage());
            Response::error('Failed to get profile status', 500);
        }
    }

    /**
     * Complete user profile (modal submission)
     * POST /api/v1/profile/complete
     */
    public function completeProfile(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate required fields
            if (empty($input['name']) || empty($input['email'])) {
                Response::error('Name and email are required', 400);
                return;
            }

            // Validate name length
            if (strlen(trim($input['name'])) < 2) {
                Response::error('Name must be at least 2 characters', 400);
                return;
            }

            // Validate email format
            if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format', 400);
                return;
            }

            // Complete profile
            $success = $this->userModel->completeProfile(
                $user['id'],
                $input['name'],
                $input['email']
            );

            if ($success) {
                // Get updated user data
                $updatedStatus = $this->userModel->getProfileCompletionStatus($user['id']);

                Response::success([
                    'message' => 'Profile completed successfully',
                    'is_complete' => $updatedStatus['is_complete'],
                    'user' => $updatedStatus['user']
                ]);
            } else {
                Response::error('Failed to complete profile', 500);
            }

        } catch (\InvalidArgumentException $e) {
            Response::error($e->getMessage(), 400);
        } catch (\Exception $e) {
            error_log('Complete profile failed: ' . $e->getMessage());
            Response::error('Failed to complete profile', 500);
        }
    }

    /**
     * Validate user for order placement
     * GET /api/v1/profile/validate-for-order
     */
    public function validateForOrder(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $status = $this->userModel->getProfileCompletionStatus($user['id']);

            // Check if profile is complete
            if (!$status['is_complete']) {
                Response::error('Profile incomplete. Please complete your profile before placing an order.', 400, [
                    'missing_fields' => $status['missing_fields'],
                    'requires_profile_completion' => true
                ]);
                return;
            }

            // Check if phone is verified (assuming phone verification is done during OTP)
            if (empty($user['phone'])) {
                Response::error('Phone number verification required', 400, [
                    'requires_phone_verification' => true
                ]);
                return;
            }

            Response::success([
                'message' => 'User profile is valid for order placement',
                'user' => $status['user'],
                'can_place_order' => true
            ]);

        } catch (\Exception $e) {
            error_log('Validate for order failed: ' . $e->getMessage());
            Response::error('Failed to validate user profile', 500);
        }
    }

    /**
     * Update user profile
     * PUT /api/v1/profile
     */
    public function updateProfile(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            error_log('DEBUG: Received profile update input: ' . json_encode($input));

            // Validate inputs if provided
            if (isset($input['name']) && strlen(trim($input['name'])) < 2) {
                Response::error('Name must be at least 2 characters', 400);
                return;
            }

            if (isset($input['email']) && !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Invalid email format', 400);
                return;
            }

            // Check if email is already taken by another user
            if (isset($input['email'])) {
                $existingUser = $this->userModel->findByEmail($input['email']);
                if ($existingUser && $existingUser['id'] !== $user['id']) {
                    Response::error('Email already exists', 400);
                    return;
                }
            }

            // Prepare update data
            $updateData = [];
            
            if (isset($input['name'])) {
                $nameParts = explode(' ', trim($input['name']), 2);
                $updateData['first_name'] = $nameParts[0];
                $updateData['last_name'] = isset($nameParts[1]) ? $nameParts[1] : '';
            }

            if (isset($input['email'])) {
                $updateData['email'] = $input['email'];
            }

            if (isset($input['date_of_birth'])) {
                $updateData['date_of_birth'] = $input['date_of_birth'];
            }

            if (isset($input['gender'])) {
                $updateData['gender'] = $input['gender'];
            }
            
            // Handle address data if provided
            if (!empty($input['address_data']) && is_array($input['address_data'])) {
                error_log('Profile update - address data found: ' . json_encode($input['address_data']));
                
                // Add user_id to address data
                $input['address_data']['user_id'] = $user['id'];
                
                // Set default values if not provided
                if (!isset($input['address_data']['type'])) {
                    $input['address_data']['type'] = 'shipping';
                }
                
                if (!isset($input['address_data']['is_default'])) {
                    $input['address_data']['is_default'] = true;
                }
                
                // Make sure all required fields are present
                $requiredFields = ['first_name', 'last_name', 'address_line_1', 'city', 'state', 'postal_code', 'country'];
                $missingFields = [];
                
                foreach ($requiredFields as $field) {
                    if (empty($input['address_data'][$field])) {
                        $missingFields[] = $field;
                    }
                }
                
                if (!empty($missingFields)) {
                    error_log('Missing required address fields: ' . implode(', ', $missingFields));
                    Response::error('Missing required address fields: ' . implode(', ', $missingFields), 400);
                    return;
                }
                
                try {
                    // Check if a shipping address already exists for this user
                    $existingAddress = $this->addressModel->getDefaultAddress($user['id'], $input['address_data']['type']);
                    if ($existingAddress) {
                        // Update existing address
                        $address = $this->addressModel->updateAddress($existingAddress['id'], $input['address_data']);
                        error_log('Address updated successfully: ' . ($address ? 'yes' : 'no'));
                        if ($address) {
                            error_log('Updated address ID: ' . ($address['id'] ?? 'unknown'));
                        }
                    } else {
                        // Add the address
                        error_log('Attempting to add address for user ID: ' . $user['id']);
                        error_log('DEBUG: About to call addAddress with: ' . json_encode($input['address_data']));
                        $address = $this->addressModel->addAddress($input['address_data']);
                        error_log('DEBUG: addAddress returned: ' . json_encode($address));
                        error_log('Address added successfully: ' . ($address ? 'yes' : 'no'));
                        if ($address) {
                            error_log('New address ID: ' . ($address['id'] ?? 'unknown'));
                        }
                    }
                } catch (\Exception $e) {
                    error_log('Error adding/updating address: ' . $e->getMessage());
                    error_log('Stack trace: ' . $e->getTraceAsString());
                    // Continue with profile update even if address fails
                }
            }

            // Update profile completion status
            $isComplete = $this->userModel->isProfileComplete($user['id']);
            if ($isComplete) {
                $updateData['profile_completed'] = 1;
                $updateData['profile_completed_at'] = date('Y-m-d H:i:s');
            }

            if (empty($updateData) && empty($input['address_data'])) {
                Response::error('No valid fields to update', 400);
                return;
            }

            // Only update user profile if there are fields to update
            $updatedUser = !empty($updateData) ? 
                $this->userModel->updateProfile($user['id'], $updateData) : 
                $this->userModel->findById($user['id']);

            if ($updatedUser) {
                // Get the updated profile with addresses
                $fullProfile = $this->userModel->getProfile($user['id']);
                
                // Add addresses to the response
                if ($fullProfile) {
                    $fullProfile['addresses'] = $this->addressModel->getUserAddresses($user['id']);
                }
                
                Response::success([
                    'message' => 'Profile updated successfully',
                    'user' => $fullProfile ?: $updatedUser
                ]);
            } else {
                Response::error('Failed to update profile', 500);
            }

        } catch (\Exception $e) {
            error_log('Update profile failed: ' . $e->getMessage());
            Response::error('Failed to update profile: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get user profile
     * GET /api/v1/profile
     */
    public function getProfile(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $profile = $this->userModel->getProfile($user['id']);

            if ($profile) {
                // Add completion status
                $completionStatus = $this->userModel->getProfileCompletionStatus($user['id']);
                $profile['profile_completion'] = $completionStatus;
                
                // Add addresses
                $profile['addresses'] = $this->addressModel->getUserAddresses($user['id']);

                Response::success($profile);
            } else {
                Response::error('Profile not found', 404);
            }

        } catch (\Exception $e) {
            error_log('Get profile failed: ' . $e->getMessage());
            Response::error('Failed to get profile', 500);
        }
    }
    
    /**
     * Add a new address
     * POST /api/v1/profile/address
     */
    public function addAddress(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $requiredFields = ['first_name', 'last_name', 'address_line_1', 'city', 'state', 'postal_code', 'country'];
            foreach ($requiredFields as $field) {
                if (empty($input[$field])) {
                    Response::error("Missing required field: {$field}", 400);
                    return;
                }
            }
            
            // Set user_id
            $input['user_id'] = $user['id'];
            
            // Set default values
            $input['type'] = $input['type'] ?? 'shipping';
            $input['is_default'] = isset($input['is_default']) ? (bool)$input['is_default'] : true;
            
            // Add address
            $address = $this->addressModel->addAddress($input);
            
            if ($address) {
                // Update user profile completion status
                $updatedStatus = $this->userModel->getProfileCompletionStatus($user['id']);
                
                Response::success([
                    'message' => 'Address added successfully',
                    'address' => $address,
                    'profile_completion' => $updatedStatus
                ]);
            } else {
                Response::error('Failed to add address', 500);
            }
            
        } catch (\Exception $e) {
            error_log('Add address failed: ' . $e->getMessage());
            Response::error('Failed to add address: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Update an address
     * PUT /api/v1/profile/address/{id}
     */
    public function updateAddress(array $params): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }
            
            $addressId = (int)$params['id'];
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Check if address exists and belongs to user
            $address = $this->addressModel->findById($addressId);
            if (!$address) {
                Response::error('Address not found', 404);
                return;
            }
            
            if ($address['user_id'] !== $user['id']) {
                Response::error('Access denied', 403);
                return;
            }
            
            // Update address
            $updatedAddress = $this->addressModel->updateAddress($addressId, $input);
            
            if ($updatedAddress) {
                Response::success([
                    'message' => 'Address updated successfully',
                    'address' => $updatedAddress
                ]);
            } else {
                Response::error('Failed to update address', 500);
            }
            
        } catch (\Exception $e) {
            error_log('Update address failed: ' . $e->getMessage());
            Response::error('Failed to update address: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Delete an address
     * DELETE /api/v1/profile/address/{id}
     */
    public function deleteAddress(array $params): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }
            
            $addressId = (int)$params['id'];
            
            // Delete address
            $deleted = $this->addressModel->deleteAddress($addressId, $user['id']);
            
            if ($deleted) {
                Response::success([
                    'message' => 'Address deleted successfully'
                ]);
            } else {
                Response::error('Failed to delete address', 500);
            }
            
        } catch (\Exception $e) {
            error_log('Delete address failed: ' . $e->getMessage());
            Response::error('Failed to delete address: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get all addresses
     * GET /api/v1/profile/addresses
     */
    public function getAddresses(): void
    {
        try {
            $user = Auth::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }
            
            $addresses = $this->addressModel->getUserAddresses($user['id']);
            
            Response::success([
                'addresses' => $addresses
            ]);
            
        } catch (\Exception $e) {
            error_log('Get addresses failed: ' . $e->getMessage());
            Response::error('Failed to get addresses', 500);
        }
    }
}
