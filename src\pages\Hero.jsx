import React, { Suspense, useEffect, useState } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { ArrowRight, Play, Star, TrendingUp } from 'lucide-react';

// Mock Link component for demo
const Link = ({ to, children, className, ...props }) => (
  <a href={to} className={className} {...props}>{children}</a>
);

const EnhancedHeroSection = () => {
  const { scrollY } = useScroll();
  const y1 = useTransform(scrollY, [0, 300], [0, -50]);
  const y2 = useTransform(scrollY, [0, 300], [0, -100]);
  const opacity = useTransform(scrollY, [0, 300], [1, 0.8]);
  
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e) => {
      setMousePosition({
        x: (e.clientX / window.innerWidth) * 100,
        y: (e.clientY / window.innerHeight) * 100,
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleExploreClick = () => {
    window.scrollTo({ top: window.innerHeight, behavior: 'smooth' });
  };

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden bg-slate-950">
      {/* Enhanced Background with Parallax */}
      <motion.div
        className="absolute inset-0"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(59, 130, 246, 0.15) 0%, rgba(14, 165, 233, 0.1) 25%, rgba(2, 6, 23, 0.9) 50%)`,
        }}
      />
      
      {/* Animated Grid Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `
              linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
          }}
        />
      </div>

      {/* Hero Image with Modern Overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1574201635302-388dd92a4c3f?q=80&w=2070&auto=format&fit=crop"
          alt="WOLFFOXX hero background"
          className="object-cover w-full h-full"
          loading="eager"
        />
        <motion.div
          className="absolute inset-0 bg-gradient-to-br from-slate-950/95 via-slate-900/85 to-slate-950/95"
          animate={{
            background: [
              'linear-gradient(135deg, rgba(2, 6, 23, 0.95), rgba(15, 23, 42, 0.85), rgba(2, 6, 23, 0.95))',
              'linear-gradient(135deg, rgba(2, 6, 23, 0.90), rgba(15, 23, 42, 0.80), rgba(2, 6, 23, 0.90))',
              'linear-gradient(135deg, rgba(2, 6, 23, 0.95), rgba(15, 23, 42, 0.85), rgba(2, 6, 23, 0.95))',
            ]
          }}
          transition={{ duration: 6, repeat: Infinity }}
        />
      </div>

      {/* Floating Elements */}
      <motion.div
        className="absolute top-20 left-20 w-2 h-2 bg-blue-400 rounded-full opacity-60"
        animate={{
          y: [0, -20, 0],
          opacity: [0.6, 1, 0.6],
        }}
        transition={{ duration: 3, repeat: Infinity, delay: 0 }}
      />
      <motion.div
        className="absolute top-40 right-32 w-1 h-1 bg-cyan-400 rounded-full opacity-80"
        animate={{
          y: [0, -15, 0],
          opacity: [0.8, 1, 0.8],
        }}
        transition={{ duration: 4, repeat: Infinity, delay: 1 }}
      />
      <motion.div
        className="absolute bottom-32 left-40 w-1.5 h-1.5 bg-sky-400 rounded-full opacity-70"
        animate={{
          y: [0, -25, 0],
          opacity: [0.7, 1, 0.7],
        }}
        transition={{ duration: 5, repeat: Infinity, delay: 2 }}
      />

      {/* Main Content */}
      <motion.div
        className="container mx-auto px-6 md:px-12 relative z-10 text-center"
        style={{ y: y1, opacity }}
      >
        {/* Premium Badge */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          className="mb-8"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/5 backdrop-blur-md border border-white/10 rounded-full text-blue-300 text-sm font-medium">
            <Star className="w-4 h-4 fill-current" />
            <span>Premium Collection 2025</span>
            <TrendingUp className="w-4 h-4" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.2 }}
          className="max-w-5xl mx-auto"
        >
          {/* Enhanced Title */}
          <motion.div
            className="mb-10"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
          >
            <motion.h1
              className="font-['Inter',sans-serif] text-5xl md:text-8xl font-black text-white leading-tight mb-6 tracking-tight"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <motion.span
                className="block relative mb-2"
                animate={{
                  textShadow: [
                    '0 0 20px rgba(59, 130, 246, 0.3)',
                    '0 0 40px rgba(14, 165, 233, 0.4)',
                    '0 0 20px rgba(59, 130, 246, 0.3)',
                  ]
                }}
                transition={{ duration: 3, repeat: Infinity }}
              >
                WOLFFOXX
              </motion.span>
              <motion.span
                className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-cyan-300 to-sky-400 font-light tracking-wider"
                animate={{
                  backgroundPosition: ['0%', '100%', '0%'],
                }}
                transition={{ duration: 4, repeat: Infinity }}
                style={{ backgroundSize: '200% 200%' }}
              >
                OVERSIZED TEES
              </motion.span>
              
              {/* Accent Line */}
              <motion.div
                className="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto mt-6 rounded-full"
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 1, delay: 1 }}
              />
            </motion.h1>
          </motion.div>

          {/* Enhanced Description */}
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <p className="text-gray-300 text-lg md:text-2xl font-light leading-relaxed max-w-3xl mx-auto">
              Redefining streetwear with 
              <motion.span
                className="text-blue-300 font-medium mx-2"
                whileHover={{ scale: 1.05 }}
              >
                bold designs
              </motion.span>
              and 
              <motion.span
                className="text-cyan-300 font-medium mx-2"
                whileHover={{ scale: 1.05 }}
              >
                exaggerated proportions
              </motion.span>
              <br className="hidden md:block" />
              that make every moment a 
              <motion.span
                className="relative inline-block font-semibold text-white ml-2"
                whileHover={{ scale: 1.1 }}
              >
                statement
                <motion.div
                  className="absolute -bottom-1 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ duration: 0.8, delay: 1.4 }}
                />
              </motion.span>
              .
            </p>
          </motion.div>

          {/* Enhanced CTA Section */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            {/* Primary CTA */}
            <Suspense fallback={<div className="h-14 w-48 bg-blue-600 rounded-xl animate-pulse" />}>
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.98 }}
                className="group"
              >
                <Link
                  to="/collection"
                  className="relative inline-flex items-center px-10 py-4 bg-gradient-to-r from-blue-600 via-blue-500 to-cyan-500 hover:from-blue-700 hover:via-blue-600 hover:to-cyan-600 text-white font-bold rounded-xl transition-all duration-500 shadow-2xl shadow-blue-900/30 overflow-hidden"
                >
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-cyan-500 to-sky-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                  <span className="relative z-10 text-lg">SHOP COLLECTION</span>
                  <ArrowRight className="ml-3 relative z-10 group-hover:translate-x-1 transition-transform duration-300" size={22} />
                  
                  {/* Shine Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700"
                  />
                </Link>
              </motion.div>
            </Suspense>

            {/* Secondary CTA */}
            <motion.div
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <Link
                to="/new-arrivals"
                className="inline-flex items-center px-10 py-4 bg-white/5 hover:bg-white/10 text-white border border-white/20 hover:border-white/40 font-semibold rounded-xl transition-all duration-500 backdrop-blur-md"
              >
                <Play className="mr-3" size={18} />
                <span className="text-lg">NEW DROPS</span>
              </Link>
            </motion.div>
          </motion.div>

          {/* Stats or Social Proof */}
          <motion.div
            className="mt-16 flex flex-wrap gap-8 justify-center text-center"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            {[
              { number: '50K+', label: 'Happy Customers' },
              { number: '4.9★', label: 'Average Rating' },
              { number: '100+', label: 'Unique Designs' }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                whileHover={{ scale: 1.1 }}
              >
                <div className="text-2xl md:text-3xl font-bold text-white mb-1">
                  {stat.number}
                </div>
                <div className="text-sm text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Enhanced Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-0 right-0 flex justify-center"
        style={{ y: y2 }}
      >
        <motion.div
          className="flex flex-col items-center text-white/60 gap-3 cursor-pointer group"
          animate={{ y: [0, 8, 0] }}
          transition={{ repeat: Infinity, duration: 2, ease: "easeInOut" }}
          onClick={handleExploreClick}
          whileHover={{ scale: 1.1, color: 'rgba(255, 255, 255, 0.9)' }}
        >
          <span className="text-xs font-semibold tracking-widest uppercase">Discover More</span>
          <motion.div
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center p-1.5 group-hover:border-white/60 transition-colors duration-300"
          >
            <motion.div
              className="w-1 h-2 bg-white/50 rounded-full group-hover:bg-white/80 transition-colors duration-300"
              animate={{ y: [0, 12, 0] }}
              transition={{ repeat: Infinity, duration: 1.5 }}
            />
          </motion.div>
        </motion.div>
      </motion.div>
    </section>
  );
};

export default EnhancedHeroSection;