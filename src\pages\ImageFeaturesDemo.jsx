import { useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, <PERSON>ap, Eye, MousePointer, Smartphone } from 'lucide-react';
import { Link } from 'react-router-dom';
import EnhancedImageZoom from '../components/EnhancedImageZoom';
import EnhancedImageTransitions from '../components/EnhancedImageTransitions';
import InteractiveHotspots from '../components/InteractiveHotspots';
import EnhancedProductImage from '../components/EnhancedProductImage';
import { products } from '../data/products';

export default function ImageFeaturesDemo() {
  const [activeDemo, setActiveDemo] = useState('zoom');
  const demoProduct = products[0]; // Use first product for demo

  const demoImages = [
    'https://images.unsplash.com/photo-1618354691438-25bc04584c23?q=80&w=700&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=700&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1556821840-3a63f95609a7?q=80&w=700&auto=format&fit=crop',
    'https://images.unsplash.com/photo-1607345366928-199ea26cfe3e?q=80&w=700&auto=format&fit=crop'
  ];

  const features = [
    {
      id: 'zoom',
      title: 'Advanced Image Zoom',
      description: 'Smooth zoom with magnifying glass effect and high-res loading',
      icon: Eye,
      color: 'blue'
    },
    {
      id: 'transitions',
      title: 'Smooth Transitions',
      description: 'Fade, slide, and scale animations with preloading',
      icon: Zap,
      color: 'purple'
    },
    {
      id: 'hotspots',
      title: 'Interactive Hotspots',
      description: 'Clickable areas with product information and tooltips',
      icon: MousePointer,
      color: 'green'
    },
    {
      id: 'complete',
      title: 'Complete Integration',
      description: 'All features combined in product cards and pages',
      icon: Smartphone,
      color: 'orange'
    }
  ];

  const getFeatureColor = (color) => {
    const colors = {
      blue: 'from-blue-500 to-cyan-500',
      purple: 'from-purple-500 to-pink-500',
      green: 'from-green-500 to-teal-500',
      orange: 'from-orange-500 to-red-500'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-[#0f172a] text-white">
      {/* Header */}
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-4 mb-8">
          <Link 
            to="/" 
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ArrowLeft size={20} />
            Back to Home
          </Link>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
            Enhanced Image Features
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Experience our advanced image interactions including zoom, smooth transitions, and interactive hotspots
          </p>
        </motion.div>

        {/* Feature Navigation */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {features.map((feature) => {
            const IconComponent = feature.icon;
            return (
              <motion.button
                key={feature.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveDemo(feature.id)}
                className={`flex items-center gap-3 px-6 py-3 rounded-xl transition-all ${
                  activeDemo === feature.id
                    ? `bg-gradient-to-r ${getFeatureColor(feature.color)} text-white shadow-lg`
                    : 'bg-slate-800 text-gray-300 hover:bg-slate-700'
                }`}
              >
                <IconComponent size={20} />
                <span className="font-medium">{feature.title}</span>
              </motion.button>
            );
          })}
        </div>

        {/* Demo Content */}
        <motion.div
          key={activeDemo}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-6xl mx-auto"
        >
          {/* Feature Description */}
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold mb-2">
              {features.find(f => f.id === activeDemo)?.title}
            </h2>
            <p className="text-gray-400">
              {features.find(f => f.id === activeDemo)?.description}
            </p>
          </div>

          {/* Demo Area */}
          <div className="bg-slate-900 rounded-2xl p-8">
            {activeDemo === 'zoom' && (
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Basic Zoom with Magnifier</h3>
                  <div className="h-80 rounded-lg overflow-hidden">
                    <EnhancedImageZoom
                      src={demoImages[0]}
                      alt="Demo Image"
                      className="w-full h-full"
                      zoomLevel={2.5}
                      enableMagnifier={true}
                      showControls={true}
                    />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Advanced Zoom Controls</h3>
                  <div className="h-80 rounded-lg overflow-hidden">
                    <EnhancedImageZoom
                      src={demoImages[1]}
                      alt="Demo Image"
                      className="w-full h-full"
                      zoomLevel={3}
                      enableMagnifier={false}
                      showControls={true}
                      highResSrc={demoImages[1]}
                    />
                  </div>
                </div>
              </div>
            )}

            {activeDemo === 'transitions' && (
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Fade Transition</h3>
                  <div className="h-80 rounded-lg overflow-hidden">
                    <EnhancedImageTransitions
                      images={demoImages}
                      currentIndex={0}
                      transitionType="fade"
                      className="w-full h-full"
                      autoPreload={true}
                    />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Slide Transition</h3>
                  <div className="h-80 rounded-lg overflow-hidden">
                    <EnhancedImageTransitions
                      images={demoImages}
                      currentIndex={0}
                      transitionType="slide"
                      className="w-full h-full"
                      autoPreload={true}
                    />
                  </div>
                </div>
              </div>
            )}

            {activeDemo === 'hotspots' && (
              <div className="max-w-2xl mx-auto">
                <h3 className="text-lg font-semibold mb-4 text-center">Interactive Product Hotspots</h3>
                <div className="h-96 rounded-lg overflow-hidden relative">
                  <img
                    src={demoImages[0]}
                    alt="Product with hotspots"
                    className="w-full h-full object-cover"
                  />
                  <InteractiveHotspots
                    className="absolute inset-0"
                    showOnHover={false}
                    alwaysVisible={true}
                  />
                </div>
                <p className="text-center text-gray-400 mt-4">
                  Click on the hotspots to see product information
                </p>
              </div>
            )}

            {activeDemo === 'complete' && (
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Product Card Integration</h3>
                  <div className="h-96 rounded-lg overflow-hidden">
                    <EnhancedProductImage
                      product={demoProduct}
                      selectedColorIndex={0}
                      currentImageIndex={0}
                      className="w-full h-full"
                      showHotspots={true}
                      enableZoom={true}
                      enableTransitions={true}
                      viewMode="card"
                    />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold mb-4">Product Page Integration</h3>
                  <div className="h-96 rounded-lg overflow-hidden">
                    <EnhancedProductImage
                      product={demoProduct}
                      selectedColorIndex={0}
                      currentImageIndex={0}
                      className="w-full h-full"
                      showHotspots={true}
                      enableZoom={true}
                      enableTransitions={true}
                      viewMode="page"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Feature Benefits */}
          <div className="mt-12 grid md:grid-cols-3 gap-6">
            <div className="bg-slate-800 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Eye className="text-blue-400" size={24} />
              </div>
              <h3 className="font-semibold mb-2">Enhanced UX</h3>
              <p className="text-gray-400 text-sm">
                Smooth interactions and visual feedback improve user engagement
              </p>
            </div>
            <div className="bg-slate-800 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Zap className="text-purple-400" size={24} />
              </div>
              <h3 className="font-semibold mb-2">Performance Optimized</h3>
              <p className="text-gray-400 text-sm">
                Image preloading and efficient animations for mobile users
              </p>
            </div>
            <div className="bg-slate-800 rounded-lg p-6 text-center">
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Smartphone className="text-green-400" size={24} />
              </div>
              <h3 className="font-semibold mb-2">Mobile First</h3>
              <p className="text-gray-400 text-sm">
                Touch-friendly interactions designed for 90% mobile traffic
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
