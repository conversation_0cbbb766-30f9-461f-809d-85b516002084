import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useOutfit } from '../context/OutfitContext';
import { dataService } from '../services/dataService';

const TestPersistentFeatures = () => {
  const { user, isAuthenticated } = useAuth();
  const { items: cartItems, addToCart, totalItems } = useCart();
  const { wishlistItems, addToWishlist, totalWishlistItems } = useWishlist();
  const { savedOutfits, saveOutfit, totalSavedOutfits } = useOutfit();
  const [testProduct, setTestProduct] = useState(null);
  const [loading, setLoading] = useState(false);

  // Load a test product
  useEffect(() => {
    const loadTestProduct = async () => {
      try {
        setLoading(true);
        const products = await dataService.getProducts({}, 1, 1);
        if (products.products && products.products.length > 0) {
          setTestProduct(products.products[0]);
        }
      } catch (error) {
        console.error('Failed to load test product:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTestProduct();
  }, []);

  const handleAddToCart = () => {
    if (testProduct) {
      // Use the rich color data from your backend
      const selectedColor = testProduct.colors?.[0];
      addToCart({
        ...testProduct,
        color: selectedColor?.name || 'Default',
        size: testProduct.sizes?.[0] || 'M',
        colorHex: selectedColor?.value || '#000000',
        image: selectedColor?.images?.[0] || testProduct.images?.[0]
      }, 1);
    }
  };

  const handleAddToWishlist = () => {
    if (testProduct) {
      addToWishlist(testProduct);
    }
  };

  const handleSaveOutfit = () => {
    if (testProduct) {
      const outfitItems = [{
        ...testProduct,
        selectedColor: testProduct.colors?.[0] || 'Default',
        selectedSize: testProduct.sizes?.[0] || 'M'
      }];
      saveOutfit(outfitItems, 'Test Outfit');
    }
  };

  if (loading) {
    return (
      <div className="p-6 bg-gray-900 text-white rounded-lg">
        <h2 className="text-xl font-bold mb-4">🧪 Testing Persistent Features</h2>
        <p>Loading test product...</p>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-900 text-white rounded-lg space-y-6">
      <h2 className="text-xl font-bold mb-4">🧪 Testing Persistent Features</h2>
      
      {/* Authentication Status */}
      <div className="bg-gray-800 p-4 rounded">
        <h3 className="font-semibold mb-2">🔐 Authentication Status</h3>
        <p>Status: {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}</p>
        {user && <p>User ID: {user.id}</p>}
        {user && <p>Phone: {user.phone}</p>}
      </div>

      {/* Test Product */}
      {testProduct && (
        <div className="bg-gray-800 p-4 rounded">
          <h3 className="font-semibold mb-2">📦 Test Product</h3>
          <div className="flex items-center space-x-4">
            {testProduct.images && testProduct.images[0] && (
              <img 
                src={testProduct.images[0]} 
                alt={testProduct.name}
                className="w-16 h-16 object-cover rounded"
              />
            )}
            <div>
              <p className="font-medium">{testProduct.name}</p>
              <p className="text-gray-300">₹{testProduct.price}</p>
              {testProduct.salePrice && (
                <p className="text-green-400">Sale: ₹{testProduct.salePrice}</p>
              )}
              <div className="text-sm text-gray-400">
                <p>Colors: {testProduct.colors?.map(c => `${c.name} (${c.value})`).join(', ') || 'None'}</p>
                <p>Sizes: {testProduct.sizes?.join(', ') || 'None'}</p>
                <p>Material: {testProduct.material}</p>
                <p>Fit: {testProduct.fit}</p>
                <p>Stock: {testProduct.in_stock ? `${testProduct.stock_quantity} available` : 'Out of stock'}</p>
                <p>Rating: {testProduct.rating}/5 ⭐</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 space-x-2">
            <button
              onClick={handleAddToCart}
              className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded text-sm"
            >
              Add to Cart
            </button>
            <button
              onClick={handleAddToWishlist}
              className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded text-sm"
            >
              Add to Wishlist
            </button>
            <button
              onClick={handleSaveOutfit}
              className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded text-sm"
            >
              Save as Outfit
            </button>
          </div>
        </div>
      )}

      {/* Cart Status */}
      <div className="bg-gray-800 p-4 rounded">
        <h3 className="font-semibold mb-2">🛒 Cart Status</h3>
        <p>Total Items: {totalItems}</p>
        <p>Items in Cart: {cartItems.length}</p>
        {cartItems.length > 0 && (
          <div className="mt-2">
            <p className="text-sm text-gray-300">Recent items with rich data:</p>
            {cartItems.slice(0, 3).map((item, index) => (
              <div key={index} className="text-sm text-gray-400 bg-gray-700 p-2 rounded mt-1">
                <div className="flex items-center space-x-2">
                  {item.image && (
                    <img src={item.image} alt={item.name} className="w-8 h-8 object-cover rounded" />
                  )}
                  <div>
                    <p>• {item.name}</p>
                    <p>Color: {item.color} {item.colorHex && <span style={{backgroundColor: item.colorHex}} className="inline-block w-3 h-3 rounded-full ml-1"></span>}</p>
                    <p>Size: {item.size} | Qty: {item.quantity}</p>
                    <p>Price: ₹{item.salePrice || item.price} {item.salePrice && <span className="line-through text-gray-500">₹{item.price}</span>}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Wishlist Status */}
      <div className="bg-gray-800 p-4 rounded">
        <h3 className="font-semibold mb-2">❤️ Wishlist Status</h3>
        <p>Total Items: {totalWishlistItems}</p>
        {wishlistItems.length > 0 && (
          <div className="mt-2">
            <p className="text-sm text-gray-300">Recent items with rich data:</p>
            {wishlistItems.slice(0, 3).map((item, index) => (
              <div key={index} className="text-sm text-gray-400 bg-gray-700 p-2 rounded mt-1">
                <div className="flex items-center space-x-2">
                  {item.image && (
                    <img src={item.image} alt={item.name} className="w-8 h-8 object-cover rounded" />
                  )}
                  <div>
                    <p>• {item.name}</p>
                    <p>Price: ₹{item.salePrice || item.price} {item.salePrice && <span className="line-through text-gray-500">₹{item.price}</span>}</p>
                    <p>Colors: {item.colors?.length || 0} available</p>
                    <p>Stock: {item.in_stock ? '✅ In Stock' : '❌ Out of Stock'}</p>
                    <p>Rating: {item.rating}/5 ⭐</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Outfit Status */}
      <div className="bg-gray-800 p-4 rounded">
        <h3 className="font-semibold mb-2">👔 Outfit Status</h3>
        <p>Total Outfits: {totalSavedOutfits}</p>
        {savedOutfits.length > 0 && (
          <div className="mt-2">
            <p className="text-sm text-gray-300">Recent outfits:</p>
            {savedOutfits.slice(0, 3).map((outfit, index) => (
              <div key={index} className="text-sm text-gray-400">
                • {outfit.name} ({outfit.items?.length || 0} items)
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Data Display Examples */}
      <div className="bg-gray-800 p-4 rounded">
        <h3 className="font-semibold mb-2">🎨 Product Data Display Examples</h3>
        <div className="text-sm space-y-2">
          <p className="text-gray-300">✅ Product Images: Available from backend</p>
          <p className="text-gray-300">✅ Product Colors: Available with names and hex values</p>
          <p className="text-gray-300">✅ Product Prices: Current price + sale price</p>
          <p className="text-gray-300">✅ Product Sizes: Available as array</p>
          <p className="text-gray-300">✅ Stock Status: Available from backend</p>
          <p className="text-gray-300">✅ User Selections: Stored with cart/wishlist/outfit items</p>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-900 p-4 rounded">
        <h3 className="font-semibold mb-2">📋 How to Test</h3>
        <ol className="text-sm space-y-1 list-decimal list-inside">
          <li>Login via OTP to enable backend persistence</li>
          <li>Click "Add to Cart" to test cart functionality</li>
          <li>Click "Add to Wishlist" to test wishlist functionality</li>
          <li>Click "Save as Outfit" to test outfit functionality</li>
          <li>Refresh the page to see if data persists</li>
          <li>Logout and login again to test cross-session persistence</li>
        </ol>
      </div>
    </div>
  );
};

export default TestPersistentFeatures;
