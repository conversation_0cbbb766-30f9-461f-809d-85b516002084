<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #000;
            color: #fff;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #111;
        }
        button {
            background: linear-gradient(45deg, #214FC3, #54AEE1);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            opacity: 0.8;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #222;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .error {
            color: #ff6b6b;
        }
        .success {
            color: #51cf66;
        }
    </style>
</head>
<body>
    <h1>🚀 Wolffoxx Backend Integration Test</h1>
    
    <div class="test-section">
        <h2>🔗 API Health Check</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <button onclick="testDatabase()">Test Database Connection</button>
        <div id="health-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📦 Products API</h2>
        <button onclick="testTableStructure()">Check Table Structure</button>
        <button onclick="testSimpleProducts()">Test Simple Products</button>
        <button onclick="testProducts()">Get All Products</button>
        <button onclick="testSingleProduct()">Get Single Product (ID: 1)</button>
        <button onclick="testCategories()">Get Categories</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 Search API</h2>
        <button onclick="testSearch()">Search "oversized"</button>
        <button onclick="testCategoryProducts()">Get Oversized Tees</button>
        <button onclick="testBestsellers()">Get Bestsellers Only</button>
        <button onclick="testNewProducts()">Get New Products Only</button>
        <button onclick="testSaleProducts()">Get Sale Products Only</button>
        <div id="search-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🏆 Special Collections</h2>
        <button onclick="testBestsellers()">Get Bestsellers</button>
        <button onclick="testNewProducts()">Get New Products</button>
        <button onclick="testSaleProducts()">Get Sale Products</button>
        <div id="collections-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';

        async function makeRequest(endpoint, resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.textContent = 'Loading...';
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`);
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.className = 'result success';
                    resultElement.textContent = `✅ Success!\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultElement.className = 'result error';
                    resultElement.textContent = `❌ Error: ${response.status}\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultElement.className = 'result error';
                resultElement.textContent = `❌ Network Error: ${error.message}`;
            }
        }

        // Health Check
        function testHealth() {
            makeRequest('/health', 'health-result');
        }

        // Database Test
        function testDatabase() {
            makeRequest('/test-db', 'health-result');
        }

        // Products Tests
        function testTableStructure() {
            makeRequest('/test-table', 'products-result');
        }

        function testSimpleProducts() {
            makeRequest('/test-products', 'products-result');
        }

        function testProducts() {
            makeRequest('/api/v1/products?per_page=5', 'products-result');
        }

        function testSingleProduct() {
            makeRequest('/api/v1/products/1', 'products-result');
        }

        function testCategories() {
            makeRequest('/api/v1/categories', 'products-result');
        }

        // Search Tests
        function testSearch() {
            makeRequest('/api/v1/products?search=oversized&per_page=5', 'search-result');
        }

        function testCategoryProducts() {
            makeRequest('/api/v1/products/category/Oversized%20Tees?per_page=5', 'search-result');
        }

        function testBestsellers() {
            makeRequest('/api/v1/products?bestseller=true&per_page=10', 'search-result');
        }

        function testNewProducts() {
            makeRequest('/api/v1/products?new=true&per_page=10', 'search-result');
        }

        function testSaleProducts() {
            makeRequest('/api/v1/products?on_sale=true&per_page=10', 'search-result');
        }



        // Auto-run health check on page load
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
