import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, TrendingUp, Percent, Sparkles, ChevronLeft, ChevronRight, Heart, ShoppingBag, Tag, Clock, Zap, Flame, Timer, Gift, ShieldCheck } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
import { dataService } from '../services/dataService';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import WishlistButton from '../components/WishlistButton';

export default function DealsPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [dealProducts, setDealProducts] = useState([]);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  useEffect(() => {
    const loadDeals = async () => {
      try {
        setIsLoading(true);

        // Fetch sale products from database (products with is_sale = 1)
        console.log('🔍 DealsPage: Calling dataService.getSaleProducts');
        const response = await dataService.getSaleProducts({}, 1, 50);
        console.log('🔍 DealsPage: Response from dataService.getSaleProducts:', response);
        
        const saleProducts = response.products || [];
        console.log('🔍 DealsPage: Sale products before filtering:', saleProducts);

        // Calculate discount percentage for each product
        const dealsWithDiscount = saleProducts
          .filter(product => {
            console.log('🔍 DealsPage: Checking product for sale:', product);
            // Check both sale_price and salePrice formats
            const salePrice = product.sale_price || product.salePrice;
            return product && product.price && salePrice && product.price > salePrice;
          })
          .map(product => {
            const price = parseFloat(product.price) || 0;
            const salePrice = parseFloat(product.sale_price) || 0;
            const discount = price > 0 && salePrice > 0 ? Math.round(((price - salePrice) / price) * 100) : 0;
            
            // Ensure is_sale is set to 1 for all products in the deals page
            return {
              ...product,
              is_sale: 1,
              discountPercentage: discount
            };
          })
          .sort((a, b) => b.discountPercentage - a.discountPercentage); // Sort by highest discount first

        console.log('🔍 DealsPage: Filtered deals with discount:', dealsWithDiscount);
        setDealProducts(dealsWithDiscount);
      } catch (error) {
        console.error('Failed to load deals:', error);
        setDealProducts([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadDeals();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-950 pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-12">
            <div className="h-12 bg-slate-800/50 rounded-lg w-64 mx-auto mb-4 animate-pulse" />
            <div className="h-6 bg-slate-800/50 rounded-lg w-96 mx-auto animate-pulse" />
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-slate-900/40 rounded-xl h-80 animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black relative overflow-hidden">

      <div className="relative z-10 pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-8">
          {/* Hero Section */}
          <motion.div
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            {/* Main Title */}
            <div className="relative mb-8">
              {/* <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="absolute -top-4 left-1/2 transform -translate-x-1/2"
              >
                <div className="flex items-center gap-2 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] text-white px-4 py-1 rounded-full text-sm font-bold">
                  <Flame size={14} />
                  <span>FLASH SALE</span>
                  <Flame size={14} />
                </div>
              </motion.div> */}

              <h1 className="text-5xl md:text-7xl lg:text-8xl font-['Bebas_Neue',sans-serif] text-white tracking-wider leading-none">
                <span className="bg-gradient-to-r from-white via-blue-100 to-white bg-clip-text text-transparent">
                  DEALS OF THE DAY
                </span>
              </h1>

              <motion.div
                initial={{ scaleX: 0 }}
                animate={{ scaleX: 1 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="h-1 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] rounded-full w-32 mx-auto mt-4"
              />
            </div>

            {/* Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto mb-8 leading-relaxed"
            >
              Exclusive limited-time offers on premium fashion.
              <span className="text-[#54AEE1] font-semibold"> Up to 40% off </span>
              selected items from top collections.
            </motion.p>

            {/* Stats Row */}
            {/* <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex justify-center items-center gap-3 sm:gap-4 md:gap-6 lg:gap-8 mb-8"
            >
              <div className="flex items-center gap-1.5 sm:gap-2 bg-slate-800/50 backdrop-blur-sm px-3 sm:px-4 py-2 rounded-full border border-slate-700/50">
                <Zap size={16} className="text-yellow-400" />
                <span className="text-white font-medium text-sm sm:text-base">{dealProducts.length} Hot Deals</span>
              </div>
              <div className="flex items-center gap-1.5 sm:gap-2 bg-slate-800/50 backdrop-blur-sm px-3 sm:px-4 py-2 rounded-full border border-slate-700/50">
                <Timer size={16} className="text-red-400" />
                <span className="text-white font-medium text-sm sm:text-base">Limited Time</span>
              </div>
              <div className="flex items-center gap-1.5 sm:gap-2 bg-slate-800/50 backdrop-blur-sm px-3 sm:px-4 py-2 rounded-full border border-slate-700/50">
                <Gift size={16} className="text-green-400" />
                <span className="text-white font-medium text-sm sm:text-base">Free Shipping</span>
              </div>
            </motion.div> */}

            {/* Countdown Timer */}
            {/* <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="inline-flex items-center gap-3 bg-gradient-to-r from-[#214FC3]/20 to-[#54AEE1]/20 backdrop-blur-sm border border-[#214FC3]/30 rounded-2xl px-6 py-4"
            >
              <Clock size={20} className="text-[#54AEE1]" />
              <div className="text-center">
                <div className="text-[#54AEE1] font-bold text-lg">Sale Ends In</div>
                <div className="text-white text-sm">23:59:45</div>
              </div>
            </motion.div> */}
          </motion.div>

          {/* Section Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex items-center justify-between mb-8"
          >
            <div className="flex items-center gap-4">
              <h2 className="text-2xl md:text-3xl font-['Bebas_Neue',sans-serif] text-white tracking-wider">
                FEATURED DEALS
              </h2>
              <div className="flex items-center gap-2 bg-gradient-to-r from-red-500/20 to-orange-500/20 px-3 py-1 rounded-full border border-red-500/30">
                <Flame size={16} className="text-red-400" />
                <span className="text-red-400 text-sm font-medium">Hot Picks</span>
              </div>
            </div>
            <div className="hidden md:flex items-center gap-2 text-gray-400 text-sm">
              <Percent size={16} />
              <span>Up to 40% savings</span>
            </div>
          </motion.div>

          {/* Deals Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6 mb-16"
          >
            {dealProducts.map((product, index) => (
              <DealCard
                key={product.id}
                product={product}
                index={index}
              />
            ))}
          </motion.div>

          {/* Bottom CTA Section */}
          {/* <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="relative"
          >
            <div className="bg-gradient-to-r from-slate-900/80 to-slate-800/80 backdrop-blur-xl rounded-3xl p-8 md:p-12 border border-slate-700/50 text-center relative overflow-hidden">
              {/* Background Pattern 
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0" style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 80 80' width='80' height='80'%3E%3Cg fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='1'%3E%3Cpath d='M20 20h40v40H20z'/%3E%3Cpath d='M40 20v40M20 40h40'/%3E%3C/g%3E%3C/svg%3E")`
                }} />
              </div>

              <div className="relative z-10">
                <div className="flex items-center justify-center gap-3 mb-6">
                  <Sparkles size={24} className="text-[#54AEE1]" />
                  <h3 className="text-3xl md:text-4xl font-['Bebas_Neue',sans-serif] text-white tracking-wider">
                    DON'T MISS OUT
                  </h3>
                  <Sparkles size={24} className="text-[#54AEE1]" />
                </div>

                <p className="text-gray-300 text-lg md:text-xl mb-8 max-w-2xl mx-auto leading-relaxed">
                  These exclusive deals won't last forever. Shop now and save big on your favorite premium styles!
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <Link
                    to="/collections"
                    className="inline-flex items-center gap-3 bg-gradient-to-r from-[#214FC3] to-[#54AEE1] text-white px-8 py-4 rounded-xl font-bold text-lg hover:shadow-lg hover:shadow-[#214FC3]/25 transition-all duration-300 hover:scale-105 group"
                  >
                    <Tag size={20} />
                    Explore All Collections
                    <motion.div
                      className="group-hover:translate-x-1 transition-transform duration-200"
                    >
                      →
                    </motion.div>
                  </Link>

                  <div className="flex items-center gap-2 text-gray-400 text-sm">
                    <ShieldCheck size={16} className="text-green-400" />
                    <span>Free returns • Secure checkout</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div> */}
        </div>
      </div>
    </div>
  );
}

// Deal Card Component - Matching the mobile card design from screenshot
function DealCard({ product, index }) {
  const navigate = useNavigate();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const handleColorSelect = (colorIndex) => {
    setSelectedColorIndex(colorIndex);
    setCurrentImageIndex(0);
  };

  const handleTouchStart = (e) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e) => {
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = (e) => {
    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if ((isLeftSwipe || isRightSwipe) && currentImages.length > 1) {
      e.preventDefault();
      e.stopPropagation();

      if (isLeftSwipe) {
        setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
      }
      if (isRightSwipe) {
        setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
      }
    }
  };

  // Desktop navigation functions
  const goToPrevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (currentImages.length > 1) {
      setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
    }
  };

  const goToNextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (currentImages.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
    }
  };

  const handleCardClick = (e) => {
    // Don't navigate if clicking on interactive elements
    if (e.target.closest('.interactive-element')) {
      return;
    }
    // Navigate to product page using original ID if available
    const productId = product.originalId || product.id;
    navigate(`/product/${productId}`);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] backdrop-blur-sm rounded-2xl overflow-hidden group border border-[#2a2a2a] cursor-pointer"
      onClick={handleCardClick}
    >
      {/* Image Container */}
      <div
        className="h-64 sm:h-72 md:h-80 lg:h-88 overflow-hidden relative"
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Image Carousel */}
        <div className="relative w-full h-full">
          {currentImages.map((image, idx) => (
            <img
              key={idx}
              src={image}
              alt={product.name}
              className={`absolute inset-0 w-full h-full object-cover transition-all duration-500 ${
                idx === currentImageIndex ? 'opacity-100 scale-100' : 'opacity-0 scale-105'
              } group-hover:scale-110`}
            />
          ))}
        </div>

        {/* Discount Badge */}
        <div className="absolute top-3 left-3 bg-gradient-to-r from-red-500 via-red-600 to-rose-600 text-white text-xs font-bold px-2 py-1 rounded-md shadow-lg">
          {product.discountPercentage || 40}% OFF
        </div>

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 interactive-element">
          <WishlistButton
            productId={product.originalId || product.id}
            productName={product.name}
            productPrice={product.is_sale === 1 ? product.sale_price : product.price}
            productImage={currentImages[0] || product.images?.[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] backdrop-blur-sm text-white w-8 h-8 rounded-full flex items-center justify-center transition-all"
          />
        </div>

        {/* Desktop Navigation Arrows - Always visible on desktop, hidden on mobile */}
        {currentImages.length > 1 && (
          <>
            <button
              onClick={goToPrevImage}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-1.5 transition-all duration-300 interactive-element hidden md:flex items-center justify-center"
            >
              <ChevronLeft size={16} />
            </button>
            <button
              onClick={goToNextImage}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-1.5 transition-all duration-300 interactive-element hidden md:flex items-center justify-center"
            >
              <ChevronRight size={16} />
            </button>
          </>
        )}

        {/* Image Dots Indicator */}
        {currentImages.length > 1 && (
          <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex gap-1">
            {currentImages.map((_, idx) => (
              <div
                key={idx}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  idx === currentImageIndex
                    ? 'bg-white'
                    : 'bg-white/40'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-2.5 sm:p-3 md:p-3">
        <div className="mb-2">
          <h3 className="text-[#e2e8f0] font-semibold text-sm sm:text-base md:text-base hover:text-[#3b82f6] transition-colors line-clamp-1 leading-tight truncate" title={product.name}>
            {product.name}
          </h3>
        </div>

        {/* Price */}
        <div className="flex items-center gap-1.5 mb-2">
          {product.is_sale === 1 && product.sale_price ? (
            <>
              <span className="text-white font-semibold text-sm sm:text-base">${parseFloat(product.sale_price).toFixed(2)}</span>
              <span className="text-slate-400 line-through text-xs">${parseFloat(product.price).toFixed(2)}</span>
            </>
          ) : (
            <span className="text-white font-semibold text-sm sm:text-base">${parseFloat(product.price).toFixed(2)}</span>
          )}
        </div>

        {/* Color Selection */}
        <div className="flex items-center gap-1.5">
          <div className="flex gap-1 interactive-element">
            {product.colors.slice(0, 4).map((color, colorIndex) => (
              <button
                key={colorIndex}
                onClick={(e) => {
                  e.stopPropagation();
                  handleColorSelect(colorIndex);
                }}
                className={`w-4 h-4 rounded-full border transition-all duration-200 ${
                  selectedColorIndex === colorIndex
                    ? 'border-white scale-110 shadow-lg shadow-white/25'
                    : 'border-[#404040] hover:border-[#6a6a6a]'
                }`}
                style={{ backgroundColor: color.value }}
                title={color.name}
              />
            ))}
            {product.colors.length > 4 && (
              <div className="w-4 h-4 rounded-full bg-[#2a2a2a] border border-[#404040] flex items-center justify-center">
                <span className="text-[#9a9a9a] text-xs font-medium">+{product.colors.length - 4}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}
