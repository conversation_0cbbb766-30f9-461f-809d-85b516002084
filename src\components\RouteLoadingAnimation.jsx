import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import logo45 from '../assets/logo30.svg';

const RouteLoadingAnimation = ({ onComplete }) => {
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    // Prevent scrolling during animation
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Optimized duration for route changes
    const completeTimer = setTimeout(() => {
      setIsComplete(true);
      // Re-enable scrolling after animation completes
      setTimeout(() => {
        document.body.style.overflow = 'unset';
        document.documentElement.style.overflow = 'unset';
        onComplete?.();
      }, 300);
    }, 1500); // Balanced duration for good branding and speed

    return () => {
      clearTimeout(completeTimer);
      // Ensure scrolling is re-enabled on cleanup
      document.body.style.overflow = 'unset';
      document.documentElement.style.overflow = 'unset';
    };
  }, [onComplete]);

  return (
    <AnimatePresence mode="wait">
      {!isComplete && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{
            duration: 0.3,
            ease: "easeInOut"
          }}
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center overflow-hidden"
          style={{
            backgroundColor: '#000000',
            touchAction: 'none',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          {/* Background */}
          <div className="absolute inset-0">
            <div className="absolute inset-0 bg-gradient-radial from-[#2a2a2a]/10 via-transparent to-transparent" />
          </div>

          {/* Logo and Brand Section - Enhanced for route changes */}
          <div className="relative z-10 flex flex-col items-center px-4 w-full max-w-screen-xl mx-auto">

            {/* Logo Section - Bigger size */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0, y: 20 }}
              animate={{ scale: 1, opacity: 1, y: 0 }}
              exit={{ scale: 0.8, opacity: 0, y: 20 }}
              transition={{
                duration: 0.5,
                ease: "easeOut"
              }}
              className="w-24 h-24 sm:w-32 sm:h-32 md:w-40 md:h-40 lg:w-48 lg:h-48 mb-4 sm:mb-6 flex items-center justify-center"
              style={{
                filter: 'drop-shadow(0 0 10px rgba(106, 106, 106, 0.1))'
              }}
            >
              <motion.img
                animate={{
                  scale: [1, 1.05, 1],
                  filter: ["brightness(1)", "brightness(1.1)", "brightness(1)"]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                src={logo45}
                alt="Wolffoxx Logo"
                className="w-full h-full object-contain"
                style={{
                  filter: 'brightness(1.1) contrast(1.1)'
                }}
              />
            </motion.div>

            {/* WOLFFOXX Text */}
            <motion.div
              initial={{ opacity: 0, y: 15 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 15 }}
              transition={{
                duration: 0.4,
                delay: 0.2,
                ease: "easeOut"
              }}
              className="text-center mb-6"
            >
              <motion.h1
                animate={{
                  letterSpacing: ["0.2em", "0.25em", "0.2em"],
                  textShadow: [
                    "0 0 0px rgba(106, 106, 106, 0)",
                    "0 0 15px rgba(106, 106, 106, 0.1)",
                    "0 0 0px rgba(106, 106, 106, 0)"
                  ]
                }}
                transition={{
                  letterSpacing: { duration: 2, repeat: Infinity, ease: "easeInOut" },
                  textShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                }}
                className="text-white text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-light tracking-[0.2em] leading-tight"
                style={{
                  fontFamily: "'Inter', 'Helvetica Neue', sans-serif",
                  fontWeight: 100,
                  textRendering: 'optimizeLegibility'
                }}
              >
                {"WOLFFOXX".split("").map((letter, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 20, filter: 'blur(2px)' }}
                    animate={{
                      opacity: 1,
                      y: 0,
                      filter: 'blur(0px)'
                    }}
                    transition={{
                      delay: 0.3 + index * 0.05,
                      duration: 0.4,
                      ease: "easeOut"
                    }}
                    className="inline-block"
                  >
                    {letter}
                  </motion.span>
                ))}
              </motion.h1>
            </motion.div>

            {/* Loading indicator */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ delay: 0.5, duration: 0.3 }}
              className="flex gap-1.5"
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.4, 1, 0.4],
                  }}
                  transition={{
                    duration: 0.8,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: "easeInOut"
                  }}
                  className="w-2 h-2 bg-white/50 rounded-full"
                />
              ))}
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default RouteLoadingAnimation;
