import { useState, useEffect, useRef, useCallback } from 'react';
import { getOptimizedImageUrl, getPlaceholderUrl, generateSrcSet } from '../services/imageOptimization';

/**
 * High-Performance Optimized Image Component
 * Features:
 * - Automatic Cloudinary optimization
 * - Lazy loading with Intersection Observer
 * - Progressive loading with blur effect
 * - Responsive images with srcset
 * - Error handling with fallbacks
 */
export default function OptimizedImage({
  src,
  alt,
  className = '',
  context = 'general', // 'thumbnail', 'card', 'hero', 'zoom'
  width,
  height,
  quality = 'auto:good',
  lazy = true,
  showPlaceholder = true,
  onLoad,
  onError,
  ...props
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(!lazy);
  const [hasError, setHasError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState('');
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  // Get optimized URLs
  const optimizedSrc = getOptimizedImageUrl(src, {
    context,
    width,
    height,
    quality
  });

  const placeholderSrc = showPlaceholder ? getPlaceholderUrl(src, {
    context,
    width,
    height
  }) : null;

  const srcSet = generateSrcSet(src, {
    context,
    width,
    height,
    quality
  });

  // Intersection Observer for lazy loading
  const observerCallback = useCallback((entries) => {
    const [entry] = entries;
    if (entry.isIntersecting) {
      setIsInView(true);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    }
  }, []);

  useEffect(() => {
    if (!lazy || isInView) return;

    const currentImg = imgRef.current;
    if (!currentImg) return;

    observerRef.current = new IntersectionObserver(observerCallback, {
      rootMargin: '50px', // Start loading 50px before entering viewport
      threshold: 0.1
    });

    observerRef.current.observe(currentImg);

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [lazy, isInView, observerCallback]);

  // Handle image loading progression
  useEffect(() => {
    if (!isInView) {
      setCurrentSrc(placeholderSrc || '');
      return;
    }

    // Progressive loading: placeholder -> optimized
    if (placeholderSrc && !isLoaded) {
      setCurrentSrc(placeholderSrc);
    }

    // Load the full image
    const img = new Image();
    img.onload = () => {
      setCurrentSrc(optimizedSrc);
      setIsLoaded(true);
      onLoad?.();
    };
    img.onerror = () => {
      setHasError(true);
      setCurrentSrc('/api/placeholder/400/400'); // Fallback image
      onError?.();
    };
    img.src = optimizedSrc;
    if (srcSet) {
      img.srcset = srcSet;
    }
  }, [isInView, optimizedSrc, placeholderSrc, isLoaded, srcSet, onLoad, onError]);

  // Cleanup observer on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  const imageClasses = `
    ${className}
    transition-all duration-500 ease-out
    ${isLoaded ? 'opacity-100' : 'opacity-70'}
    ${showPlaceholder && !isLoaded ? 'blur-sm' : 'blur-0'}
    ${hasError ? 'bg-gray-200' : ''}
  `.trim();

  return (
    <div className="relative overflow-hidden">
      <img
        ref={imgRef}
        src={currentSrc}
        srcSet={isInView && srcSet ? srcSet : undefined}
        sizes={isInView ? "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw" : undefined}
        alt={alt}
        className={imageClasses}
        loading={lazy ? "lazy" : "eager"}
        decoding="async"
        width={width}
        height={height}
        {...props}
      />
      
      {/* Loading indicator */}
      {!isLoaded && isInView && !hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
      
      {/* Error state */}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500">
          <div className="text-center">
            <div className="text-2xl mb-2">📷</div>
            <div className="text-sm">Image unavailable</div>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Specialized components for different contexts
 */
export const ThumbnailImage = (props) => (
  <OptimizedImage {...props} context="thumbnail" />
);

export const CardImage = (props) => (
  <OptimizedImage {...props} context="card" />
);

export const HeroImage = (props) => (
  <OptimizedImage {...props} context="hero" lazy={false} />
);

export const ZoomImage = (props) => (
  <OptimizedImage {...props} context="zoom" quality="auto:best" />
);
