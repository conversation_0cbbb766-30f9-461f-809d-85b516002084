import { motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import SavedOutfits from '../components/SavedOutfits';

const OutfitsPage = () => {
  const location = useLocation();
  const highlightOutfitId = location.state?.highlightOutfitId;

  return (
    <div className="min-h-screen bg-[#000000] pt-24 pb-16">
      <div className="container mx-auto px-4 md:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <motion.h1
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-4xl md:text-5xl font-['Bebas_Neue',sans-serif] text-white mb-4"
          >
            YOUR OUTFITS
          </motion.h1>
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="text-[#AAAAAA] max-w-2xl mx-auto"
          >
            Manage and shop your saved outfit collections. Create perfect looks and add them to your cart with one click.
          </motion.p>
        </div>

        {/* Saved Outfits Component */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <SavedOutfits highlightOutfitId={highlightOutfitId} />
        </motion.div>
      </div>
    </div>
  );
};

export default OutfitsPage;
