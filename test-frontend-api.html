<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend API Integration Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        pre {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
    </style>
</head>
<body>
    <h1>🔗 Frontend API Integration Test</h1>
    <p>Testing if your React frontend can connect to your PHP backend API</p>

    <div class="test-section">
        <h2>📦 Products API</h2>
        <button onclick="testGetProducts()">Get All Products</button>
        <button onclick="testGetProduct()">Get Single Product (ID: 1)</button>
        <button onclick="testGetCategories()">Get Categories</button>
        <button onclick="testSearchProducts()">Search Products</button>
        <div id="products-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🎯 Filtered Products</h2>
        <button onclick="testBestsellers()">Get Bestsellers</button>
        <button onclick="testNewProducts()">Get New Products</button>
        <button onclick="testSaleProducts()">Get Sale Products</button>
        <div id="filtered-result" class="result"></div>
    </div>

    <script type="module">
        // Import your frontend API service
        const API_BASE_URL = 'http://localhost/wolffoxx/backend/public/api/v1';

        // Simple HTTP client
        async function apiCall(endpoint) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`);
                const data = await response.json();
                return { success: true, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // Test functions
        window.testGetProducts = async () => {
            const result = await apiCall('/products?per_page=5');
            displayResult('products-result', result, 'Get Products');
        };

        window.testGetProduct = async () => {
            const result = await apiCall('/products/1');
            displayResult('products-result', result, 'Get Single Product');
        };

        window.testGetCategories = async () => {
            const result = await apiCall('/categories');
            displayResult('products-result', result, 'Get Categories');
        };

        window.testSearchProducts = async () => {
            const result = await apiCall('/products?search=oversized&per_page=5');
            displayResult('products-result', result, 'Search Products');
        };

        window.testBestsellers = async () => {
            const result = await apiCall('/products?bestseller=true&per_page=5');
            displayResult('filtered-result', result, 'Get Bestsellers');
        };

        window.testNewProducts = async () => {
            const result = await apiCall('/products?new=true&per_page=5');
            displayResult('filtered-result', result, 'Get New Products');
        };

        window.testSaleProducts = async () => {
            const result = await apiCall('/products?on_sale=true&per_page=5');
            displayResult('filtered-result', result, 'Get Sale Products');
        };

        function displayResult(elementId, result, testName) {
            const element = document.getElementById(elementId);
            
            if (result.success) {
                element.className = 'result success';
                element.innerHTML = `
                    <h3>✅ ${testName} - SUCCESS</h3>
                    <p><strong>Data Structure:</strong></p>
                    <pre>${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                element.className = 'result error';
                element.innerHTML = `
                    <h3>❌ ${testName} - FAILED</h3>
                    <p><strong>Error:</strong> ${result.error}</p>
                `;
            }
        }

        // Auto-run basic test
        window.onload = () => {
            testGetProducts();
        };
    </script>
</body>
</html>
