<?php

/**
 * Database Migration Script
 * 
 * Runs all pending database migrations in order.
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Utils\Logger;

class MigrationRunner
{
    private Logger $logger;
    private string $migrationsPath;
    private array $executedMigrations = [];

    public function __construct()
    {
        $this->logger = new Logger('migration');
        $this->migrationsPath = __DIR__ . '/../database/migrations/';
        
        // Initialize database
        Database::init();
        
        // Create migrations table if it doesn't exist
        $this->createMigrationsTable();
        
        // Load executed migrations
        $this->loadExecutedMigrations();
    }

    /**
     * Run all pending migrations
     */
    public function run(): void
    {
        echo "🚀 Starting database migrations...\n\n";

        $migrationFiles = $this->getMigrationFiles();
        $pendingMigrations = $this->getPendingMigrations($migrationFiles);

        if (empty($pendingMigrations)) {
            echo "✅ No pending migrations found.\n";
            return;
        }

        echo "📋 Found " . count($pendingMigrations) . " pending migration(s):\n";
        foreach ($pendingMigrations as $migration) {
            echo "   - {$migration}\n";
        }
        echo "\n";

        foreach ($pendingMigrations as $migration) {
            $this->runMigration($migration);
        }

        echo "\n🎉 All migrations completed successfully!\n";
    }

    /**
     * Create migrations tracking table
     */
    private function createMigrationsTable(): void
    {
        $sql = "CREATE TABLE IF NOT EXISTS migrations (
            id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
            migration VARCHAR(255) NOT NULL UNIQUE,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        try {
            Database::execute($sql);
            $this->logger->info('Migrations table created/verified');
        } catch (Exception $e) {
            $this->logger->error('Failed to create migrations table', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Load list of executed migrations
     */
    private function loadExecutedMigrations(): void
    {
        try {
            $sql = "SELECT migration FROM migrations ORDER BY id";
            $statement = Database::execute($sql);
            $results = $statement->fetchAll();

            $this->executedMigrations = array_column($results, 'migration');
        } catch (Exception $e) {
            $this->logger->error('Failed to load executed migrations', [
                'error' => $e->getMessage()
            ]);
            $this->executedMigrations = [];
        }
    }

    /**
     * Get all migration files
     */
    private function getMigrationFiles(): array
    {
        if (!is_dir($this->migrationsPath)) {
            throw new Exception("Migrations directory not found: {$this->migrationsPath}");
        }

        $files = glob($this->migrationsPath . '*.sql');
        
        if ($files === false) {
            throw new Exception("Failed to read migrations directory");
        }

        // Sort files to ensure proper execution order
        sort($files);

        // Extract just the filenames
        return array_map('basename', $files);
    }

    /**
     * Get pending migrations
     */
    private function getPendingMigrations(array $migrationFiles): array
    {
        return array_filter($migrationFiles, function($file) {
            return !in_array($file, $this->executedMigrations);
        });
    }

    /**
     * Run a single migration
     */
    private function runMigration(string $migrationFile): void
    {
        $filePath = $this->migrationsPath . $migrationFile;
        
        if (!file_exists($filePath)) {
            throw new Exception("Migration file not found: {$filePath}");
        }

        echo "⚡ Running migration: {$migrationFile}... ";

        try {
            // Read migration file
            $sql = file_get_contents($filePath);
            
            if ($sql === false) {
                throw new Exception("Failed to read migration file: {$filePath}");
            }

            // Begin transaction
            Database::beginTransaction();

            // Split SQL into individual statements
            $statements = $this->splitSqlStatements($sql);

            // Execute each statement
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    Database::execute($statement);
                }
            }

            // Record migration as executed
            $this->recordMigration($migrationFile);

            // Commit transaction
            Database::commit();

            echo "✅ Success\n";

            $this->logger->info('Migration executed successfully', [
                'migration' => $migrationFile
            ]);

        } catch (Exception $e) {
            // Rollback transaction
            Database::rollback();

            echo "❌ Failed\n";
            echo "Error: {$e->getMessage()}\n";

            $this->logger->error('Migration failed', [
                'migration' => $migrationFile,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Split SQL file into individual statements
     */
    private function splitSqlStatements(string $sql): array
    {
        // Remove comments
        $sql = preg_replace('/--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);

        // Split by semicolon (simple approach)
        $statements = explode(';', $sql);

        // Filter out empty statements
        return array_filter($statements, function($statement) {
            return !empty(trim($statement));
        });
    }

    /**
     * Record migration as executed
     */
    private function recordMigration(string $migrationFile): void
    {
        $sql = "INSERT INTO migrations (migration) VALUES (?)";
        Database::execute($sql, [$migrationFile]);
    }

    /**
     * Show migration status
     */
    public function status(): void
    {
        echo "📊 Migration Status:\n\n";

        $migrationFiles = $this->getMigrationFiles();
        
        if (empty($migrationFiles)) {
            echo "No migration files found.\n";
            return;
        }

        foreach ($migrationFiles as $file) {
            $status = in_array($file, $this->executedMigrations) ? '✅ Executed' : '⏳ Pending';
            echo "   {$file} - {$status}\n";
        }

        echo "\n";
        echo "Total migrations: " . count($migrationFiles) . "\n";
        echo "Executed: " . count($this->executedMigrations) . "\n";
        echo "Pending: " . (count($migrationFiles) - count($this->executedMigrations)) . "\n";
    }
}

// Main execution
try {
    $runner = new MigrationRunner();

    // Check command line arguments
    $command = $argv[1] ?? 'run';

    switch ($command) {
        case 'status':
            $runner->status();
            break;
        
        case 'run':
        default:
            $runner->run();
            break;
    }

} catch (Exception $e) {
    echo "\n❌ Migration failed: {$e->getMessage()}\n";
    exit(1);
}
