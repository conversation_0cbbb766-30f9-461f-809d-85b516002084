-- Create wishlists table for user wishlist management
CREATE TABLE IF NOT EXISTS wishlists (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    product_id INT UNSIGNED NOT NULL,
    
    -- Additional wishlist metadata
    notes TEXT,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    is_public BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicates
    UNIQUE KEY unique_user_product (user_id, product_id),
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_priority (priority),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create wishlist_collections table for organized wishlist groups
CREATE TABLE IF NOT EXISTS wishlist_collections (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    
    -- Collection settings
    color VARCHAR(7) DEFAULT '#3b82f6',
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_is_default (is_default),
    INDEX idx_is_public (is_public),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create wishlist_collection_items table for items in collections
CREATE TABLE IF NOT EXISTS wishlist_collection_items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    collection_id INT UNSIGNED NOT NULL,
    wishlist_id INT UNSIGNED NOT NULL,
    sort_order INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (collection_id) REFERENCES wishlist_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (wishlist_id) REFERENCES wishlists(id) ON DELETE CASCADE,
    
    -- Unique constraint
    UNIQUE KEY unique_collection_wishlist (collection_id, wishlist_id),
    
    -- Indexes
    INDEX idx_collection_id (collection_id),
    INDEX idx_wishlist_id (wishlist_id),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create wishlist_shares table for sharing wishlists
CREATE TABLE IF NOT EXISTS wishlist_shares (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    wishlist_id INT UNSIGNED NULL,
    collection_id INT UNSIGNED NULL,
    share_token VARCHAR(255) NOT NULL UNIQUE,
    shared_by_user_id INT UNSIGNED NOT NULL,
    
    -- Share settings
    is_active BOOLEAN DEFAULT TRUE,
    allow_comments BOOLEAN DEFAULT TRUE,
    password_protected BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255) NULL,
    
    -- Access tracking
    view_count INT DEFAULT 0,
    last_viewed_at TIMESTAMP NULL,
    
    -- Expiration
    expires_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (wishlist_id) REFERENCES wishlists(id) ON DELETE CASCADE,
    FOREIGN KEY (collection_id) REFERENCES wishlist_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Constraints
    CHECK ((wishlist_id IS NOT NULL AND collection_id IS NULL) OR 
           (wishlist_id IS NULL AND collection_id IS NOT NULL)),
    
    -- Indexes
    INDEX idx_share_token (share_token),
    INDEX idx_wishlist_id (wishlist_id),
    INDEX idx_collection_id (collection_id),
    INDEX idx_shared_by_user_id (shared_by_user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create wishlist_notifications table for price drop alerts
CREATE TABLE IF NOT EXISTS wishlist_notifications (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    wishlist_id INT UNSIGNED NOT NULL,
    notification_type ENUM('price_drop', 'back_in_stock', 'sale_alert') NOT NULL,
    
    -- Notification settings
    is_enabled BOOLEAN DEFAULT TRUE,
    threshold_price DECIMAL(10,2) NULL,
    threshold_percentage DECIMAL(5,2) NULL,
    
    -- Notification tracking
    last_notified_at TIMESTAMP NULL,
    notification_count INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (wishlist_id) REFERENCES wishlists(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_wishlist_id (wishlist_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_last_notified_at (last_notified_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create wishlist_analytics table for tracking wishlist behavior
CREATE TABLE IF NOT EXISTS wishlist_analytics (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    product_id INT UNSIGNED NOT NULL,
    action ENUM('added', 'removed', 'viewed', 'shared', 'purchased') NOT NULL,
    
    -- Context data
    source VARCHAR(100), -- 'product_page', 'category_page', 'search', etc.
    device_type VARCHAR(50),
    session_id VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_action (action),
    INDEX idx_source (source),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
