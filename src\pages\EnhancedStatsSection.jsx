import { useEffect, useRef, useState } from 'react';

const ProfessionalStatsSection = () => {
  const [hasAnimated, setHasAnimated] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observerOptions = {
      threshold: 0.3,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !hasAnimated) {
          const counters = entry.target.querySelectorAll('[data-target]');
          counters.forEach((counter, index) => {
            setTimeout(() => {
              animateCounter(counter);
            }, index * 200); // Staggered animation
          });
          setHasAnimated(true);
        }
      });
    }, observerOptions);

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, [hasAnimated]);

  const animateCounter = (element) => {
    const target = parseInt(element.getAttribute('data-target'));
    const suffix = element.getAttribute('data-suffix') || '';
    const prefix = element.getAttribute('data-prefix') || '';
    const isDecimal = element.getAttribute('data-decimal') === 'true';
    const duration = 2500;
    const startTime = performance.now();

    function updateCounter(currentTime) {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      // Smooth easing function
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      let currentValue;
      if (isDecimal) {
        currentValue = (target / 10 * easeOutQuart).toFixed(1);
        element.textContent = prefix + currentValue + suffix;
      } else if (target >= 1000) {
        const value = Math.floor(target * easeOutQuart);
        const displayValue = Math.floor(value / 1000);
        element.textContent = prefix + displayValue + suffix;
      } else {
        currentValue = Math.floor(target * easeOutQuart);
        element.textContent = prefix + currentValue + suffix;
      }

      if (progress < 1) {
        requestAnimationFrame(updateCounter);
      }
    }

    requestAnimationFrame(updateCounter);
  };

  const statsData = [
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      value: "50000",
      suffix: "K+",
      label: "Happy Customers",
      description: "Satisfied customers worldwide",
      color: "from-blue-600 to-indigo-600"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
      value: "250000",
      suffix: "K+",
      label: "Products Sold",
      description: "Items delivered successfully",
      color: "from-emerald-600 to-teal-600"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
        </svg>
      ),
      value: "48",
      suffix: "/5",
      decimal: true,
      label: "Customer Rating",
      description: "Average review score",
      color: "from-amber-500 to-orange-600"
    },
    {
      icon: (
        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
      ),
      value: "92",
      suffix: "%",
      label: "Return Rate",
      description: "Customer retention",
      color: "from-purple-600 to-pink-600"
    }
  ];

  return (
    <section
      ref={sectionRef}
      className="pt-6 pb-12 sm:pt-8 sm:pb-16 md:pb-20 lg:pb-24 bg-black relative overflow-hidden"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header - Optimized spacing for mobile */}
        <div className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12">
          <h2 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-white mb-3 sm:mb-4 tracking-tight leading-tight">
            TRUSTED BY <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">THOUSANDS</span>
          </h2>

          <p className="text-xs sm:text-sm md:text-base lg:text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed font-light px-2 sm:px-0">
            Join our growing community of satisfied customers who trust us for quality products and exceptional service.
          </p>
        </div>

        {/* Mobile-First Responsive Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 md:gap-6 lg:gap-8">
          {statsData.map((stat, index) => (
            <div
              key={index}
              className="group relative bg-[#0a0a0a] backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 shadow-2xl hover:shadow-[#404040]/20 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 md:hover:-translate-y-2 border border-[#2a2a2a] hover:border-[#404040]"
            >
              {/* Icon Container - Smaller on mobile */}
              <div className={`inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 md:w-16 md:h-16 rounded-xl md:rounded-2xl bg-gradient-to-r ${stat.color} mb-3 sm:mb-4 md:mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                <div className="text-white scale-75 sm:scale-90 md:scale-100">
                  {stat.icon}
                </div>
              </div>

              {/* Counter - Optimized for mobile */}
              <div className="mb-2 sm:mb-3 md:mb-4">
                <h3
                  className="text-lg sm:text-xl md:text-2xl lg:text-3xl xl:text-4xl font-bold text-white mb-1 sm:mb-2 tracking-tight leading-tight"
                  data-target={stat.value}
                  data-suffix={stat.suffix}
                  data-decimal={stat.decimal ? 'true' : 'false'}
                >
                  0{stat.suffix}
                </h3>
                <p className="text-xs sm:text-sm md:text-sm lg:text-base font-semibold text-gray-200 mb-1 leading-tight">
                  {stat.label}
                </p>
                <p className="text-xs sm:text-xs md:text-sm text-gray-400 leading-tight">
                  {stat.description}
                </p>
              </div>

              {/* Hover Effect Overlay */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-600/10 to-indigo-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        {/* Bottom Section - Mobile optimized */}
        {/* <div className="text-center mt-8 sm:mt-12 md:mt-16">
          <div className="inline-flex items-center space-x-2 sm:space-x-3 text-gray-400 text-xs sm:text-sm font-medium bg-[#1a1a1a] backdrop-blur-sm rounded-full px-4 sm:px-6 py-2 sm:py-3 shadow-lg border border-[#2a2a2a]">
            <div className="flex space-x-1">
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-emerald-500 rounded-full animate-pulse" style={{animationDelay: '0.2s'}}></div>
              <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-purple-500 rounded-full animate-pulse" style={{animationDelay: '0.4s'}}></div>
            </div>
            <span className="leading-tight">Growing every day with your trust</span>
          </div>
        </div> */}
      </div>
    </section>
  );
};

export default ProfessionalStatsSection;