# 🧪 Authentication & Profile Testing Guide

Complete testing guide for the OTP-based authentication system and user profile management.

## 🚀 **Quick Setup for Testing**

### **1. Environment Setup**
```bash
# Copy environment file
cp .env.example .env

# Update API URL in .env
VITE_API_BASE_URL=http://localhost:8000/api/v1

# Start frontend
npm run dev
```

### **2. Backend Setup**
```bash
# Ensure backend is running
cd backend
php -S localhost:8000 -t public

# Or use your preferred server setup
```

## 📱 **Phase 1: Authentication Flow Testing**

### **Test 1: OTP Login Flow**

#### **1.1 Phone Number Entry**
- **URL**: `http://localhost:3000/login`
- **Test Cases**:
  ```
  ✅ Valid: 9876543210
  ✅ Valid: +91 9876543210  
  ✅ Valid: 91-9876-543210
  ❌ Invalid: 123456789 (too short)
  ❌ Invalid: 12345678901 (too long)
  ❌ Invalid: abcd123456 (contains letters)
  ```

#### **1.2 OTP Verification**
- **Expected**: 6-digit OTP input fields
- **Test Cases**:
  ```
  ✅ Valid: 123456
  ✅ Auto-focus: Next field on digit entry
  ✅ Backspace: Previous field on backspace
  ❌ Invalid: 12345 (incomplete)
  ❌ Invalid: abcdef (letters)
  ```

#### **1.3 Resend OTP**
- **Expected**: 60-second timer before resend
- **Test**: Click resend after timer expires

### **Test 2: Authentication States**

#### **2.1 Unauthenticated State**
```bash
# Test these URLs when logged out:
http://localhost:3000/profile → Redirects to /login
http://localhost:3000/login → Shows login form
```

#### **2.2 Authenticated State**
```bash
# Test these URLs when logged in:
http://localhost:3000/login → Redirects to /
http://localhost:3000/profile → Shows profile page
```

## 👤 **Phase 2: Profile Management Testing**

### **Test 3: Profile Page Features**

#### **3.1 Profile Display**
- **URL**: `http://localhost:3000/profile`
- **Expected Elements**:
  ```
  ✅ User name display
  ✅ Phone number (formatted: +91 98765 43210)
  ✅ Profile image placeholder
  ✅ Quick stats (Wishlist, Cart, Orders, Outfits)
  ✅ Edit profile button
  ✅ Logout button
  ```

#### **3.2 Profile Editing**
- **Test Cases**:
  ```
  ✅ Edit first name
  ✅ Edit last name  
  ✅ Add/edit email
  ✅ Set date of birth
  ✅ Select gender
  ✅ Save changes
  ✅ Cancel editing
  ```

#### **3.3 Form Validation**
```bash
# Test these validation scenarios:
✅ Required: First name cannot be empty
✅ Required: Last name cannot be empty
✅ Email: Valid email format (<EMAIL>)
✅ Date: Valid date format
❌ Invalid: Empty first name
❌ Invalid: Invalid email format
```

## 🔒 **Phase 3: Security Testing**

### **Test 4: Protected Routes**

#### **4.1 Route Protection**
```bash
# Test without authentication:
/profile → Should redirect to /login
/admin → Should redirect to /login (if implemented)

# Test with authentication:
/login → Should redirect to / or intended page
/profile → Should show profile page
```

#### **4.2 Token Management**
```bash
# Test in browser DevTools → Application → Local Storage:
✅ Tokens stored: wolffoxx_tokens
✅ User data stored: wolffoxx_user
✅ Auto-login: Refresh page maintains login
✅ Logout: Clears stored data
```

### **Test 5: API Integration**

#### **5.1 Network Requests**
```bash
# Monitor in DevTools → Network tab:
✅ POST /auth/otp/send
✅ POST /auth/otp/verify  
✅ POST /auth/otp/resend
✅ PUT /auth/otp/profile
✅ GET /users/profile
✅ POST /auth/logout
```

#### **5.2 Error Handling**
```bash
# Test error scenarios:
✅ Network error: Offline mode
✅ Server error: 500 response
✅ Invalid OTP: Wrong code
✅ Expired OTP: Old code
✅ Rate limiting: Too many requests
```

## 📱 **Phase 4: Mobile Responsiveness Testing**

### **Test 6: Mobile UX**

#### **6.1 Touch Targets**
```bash
# All interactive elements should be ≥44px:
✅ Login buttons
✅ OTP input fields
✅ Profile edit buttons
✅ Navigation links
✅ Form inputs
```

#### **6.2 Mobile Layout**
```bash
# Test on different screen sizes:
✅ 320px (iPhone SE)
✅ 375px (iPhone 12)
✅ 414px (iPhone 12 Pro Max)
✅ 768px (iPad)
✅ 1024px (Desktop)
```

#### **6.3 Mobile Features**
```bash
✅ Keyboard: Numeric keypad for phone/OTP
✅ Auto-focus: Proper input focus flow
✅ Viewport: No horizontal scroll
✅ Touch: Smooth interactions
✅ Loading: Proper loading states
```

## 🎨 **Phase 5: Design Consistency Testing**

### **Test 7: Theme Compliance**

#### **7.1 Color Scheme**
```bash
✅ Background: Black (#000000, #0a0a0a, #1a1a1a)
✅ Borders: Gray (#2a2a2a, #404040)
✅ Text: White, #AAAAAA, #6a6a6a
✅ Accent: Blue gradients
✅ Success: Green (#22c55e)
✅ Error: Red (#ef4444)
```

#### **7.2 Component Styling**
```bash
✅ Buttons: Blue gradient with hover effects
✅ Inputs: Dark background with blue focus
✅ Cards: Dark background with borders
✅ Loading: Consistent spinner design
✅ Animations: Smooth transitions
```

## 🔄 **Phase 6: Integration Testing**

### **Test 8: Context Integration**

#### **8.1 Auth Context**
```bash
✅ Login updates auth state
✅ Logout clears auth state
✅ Profile updates user data
✅ Token refresh works
✅ Error handling works
```

#### **8.2 Other Contexts**
```bash
✅ Cart: Maintains items after login
✅ Wishlist: Maintains items after login
✅ Outfits: Maintains data after login
```

### **Test 9: Navigation Integration**

#### **9.1 Navbar Updates**
```bash
✅ Shows login icon when logged out
✅ Shows user icon when logged in
✅ Displays user name in mobile menu
✅ Profile link works correctly
```

#### **9.2 Route Transitions**
```bash
✅ Smooth page transitions
✅ Proper loading states
✅ Correct redirects
✅ Back button works
```

## 🐛 **Phase 7: Error Scenarios Testing**

### **Test 10: Error Handling**

#### **10.1 Network Errors**
```bash
# Simulate these conditions:
✅ No internet connection
✅ Server timeout
✅ 500 server error
✅ 404 not found
✅ Invalid JSON response
```

#### **10.2 User Errors**
```bash
✅ Invalid phone number
✅ Wrong OTP code
✅ Expired OTP
✅ Empty form fields
✅ Invalid email format
```

#### **10.3 Edge Cases**
```bash
✅ Multiple rapid clicks
✅ Browser back/forward
✅ Page refresh during flow
✅ Multiple tabs open
✅ Session timeout
```

## ✅ **Testing Checklist**

### **Authentication Flow**
- [ ] Phone number validation
- [ ] OTP sending
- [ ] OTP verification
- [ ] Auto-registration for new users
- [ ] Login for existing users
- [ ] Logout functionality

### **Profile Management**
- [ ] Profile display
- [ ] Profile editing
- [ ] Form validation
- [ ] Data persistence
- [ ] Image upload (if implemented)

### **Security**
- [ ] Route protection
- [ ] Token management
- [ ] Data encryption
- [ ] Session handling
- [ ] Error messages don't leak info

### **Mobile Experience**
- [ ] Touch targets ≥44px
- [ ] Responsive layout
- [ ] Proper keyboards
- [ ] Smooth interactions
- [ ] Loading states

### **Design Consistency**
- [ ] Color scheme compliance
- [ ] Typography consistency
- [ ] Component styling
- [ ] Animation smoothness
- [ ] Theme integration

### **Integration**
- [ ] Context state management
- [ ] API connectivity
- [ ] Navigation flow
- [ ] Error handling
- [ ] Performance

## 🚨 **Common Issues & Solutions**

### **Issue 1: OTP Not Received**
```bash
# Check:
1. Backend SMS configuration
2. Phone number format
3. Rate limiting
4. SMS provider status
5. Development mode logs
```

### **Issue 2: Login Redirect Loop**
```bash
# Check:
1. Token validation
2. Route protection logic
3. Authentication state
4. Local storage data
5. API response format
```

### **Issue 3: Profile Not Loading**
```bash
# Check:
1. API endpoint availability
2. Token in request headers
3. User data format
4. Network connectivity
5. Error handling
```

### **Issue 4: Mobile Layout Issues**
```bash
# Check:
1. Viewport meta tag
2. CSS media queries
3. Touch target sizes
4. Keyboard behavior
5. Scroll behavior
```

## 📊 **Performance Testing**

### **Metrics to Monitor**
```bash
✅ Page load time: <2 seconds
✅ API response time: <500ms
✅ Bundle size: Optimized
✅ Memory usage: No leaks
✅ Battery usage: Minimal
```

### **Tools for Testing**
```bash
✅ Chrome DevTools
✅ Lighthouse
✅ React DevTools
✅ Network throttling
✅ Mobile device testing
```

## 🎯 **Success Criteria**

### **Functional Requirements**
- [ ] Users can login with phone + OTP
- [ ] New users auto-register
- [ ] Profile management works
- [ ] Protected routes function
- [ ] Logout clears session

### **Non-Functional Requirements**
- [ ] Mobile-first responsive design
- [ ] <2 second page load times
- [ ] Accessible UI components
- [ ] Consistent theme application
- [ ] Smooth animations

### **User Experience**
- [ ] Intuitive login flow
- [ ] Clear error messages
- [ ] Proper loading states
- [ ] Seamless navigation
- [ ] Professional appearance

**🎉 Ready to test your authentication system!**
