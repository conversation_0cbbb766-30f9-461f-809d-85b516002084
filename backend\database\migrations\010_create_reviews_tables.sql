-- Create reviews table
CREATE TABLE IF NOT EXISTS reviews (
    id INT AUTO_INCREMENT PRIMARY KEY,
    uuid VARCHAR(36) NOT NULL UNIQUE,
    product_id INT NOT NULL,
    user_id INT NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255) NOT NULL,
    comment TEXT NOT NULL,
    selected_color VARCHAR(50) NULL,
    selected_size VARCHAR(20) NULL,
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    helpful_votes INT DEFAULT 0,
    total_votes INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_product_id (product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_created_at (created_at),
    INDEX idx_is_approved (is_approved),
    
    UNIQUE KEY unique_user_product (user_id, product_id)
);

-- Create review_votes table for helpful/not helpful votes
CREATE TABLE IF NOT EXISTS review_votes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    review_id INT NOT NULL,
    user_id INT NOT NULL,
    vote ENUM('helpful', 'not_helpful') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_review_vote (user_id, review_id),
    INDEX idx_review_id (review_id),
    INDEX idx_vote (vote)
);

-- Create review_flags table for reporting inappropriate reviews
CREATE TABLE IF NOT EXISTS review_flags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    review_id INT NOT NULL,
    user_id INT NOT NULL,
    reason VARCHAR(100) NOT NULL DEFAULT 'inappropriate',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    UNIQUE KEY unique_user_review_flag (user_id, review_id),
    INDEX idx_review_id (review_id),
    INDEX idx_reason (reason)
);

-- Add review columns to products table if they don't exist
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS total_reviews INT DEFAULT 0;

-- Create indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_products_average_rating ON products(average_rating);
CREATE INDEX IF NOT EXISTS idx_products_total_reviews ON products(total_reviews);

-- Insert some sample reviews for testing
INSERT IGNORE INTO reviews (uuid, product_id, user_id, rating, title, comment, selected_color, selected_size, is_verified_purchase, created_at) VALUES
(UUID(), 1, 1, 5, 'Amazing quality!', 'This t-shirt is incredibly soft and comfortable. The fit is perfect and the color is exactly as shown in the pictures. Highly recommended!', 'Black', 'M', TRUE, DATE_SUB(NOW(), INTERVAL 5 DAY)),
(UUID(), 1, 2, 4, 'Good value for money', 'Nice t-shirt overall. The material is good quality and it washes well. Only minor complaint is that it runs slightly small.', 'White', 'L', TRUE, DATE_SUB(NOW(), INTERVAL 3 DAY)),
(UUID(), 2, 1, 5, 'Perfect hoodie!', 'Love this hoodie! Super warm and cozy. The design is stylish and it fits true to size. Will definitely buy more colors.', 'Gray', 'M', TRUE, DATE_SUB(NOW(), INTERVAL 2 DAY)),
(UUID(), 2, 3, 4, 'Comfortable and stylish', 'Great hoodie for the price. Material is soft and the print quality is excellent. Delivery was fast too.', 'Black', 'L', FALSE, DATE_SUB(NOW(), INTERVAL 1 DAY)),
(UUID(), 3, 2, 3, 'Decent shirt', 'The shirt is okay. Material could be better for the price but the design is nice. Fits as expected.', 'Blue', 'M', TRUE, DATE_SUB(NOW(), INTERVAL 4 DAY)),
(UUID(), 3, 3, 5, 'Excellent formal shirt', 'Perfect for office wear. The fabric is premium quality and the cut is very professional. Worth every penny!', 'White', 'L', TRUE, DATE_SUB(NOW(), INTERVAL 6 DAY));

-- Insert some sample review votes
INSERT IGNORE INTO review_votes (review_id, user_id, vote) VALUES
(1, 2, 'helpful'),
(1, 3, 'helpful'),
(2, 1, 'helpful'),
(2, 3, 'not_helpful'),
(3, 2, 'helpful'),
(4, 1, 'helpful'),
(5, 1, 'not_helpful'),
(6, 1, 'helpful'),
(6, 2, 'helpful');

-- Update products table with calculated review stats
UPDATE products p SET 
    average_rating = (
        SELECT COALESCE(AVG(rating), 0) 
        FROM reviews r 
        WHERE r.product_id = p.id AND r.is_approved = 1
    ),
    total_reviews = (
        SELECT COUNT(*) 
        FROM reviews r 
        WHERE r.product_id = p.id AND r.is_approved = 1
    );
