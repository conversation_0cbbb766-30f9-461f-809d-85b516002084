import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Star, TrendingUp, Crown, Sparkles, ChevronLeft, ChevronRight, Heart, ShoppingBag } from 'lucide-react';
import { Link } from 'react-router-dom';
import { dataService } from '../services/dataService';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import WishlistButton from '../components/WishlistButton';

export default function BestSellersPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [bestSellersByCategory, setBestSellersByCategory] = useState({});
  const { addToCart } = useCart();

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  useEffect(() => {
    const loadBestSellers = async () => {
      try {
        setIsLoading(true);

        // Get bestseller products from database
        const bestSellers = await dataService.getBestsellerProducts(50);

        // Group by category and limit to 3-4 products per category
        const grouped = bestSellers.reduce((acc, product) => {
          if (!acc[product.category]) {
            acc[product.category] = [];
          }
          if (acc[product.category].length < 4) {
            acc[product.category].push(product);
          }
          return acc;
        }, {});

        setBestSellersByCategory(grouped);
      } catch (error) {
        console.error('Failed to load bestsellers:', error);
        setBestSellersByCategory({});
      } finally {
        setIsLoading(false);
      }
    };

    loadBestSellers();
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-950 pt-24 pb-16">
        <div className="container mx-auto px-4 md:px-8">
          <div className="text-center mb-12">
            <div className="h-12 bg-slate-800/50 rounded-lg w-64 mx-auto mb-4 animate-pulse" />
            <div className="h-6 bg-slate-800/50 rounded-lg w-96 mx-auto animate-pulse" />
          </div>
          <div className="space-y-12">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-6">
                <div className="h-8 bg-slate-800/50 rounded-lg w-48 animate-pulse" />
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {[...Array(4)].map((_, j) => (
                    <div key={j} className="bg-slate-900/40 rounded-xl h-80 animate-pulse" />
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black pt-24 pb-16">
      <div className="container mx-auto px-4 md:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full">
              <Crown size={24} className="text-white" />
            </div>
            <h1 className="text-4xl md:text-5xl font-['Bebas_Neue',sans-serif] text-white">
              BEST SELLERS
            </h1>
            <div className="p-3 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full">
              <Star size={24} className="text-white fill-white" />
            </div>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto text-lg">
            Discover our most popular products across all categories. These customer favorites are flying off the shelves!
          </p>
        </motion.div>

        {/* Best Sellers by Category */}
        <div className="space-y-16">
          {Object.entries(bestSellersByCategory).map(([category, categoryProducts], categoryIndex) => (
            <motion.div
              key={category}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: categoryIndex * 0.1 }}
              className="space-y-6"
            >
              {/* Category Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h2 className="text-2xl md:text-3xl font-['Bebas_Neue',sans-serif] text-white tracking-wider">
                    {category}
                  </h2>
                  <div className="flex items-center gap-2 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 px-3 py-1 rounded-full border border-yellow-500/30">
                    <TrendingUp size={16} className="text-yellow-400" />
                    <span className="text-yellow-400 text-sm font-medium">Top Picks</span>
                  </div>
                </div>
                <Link
                  to={`/category/${category.toLowerCase().replace(/\s+/g, '-')}`}
                  className="text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium"
                >
                  View All →
                </Link>
              </div>

              {/* Products Grid */}
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
                {categoryProducts.map((product, index) => (
                  <BestSellerCard
                    key={product.id}
                    product={product}
                    index={index}
                  />
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        {/* <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-16 p-8 bg-gradient-to-r from-slate-900/50 to-slate-800/50 rounded-2xl border border-slate-700/50"
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles size={20} className="text-yellow-400" />
            <h3 className="text-xl font-bold text-white">Can't find what you're looking for?</h3>
            <Sparkles size={20} className="text-yellow-400" />
          </div>
          <p className="text-gray-400 mb-6">
            Explore our complete collection and discover more amazing products
          </p>
          <Link
            to="/collections"
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 hover:scale-105"
          >
            Browse All Collections
            <Crown size={18} />
          </Link>
        </motion.div> */}
      </div>
    </div>
  );
}

// Best Seller Card Component
function BestSellerCard({ product, index }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const { addToCart } = useCart();

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prev) => (prev - 1 + currentImages.length) % currentImages.length);
  };

  const handleColorChange = (colorIndex) => {
    setSelectedColorIndex(colorIndex);
    setCurrentImageIndex(0);
  };

  const handleAddToCart = (e) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart({
      id: product.id,
      name: product.name,
      price: product.sale_price || product.salePrice || product.price,
      salePrice: product.sale_price || product.salePrice,
      image: currentImages[0] || product.images?.[0],
      color: product.colors?.[selectedColorIndex]?.name || 'Default',
      size: product.sizes?.[0] || 'M'
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3, delay: index * 0.05 }}
      className="group"
    >
      <Link
        to={`/product/${product.id}`}
        className="block bg-slate-900/40 backdrop-blur-md rounded-xl overflow-hidden border border-slate-800/50 hover:border-slate-700/70 transition-all duration-300 hover:shadow-xl hover:shadow-slate-900/20 hover:scale-[1.02]"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Image Container */}
        <div className="relative aspect-[4/5] overflow-hidden bg-slate-800/30">
          <img
            src={currentImages[currentImageIndex] || product.images?.[0]}
            alt={product.name}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
          />

          {/* Best Seller Badge */}
          <div className="absolute top-3 left-3 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full flex items-center gap-1">
            <Star size={10} className="fill-white" />
            <span>BEST</span>
          </div>

          {/* Image Navigation */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-full transition-all duration-200 opacity-100"
              >
                <ChevronLeft size={14} />
              </button>
              <button
                onClick={nextImage}
                className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-full transition-all duration-200 opacity-100"
              >
                <ChevronRight size={14} />
              </button>
            </>
          )}

          {/* Wishlist Button */}
          <div className="absolute top-3 right-3">
            <WishlistButton
              productId={product.id}
              productName={product.name}
              productPrice={product.salePrice || product.price}
              productImage={currentImages[0] || product.images?.[0]}
              className="bg-slate-800/80 hover:bg-slate-700 text-white w-8 h-8 rounded-full flex items-center justify-center transition-all duration-200"
            />
          </div>

          {/* Quick Add to Cart */}
          <motion.button
            onClick={handleAddToCart}
            className="absolute bottom-3 left-3 right-3 bg-[#0686d6] hover:bg-blue-600 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ShoppingBag size={16} />
            Quick Add
          </motion.button>
        </div>

        {/* Product Info */}
        <div className="p-4 space-y-3">
          <h3 className="font-medium text-white text-sm leading-tight line-clamp-2 group-hover:text-blue-300 transition-colors">
            {product.name}
          </h3>

          {/* Colors */}
          {product.colors && product.colors.length > 0 && (
            <div className="flex items-center gap-2">
              {product.colors.slice(0, 4).map((color, colorIndex) => (
                <button
                  key={colorIndex}
                  onClick={(e) => {
                    e.preventDefault();
                    handleColorChange(colorIndex);
                  }}
                  className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${
                    selectedColorIndex === colorIndex
                      ? 'border-white scale-110'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  }`}
                  style={{ backgroundColor: color.value }}
                />
              ))}
              {product.colors.length > 4 && (
                <span className="text-xs text-[#9a9a9a]">+{product.colors.length - 4}</span>
              )}
            </div>
          )}

          {/* Price and Rating */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {product.salePrice ? (
                <>
                  <span className="text-white font-bold text-lg">${product.salePrice}</span>
                  <span className="text-gray-500 line-through text-sm">${product.price}</span>
                </>
              ) : (
                <span className="text-white font-bold text-lg">${product.price}</span>
              )}
            </div>
            {product.average_rating && product.average_rating > 0 && (
              <div className="flex items-center gap-1">
                <Star size={12} className="text-yellow-400 fill-yellow-400" />
                <span className="text-gray-400 text-xs">{parseFloat(product.average_rating).toFixed(1)}</span>
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
