import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function ColorPreviewSlider({ product, className = '' }) {
  const [activeColorIndex, setActiveColorIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  if (!product || !product.colors || product.colors.length === 0) {
    return null;
  }

  const activeColor = product.colors[activeColorIndex];

  const nextColor = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setActiveColorIndex((prev) => (prev + 1) % product.colors.length);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  const prevColor = () => {
    if (isTransitioning) return;
    setIsTransitioning(true);
    setActiveColorIndex((prev) => (prev === 0 ? product.colors.length - 1 : prev - 1));

    setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  };

  const colorImage = activeColor.images?.[0] || activeColor.image || product.images?.[0];

  return (
    <motion.div
      className={`relative group ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="relative h-full w-full overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeColor.name}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="h-full w-full"
          >
            <img
              src={colorImage}
              alt={`${product.name} in ${activeColor.name}`}
              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
            />
          </motion.div>
        </AnimatePresence>

        {product.colors.length > 1 && (
          <>
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="absolute top-4 left-4 bg-black/60 backdrop-blur-sm text-white text-xs font-medium px-2 py-1 rounded"
            >
              Color: {activeColor.name}
            </motion.div>

            <div className="absolute opacity-0 group-hover:opacity-100 transition-opacity duration-300 inset-0 flex justify-between items-center">
              {product.colors.length > 1 && (
                <>
                  <button
                    onClick={prevColor}
                    className="ml-2 w-8 h-8 rounded-full bg-black/50 flex items-center justify-center text-white transition-transform hover:scale-110 hover:bg-black/70"
                    aria-label="Previous color"
                  >
                    <ChevronLeft size={16} />
                  </button>
                  <button
                    onClick={nextColor}
                    className="mr-2 w-8 h-8 rounded-full bg-black/50 flex items-center justify-center text-white transition-transform hover:scale-110 hover:bg-black/70"
                    aria-label="Next color"
                  >
                    <ChevronRight size={16} />
                  </button>
                </>
              )}
            </div>
          </>
        )}
      </div>

      {product.colors.length > 1 && (
        <div className="absolute bottom-2 left-0 right-0 flex justify-center gap-2">
          {product.colors.map((color, index) => (
            <button
              key={color.name}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (!isTransitioning) {
                  setIsTransitioning(true);
                  setActiveColorIndex(index);
                  setTimeout(() => setIsTransitioning(false), 300);
                }
              }}
              className="w-4 h-4 rounded-full flex items-center justify-center transition-all"
              aria-label={`View ${product.name} in ${color.name}`}
            >
              <span
                className={`w-2 h-2 rounded-full transition-all ${
                  activeColorIndex === index ? 'w-3 h-3 bg-white' : 'bg-white/50'
                }`}
              />
            </button>
          ))}
        </div>
      )}
    </motion.div>
  );
}