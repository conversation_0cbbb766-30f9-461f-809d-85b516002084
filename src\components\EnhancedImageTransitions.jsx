import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, RotateCcw } from 'lucide-react';

export default function EnhancedImageTransitions({
  images = [],
  currentIndex = 0,
  onIndexChange,
  transitionType = 'fade', // 'fade', 'slide', 'scale', 'flip'
  autoPreload = true,
  showControls = true,
  showDots = true,
  className = '',
  imageClassName = '',
  enableSwipe = true
}) {
  const [loadedImages, setLoadedImages] = useState(new Set());
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const containerRef = useRef(null);

  // Preload images
  useEffect(() => {
    if (autoPreload && images.length > 0) {
      const preloadImages = async () => {
        const promises = images.map((src, index) => {
          return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
              setLoadedImages(prev => new Set([...prev, index]));
              resolve();
            };
            img.onerror = () => resolve(); // Continue even if image fails
            img.src = src;
          });
        });
        
        await Promise.all(promises);
      };
      
      preloadImages();
    }
  }, [images, autoPreload]);

  // Preload adjacent images when current index changes
  useEffect(() => {
    if (images.length > 0) {
      const preloadAdjacent = (index) => {
        const prevIndex = index > 0 ? index - 1 : images.length - 1;
        const nextIndex = index < images.length - 1 ? index + 1 : 0;
        
        [prevIndex, nextIndex].forEach(idx => {
          if (!loadedImages.has(idx)) {
            const img = new Image();
            img.onload = () => {
              setLoadedImages(prev => new Set([...prev, idx]));
            };
            img.src = images[idx];
          }
        });
      };
      
      preloadAdjacent(currentIndex);
    }
  }, [currentIndex, images, loadedImages]);

  const handlePrevious = () => {
    if (isTransitioning) return;
    const newIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
    changeImage(newIndex);
  };

  const handleNext = () => {
    if (isTransitioning) return;
    const newIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
    changeImage(newIndex);
  };

  const changeImage = (newIndex) => {
    if (newIndex === currentIndex || isTransitioning) return;
    
    setIsTransitioning(true);
    if (onIndexChange) {
      onIndexChange(newIndex);
    }
    
    // Reset transition state after animation
    setTimeout(() => {
      setIsTransitioning(false);
    }, 500);
  };

  // Touch handlers for swipe
  const handleTouchStart = (e) => {
    if (!enableSwipe) return;
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e) => {
    if (!enableSwipe) return;
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!enableSwipe || !touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleNext();
    } else if (isRightSwipe) {
      handlePrevious();
    }
  };

  // Animation variants
  const getTransitionVariants = () => {
    switch (transitionType) {
      case 'slide':
        return {
          enter: { x: 300, opacity: 0 },
          center: { x: 0, opacity: 1 },
          exit: { x: -300, opacity: 0 }
        };
      case 'scale':
        return {
          enter: { scale: 0.8, opacity: 0 },
          center: { scale: 1, opacity: 1 },
          exit: { scale: 1.2, opacity: 0 }
        };
      case 'flip':
        return {
          enter: { rotateY: 90, opacity: 0 },
          center: { rotateY: 0, opacity: 1 },
          exit: { rotateY: -90, opacity: 0 }
        };
      default: // fade
        return {
          enter: { opacity: 0 },
          center: { opacity: 1 },
          exit: { opacity: 0 }
        };
    }
  };

  const variants = getTransitionVariants();

  if (!images || images.length === 0) {
    return (
      <div className={`flex items-center justify-center bg-slate-800 ${className}`}>
        <div className="text-slate-400">No images available</div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* Image Container */}
      <div className="relative w-full h-full">
        <AnimatePresence mode="wait" custom={currentIndex}>
          <motion.div
            key={currentIndex}
            custom={currentIndex}
            variants={variants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              duration: 0.5,
              ease: [0.4, 0, 0.2, 1]
            }}
            className="absolute inset-0"
          >
            {/* Loading State */}
            {!loadedImages.has(currentIndex) && (
              <div className="absolute inset-0 bg-slate-800 flex items-center justify-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                  className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
                />
              </div>
            )}
            
            {/* Image */}
            <motion.img
              src={images[currentIndex]}
              alt={`Image ${currentIndex + 1}`}
              className={`w-full h-full object-cover ${imageClassName}`}
              initial={{ opacity: loadedImages.has(currentIndex) ? 1 : 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              onLoad={() => {
                setLoadedImages(prev => new Set([...prev, currentIndex]));
              }}
              onError={(e) => {
                e.target.src = '/api/placeholder/400/400';
              }}
            />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Controls */}
      {showControls && images.length > 1 && (
        <>
          <motion.button
            whileHover={{ scale: 1.1, backgroundColor: 'rgba(0,0,0,0.8)' }}
            whileTap={{ scale: 0.9 }}
            onClick={handlePrevious}
            disabled={isTransitioning}
            className="absolute left-2 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 z-20 disabled:opacity-50"
          >
            <ChevronLeft size={20} />
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.1, backgroundColor: 'rgba(0,0,0,0.8)' }}
            whileTap={{ scale: 0.9 }}
            onClick={handleNext}
            disabled={isTransitioning}
            className="absolute right-2 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-200 z-20 disabled:opacity-50"
          >
            <ChevronRight size={20} />
          </motion.button>
        </>
      )}

      {/* Dot Indicators */}
      {showDots && images.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2 z-20">
          {images.map((_, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.2 }}
              whileTap={{ scale: 0.9 }}
              onClick={() => changeImage(index)}
              disabled={isTransitioning}
              className={`w-2 h-2 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-white scale-125 shadow-lg'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
            />
          ))}
        </div>
      )}

      {/* Loading Progress */}
      {autoPreload && loadedImages.size < images.length && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute top-4 left-4 bg-black/70 text-white text-xs px-3 py-2 rounded-full backdrop-blur-sm z-20"
        >
          Loading {loadedImages.size}/{images.length}
        </motion.div>
      )}

      {/* Transition Type Indicator (for demo) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 right-4 bg-black/70 text-white text-xs px-2 py-1 rounded backdrop-blur-sm z-20">
          {transitionType}
        </div>
      )}
    </div>
  );
}
