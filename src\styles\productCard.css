.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

.product-card .color-dot {
  transition: all 0.2s ease;
}

.product-card .color-dot.active {
  transform: scale(1.3);
  box-shadow: 0 0 0 2px white, 0 0 0 3px #000;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.product-card .product-image-container img {
  transition: opacity 0.5s ease;
}

.product-card .nav-arrows {
  animation: fadeIn 0.3s ease;
}
