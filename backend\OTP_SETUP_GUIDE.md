# 📱 OTP Authentication Setup Guide

Complete guide to set up phone number based OTP authentication for your Wolffoxx ecommerce backend.

## 🎯 **What You Get**

✅ **Phone-based Login** - Users login with phone number + OTP  
✅ **Auto Registration** - New users created automatically  
✅ **FREE SMS Options** - Multiple free SMS providers  
✅ **Security Features** - Rate limiting, attempt tracking  
✅ **Fallback Support** - Email auth still works  

## 🚀 **Quick Setup (5 Minutes)**

### **Step 1: Choose FREE SMS Provider**

#### **Option A: Fast2SMS (Recommended - 50 FREE SMS daily)**
```bash
# 1. Go to fast2sms.com
# 2. Sign up for free account
# 3. Get API key from dashboard
# 4. Add to .env:

SMS_PROVIDER=fast2sms
FAST2SMS_API_KEY=your_api_key_here
FAST2SMS_SENDER_ID=WOLFFOXX
```

#### **Option B: TextLocal (10 FREE SMS for testing)**
```bash
# 1. Go to textlocal.in
# 2. Sign up for free account
# 3. Get API key
# 4. Add to .env:

SMS_PROVIDER=textlocal
TEXTLOCAL_API_KEY=your_api_key_here
TEXTLOCAL_SENDER=WOLFFOXX
```

#### **Option C: Development Mode (No SMS, just logs)**
```bash
# For testing without SMS
SMS_PROVIDER=log
```

### **Step 2: Update Database**
```bash
# Run the new migration
php scripts/migrate.php
```

### **Step 3: Test OTP System**
```bash
# Test API endpoints:

# 1. Send OTP
curl -X POST http://localhost:8000/api/v1/auth/otp/send \
  -H "Content-Type: application/json" \
  -d '{"phone": "**********"}'

# 2. Verify OTP
curl -X POST http://localhost:8000/api/v1/auth/otp/verify \
  -H "Content-Type: application/json" \
  -d '{"phone": "**********", "otp": "123456"}'
```

## 📋 **API Endpoints**

### **Send OTP**
```http
POST /api/v1/auth/otp/send
Content-Type: application/json

{
  "phone": "**********",
  "purpose": "login"
}

Response:
{
  "success": true,
  "data": {
    "message": "OTP sent successfully",
    "phone": "987****210",
    "expires_in": 300,
    "can_resend_after": 60
  }
}
```

### **Verify OTP & Login**
```http
POST /api/v1/auth/otp/verify
Content-Type: application/json

{
  "phone": "**********",
  "otp": "123456",
  "user_details": {
    "first_name": "John",
    "last_name": "Doe"
  }
}

Response:
{
  "success": true,
  "data": {
    "user": { ... },
    "tokens": {
      "access_token": "...",
      "refresh_token": "..."
    },
    "is_new_user": true,
    "message": "Account created and logged in successfully"
  }
}
```

### **Resend OTP**
```http
POST /api/v1/auth/otp/resend
Content-Type: application/json

{
  "phone": "**********"
}
```

## 🔧 **Frontend Integration**

### **React Example**
```javascript
// Send OTP
const sendOTP = async (phone) => {
  const response = await fetch('/api/v1/auth/otp/send', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone })
  });
  return response.json();
};

// Verify OTP
const verifyOTP = async (phone, otp) => {
  const response = await fetch('/api/v1/auth/otp/verify', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone, otp })
  });
  return response.json();
};

// Usage in component
const handleLogin = async () => {
  // Step 1: Send OTP
  const otpResult = await sendOTP(phoneNumber);
  if (otpResult.success) {
    setShowOTPInput(true);
  }
  
  // Step 2: Verify OTP (after user enters OTP)
  const loginResult = await verifyOTP(phoneNumber, otpCode);
  if (loginResult.success) {
    localStorage.setItem('token', loginResult.data.tokens.access_token);
    // Redirect to dashboard
  }
};
```

## 💰 **FREE SMS Provider Setup**

### **Fast2SMS (Best for India)**
```bash
# Features:
- 50 FREE SMS daily
- No credit card required
- Instant activation
- Good delivery rates

# Setup:
1. Visit: https://www.fast2sms.com/
2. Sign up with phone number
3. Verify your account
4. Go to Dashboard → API Keys
5. Copy API key to .env file
```

### **TextLocal (Good for Testing)**
```bash
# Features:
- 10 FREE SMS for testing
- UK-based service
- Good documentation

# Setup:
1. Visit: https://www.textlocal.in/
2. Create free account
3. Get API key from settings
4. Add to .env file
```

### **MSG91 (Trial Credits)**
```bash
# Features:
- Free trial credits
- Global coverage
- Enterprise features

# Setup:
1. Visit: https://msg91.com/
2. Sign up for free trial
3. Get auth key
4. Configure in .env
```

## 🛡️ **Security Features**

### **Rate Limiting**
- Max 5 OTP requests per hour per phone
- Max 3 verification attempts per OTP
- Automatic cleanup of expired OTPs

### **Phone Number Validation**
- Automatic formatting and cleaning
- Country code handling
- Invalid number detection

### **Logging & Monitoring**
- All OTP requests logged
- Failed attempts tracked
- SMS delivery status monitoring

## 🔄 **User Flow**

```
1. User enters phone number
   ↓
2. System sends OTP via SMS
   ↓
3. User enters OTP
   ↓
4. System verifies OTP
   ↓
5. If new user: Create account automatically
   If existing user: Login
   ↓
6. Return JWT tokens for authentication
```

## 🎛️ **Configuration Options**

### **OTP Settings**
```bash
OTP_LENGTH=6          # OTP length (4-8 digits)
OTP_EXPIRY=300        # Expiry in seconds (5 minutes)
OTP_MAX_ATTEMPTS=3    # Max verification attempts
```

### **SMS Settings**
```bash
SMS_PROVIDER=fast2sms # Provider to use
# Provider-specific settings in .env
```

## 🧪 **Testing**

### **Development Mode**
```bash
# Set in .env for testing without SMS
SMS_PROVIDER=log

# OTPs will be logged to storage/logs/app.log
# Check logs to see generated OTPs
```

### **Test Phone Numbers**
```bash
# Use these for testing (won't send actual SMS)
91**********  # Test number 1
************  # Test number 2
```

## 🚨 **Troubleshooting**

### **OTP Not Received**
1. Check SMS provider configuration
2. Verify phone number format
3. Check rate limiting
4. Review SMS logs

### **OTP Verification Failed**
1. Check OTP expiry time
2. Verify attempt count
3. Ensure correct phone number format

### **SMS Provider Errors**
1. Verify API credentials
2. Check account balance/limits
3. Review provider documentation

## 📊 **Monitoring**

### **Admin Endpoints**
```bash
# Get OTP statistics (admin only)
GET /api/v1/auth/otp/stats

# Cleanup expired OTPs (admin only)
POST /api/v1/auth/otp/cleanup
```

### **Logs Location**
```bash
storage/logs/app.log      # General application logs
storage/logs/otp.log      # OTP-specific logs
storage/logs/sms.log      # SMS delivery logs
```

## 🎉 **Benefits for Your Client**

✅ **$0 Monthly Cost** - Free SMS providers  
✅ **Better UX** - No password to remember  
✅ **Higher Conversion** - Faster signup process  
✅ **Mobile-First** - Perfect for mobile users  
✅ **Secure** - OTP expires quickly  
✅ **Scalable** - Easy to upgrade later  

## 🔄 **Migration from Email Auth**

Both email and OTP auth work simultaneously:
- Existing email users continue to work
- New users can use phone/OTP
- Users can add phone to existing email accounts

Your client gets the best of both worlds! 🚀
