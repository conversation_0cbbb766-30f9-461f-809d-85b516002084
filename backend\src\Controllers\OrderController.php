<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Order;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\User;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Services\EmailService;

/**
 * Order Controller
 * 
 * Handles order creation, management, and tracking
 */
class OrderController
{
    private Order $orderModel;
    private Cart $cartModel;
    private User $userModel;
    private EmailService $emailService;

    public function __construct()
    {
        $this->orderModel = new Order();
        $this->cartModel = new Cart();
        $this->userModel = new User();
        $this->emailService = new EmailService();
    }

    /**
     * Create order from cart
     * POST /api/v1/orders
     */
    public function create(): void
    {
        try {
            // Check authentication
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $user = $this->userModel->findById($userId);
            if (!$user) {
                Response::error('User not found', 404);
                return;
            }

            // Validate email address before order creation
            if (empty($user['email']) || !filter_var($user['email'], FILTER_VALIDATE_EMAIL)) {
                Response::error('Valid email address is required to place an order. Please update your profile with a valid email address.', 400, [
                    'requires_email' => true,
                    'current_email' => $user['email'] ?? null
                ]);
                return;
            }

            // Validate user profile completion before order creation
            $profileStatus = $this->userModel->getProfileCompletionStatus($user['id']);
            if (!$profileStatus['is_complete']) {
                Response::error('Profile incomplete. Please complete your profile before placing an order.', 400, [
                    'missing_fields' => $profileStatus['missing_fields'],
                    'requires_profile_completion' => true
                ]);
                return;
            }

            // Validate required user data for order
            if (empty($user['phone'])) {
                Response::error('Phone number verification required', 400, [
                    'requires_phone_verification' => true
                ]);
                return;
            }

            // Get request data
            $input = json_decode(file_get_contents('php://input'), true);
            
            // Validate required fields
            $required = ['shipping_address', 'billing_address', 'payment_method'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::error("Missing required field: {$field}", 400);
                    return;
                }
            }

            // Get user's cart with fresh data from database
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                Response::error('Cart is empty', 400);
                return;
            }

            // Log cart data for debugging including individual item quantities
            error_log('Order creation - Cart data: ' . json_encode([
                'cart_id' => $cart['id'] ?? 'N/A',
                'items_count' => count($cart['items']),
                'subtotal' => $cart['subtotal'] ?? 0,
                'total_amount' => $cart['total_amount'] ?? 0,
                'items_with_quantities' => array_map(function($item) {
                    return [
                        'product_id' => $item['product_id'],
                        'product_name' => $item['product_name'],
                        'quantity' => $item['quantity'],
                        'color' => $item['selected_color'],
                        'size' => $item['selected_size']
                    ];
                }, $cart['items'])
            ]));

            // Create order - use frontend calculated values for accuracy
            $orderData = [
                'user_id' => $user['id'],
                'customer_email' => $user['email'],
                'customer_phone' => $user['phone'],
                'customer_name' => $input['customer_name'] ?? $user['name'] ?? '',
                'shipping_address' => $input['shipping_address'],
                'billing_address' => $input['billing_address'],
                'payment_method' => $input['payment_method'],
                'subtotal' => $input['subtotal'] ?? $cart['subtotal'], // Use frontend subtotal if available
                'tax_amount' => $input['tax_amount'] ?? ($cart['tax_amount'] ?? 0), // Use frontend tax if available
                'shipping_amount' => $input['shipping_amount'] ?? 0,
                'discount_amount' => $input['discount_amount'] ?? ($cart['discount_amount'] ?? 0),
                'total_amount' => $input['total_amount'] ?? $cart['total_amount'], // Use frontend total if available
                'coupon_code' => $cart['coupon_code'] ?? null,
                'order_notes' => $input['order_notes'] ?? null
            ];

            $orderId = $this->orderModel->createOrder($orderData, $cart['items']);

            if ($orderId) {
                // Clear cart after successful order
                $this->cartModel->clearCart($user['id']);

                // Get created order details with items
                $order = $this->orderModel->getOrderById($orderId);

                // Get order items from database to ensure we have the exact quantities that were saved
                $orderItems = $this->orderModel->getOrderItems($orderId);

                // Send order confirmation email
                // Prepare shipping address array for email template compatibility
                $order['shipping_address'] = [
                    'address_line_1' => $order['shipping_address_line1'] ?? '',
                    'address_line_2' => $order['shipping_address_line2'] ?? '',
                    'city' => $order['shipping_city'] ?? '',
                    'state' => $order['shipping_state'] ?? '',
                    'postal_code' => $order['shipping_postal_code'] ?? '',
                    'country' => $order['shipping_country'] ?? 'India',
                    'phone' => $order['customer_phone'] ?? $user['phone'] ?? ''
                ];

                // Log order items quantities for debugging
                error_log('Order confirmation email - Order items quantities: ' . json_encode(
                    array_map(function($item) {
                        return [
                            'product_id' => $item['product_id'],
                            'product_name' => $item['product_name'],
                            'quantity' => $item['quantity'],
                            'selected_color' => $item['selected_color'],
                            'selected_size' => $item['selected_size']
                        ];
                    }, $orderItems)
                ));

                // Use order items from database instead of cart items to ensure correct quantities
                $this->sendOrderConfirmationEmail($order, $user, $orderItems);

                Response::success([
                    'id' => $orderId,
                    'order_id' => $orderId,
                    'order_number' => $order['order_number'],
                    'total_amount' => $order['total_amount'],
                    'status' => $order['status'],
                    'message' => 'Order created successfully'
                ]);
            } else {
                Response::error('Failed to create order', 500);
            }

        } catch (\Exception $e) {
            error_log('Order creation failed: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine() . ' | Trace: ' . $e->getTraceAsString());
            Response::error('Order creation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get user's orders
     * GET /api/v1/orders
     */
    public function index(): void
    {
        $this->getUserOrders();
    }

    /**
     * Get user's orders
     * GET /api/v1/orders
     */
    public function getUserOrders(): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $page = (int)($_GET['page'] ?? 1);
            $limit = (int)($_GET['limit'] ?? 10);

            $orders = $this->orderModel->getUserOrders($userId, $page, $limit);

            Response::success([
                'orders' => $orders,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $this->orderModel->getUserOrdersCount($userId)
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get user orders failed: ' . $e->getMessage());
            Response::error('Failed to get orders', 500);
        }
    }

    /**
     * Get single order details
     * GET /api/v1/orders/{id}
     */
    public function show(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $order = $this->orderModel->getOrderById($orderId);

            if (!$order) {
                Response::error('Order not found', 404);
                return;
            }

            // Check if order belongs to user
            if ($order['user_id'] !== $userId) {
                Response::error('Access denied', 403);
                return;
            }

            // Get order items
            $order['items'] = $this->orderModel->getOrderItems($orderId);

            // Format shipping address for frontend
            $order['shipping_address'] = [
                'first_name' => $order['customer_name'] ? explode(' ', $order['customer_name'])[0] : '',
                'last_name' => $order['customer_name'] ? (explode(' ', $order['customer_name'])[1] ?? '') : '',
                'address_line_1' => $order['shipping_address_line1'] ?? '',
                'address_line_2' => $order['shipping_address_line2'] ?? '',
                'city' => $order['shipping_city'] ?? '',
                'state' => $order['shipping_state'] ?? '',
                'postal_code' => $order['shipping_postal_code'] ?? '',
                'country' => $order['shipping_country'] ?? ''
            ];

            Response::success($order);

        } catch (\Exception $e) {
            error_log('Get order details failed: ' . $e->getMessage());
            Response::error('Failed to get order details', 500);
        }
    }

    /**
     * Update order status
     * PUT /api/v1/orders/{id}/status
     */
    public function updateStatus(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $input = json_decode(file_get_contents('php://input'), true);

            if (empty($input['status'])) {
                Response::error('Status is required', 400);
                return;
            }

            $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled'];
            if (!in_array($input['status'], $validStatuses)) {
                Response::error('Invalid status', 400);
                return;
            }

            // Check if order exists and belongs to user
            $order = $this->orderModel->getOrderById($orderId);
            if (!$order || $order['user_id'] !== $userId) {
                Response::error('Order not found', 404);
                return;
            }

            // Only allow cancellation by user
            if ($input['status'] !== 'cancelled') {
                Response::error('You can only cancel orders', 403);
                return;
            }

            // Don't allow cancellation of shipped/delivered orders
            if (in_array($order['status'], ['shipped', 'delivered'])) {
                Response::error('Cannot cancel shipped or delivered orders', 400);
                return;
            }

            $success = $this->orderModel->updateOrderStatus($orderId, $input['status']);

            if ($success) {
                Response::success(['message' => 'Order status updated successfully']);
            } else {
                Response::error('Failed to update order status', 500);
            }

        } catch (\Exception $e) {
            error_log('Update order status failed: ' . $e->getMessage());
            Response::error('Failed to update order status', 500);
        }
    }

    /**
     * Track order
     * GET /api/v1/orders/{id}/track
     */
    public function trackOrder(array $params): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            if (!$userId) {
                Response::error('Authentication required', 401);
                return;
            }

            $orderId = (int)$params['id'];
            $order = $this->orderModel->getOrderById($orderId);

            if (!$order || $order['user_id'] !== $userId) {
                Response::error('Order not found', 404);
                return;
            }

            $tracking = [
                'order_number' => $order['order_number'],
                'status' => $order['status'],
                'tracking_number' => $order['tracking_number'],
                'estimated_delivery_date' => $order['estimated_delivery_date'],
                'delivered_at' => $order['delivered_at'],
                'status_history' => $this->orderModel->getOrderStatusHistory($orderId)
            ];

            Response::success($tracking);

        } catch (\Exception $e) {
            error_log('Track order failed: ' . $e->getMessage());
            Response::error('Failed to track order', 500);
        }
    }



    /**
     * Send order confirmation email
     */
    private function sendOrderConfirmationEmail(array $order, array $user, array $orderItems): void
    {
        try {
            // Only send email if user has an email address
            $email = $user['email'] ?? $order['customer_email'] ?? null;

            if (empty($email)) {
                error_log("No email address found for order {$order['order_number']}");
                return;
            }

            // Send order confirmation email
            $emailSent = $this->emailService->send(
                $email,
                "🎉 Order Confirmation - {$order['order_number']} | Wolffoxx",
                'emails/order-confirmation',
                [
                    'order' => $order,
                    'user' => $user,
                    'orderItems' => array_map(function($item) {
                        // Use sale price if available and greater than 0, otherwise use unit price
                        $effectivePrice = (!empty($item['sale_price']) && $item['sale_price'] > 0)
                            ? $item['sale_price']
                            : ($item['unit_price'] ?? $item['price'] ?? 0);

                        $item['price'] = $effectivePrice;
                        $item['total_price'] = $effectivePrice * ($item['quantity'] ?? 1);

                        return $item;
                    }, $orderItems)
                ]
            );

            // Always send admin copy (do not block on failure)
        $adminEmail = $_ENV['ADMIN_EMAIL'] ?? '<EMAIL>';
        if (!empty($adminEmail) && filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
            $this->emailService->send(
                $adminEmail,
                "🛎️ New Order Received - {$order['order_number']}",
                'emails/order-confirmation',
                [
                    'order' => $order,
                    'user' => $user,
                    'orderItems' => $orderItems,
                    'is_admin_copy' => true
                ]
            );
        }

        if ($emailSent) {
                error_log("Order confirmation email sent successfully for order {$order['order_number']} to {$email}");
            } else {
                error_log("Failed to send order confirmation email for order {$order['order_number']} to {$email}");
            }

        } catch (\Exception $e) {
            error_log("Order confirmation email error for order {$order['order_number']}: " . $e->getMessage());
        }
    }
}
