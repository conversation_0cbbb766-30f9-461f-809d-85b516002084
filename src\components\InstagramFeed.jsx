import { motion } from 'framer-motion';
import { Instagram } from 'lucide-react';

const instagramPosts = [
  {
    id: 1,
    image: 'https://images.unsplash.com/photo-1529139574466-a303027c1d8b?q=80&w=300&auto=format&fit=crop ',
    username: '@user1'
  },
  {
    id: 2,
    image: 'https://images.unsplash.com/photo-1551803091-e20673f15770?q=80&w=300&auto=format&fit=crop ',
    username: '@user2'
  },
  {
    id: 3,
    image: 'https://images.unsplash.com/photo-1550614000-4895a10e1bfd?q=80&w=300&auto=format&fit=crop ',
    username: '@user3'
  },
  {
    id: 4,
    image: 'https://images.unsplash.com/photo-1583846783214-7229a91b20ed?q=80&w=300&auto=format&fit=crop ',
    username: '@user4'
  },
  {
    id: 5,
    image: 'https://images.unsplash.com/photo-1573496773905-f5b17e717f05?q=80&w=300&auto=format&fit=crop ',
    username: '@user5'
  },
  {
    id: 6,
    image: 'https://images.unsplash.com/photo-1599255068390-206e0d068539?q=80&w=300&auto=format&fit=crop ',
    username: '@user6'
  },
  {
    id: 7,
    image: 'https://images.unsplash.com/photo-1578897366846-aa7aaffa3c18?q=80&w=300&auto=format&fit=crop ',
    username: '@user7'
  },
  {
    id: 8,
    image: 'https://images.unsplash.com/photo-1581655353564-df123a1eb820?q=80&w=300&auto=format&fit=crop ',
    username: '@user8'
  }
];

export default function InstagramFeed() {
  return (
    <section className="py-16 bg-gray-950">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div
          className="text-center mb-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="flex items-center justify-center gap-2 mb-3">
            <Instagram size={24} className="text-white" />
            <h2 className="text-4xl font-['Bebas_Neue',sans-serif] tracking-wider text-white">#<span className="text-[#FF6B35]">WEARTHEDROP</span></h2>
          </div>
          <p className="text-gray-400 max-w-2xl mx-auto">Tag us on Instagram @streetnoir for a chance to be featured</p>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 md:gap-4"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {instagramPosts.map((post, index) => (
            <motion.a
              key={post.id}
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              className="block aspect-square bg-gray-800 rounded-md overflow-hidden group relative"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              viewport={{ once: true }}
              whileHover={{ scale: 1.03 }}
            >
              <img
                src={post.image}
                alt="Instagram post"
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
              />
              <div className="absolute inset-0 bg-gray-900/60 opacity-0 group-hover:opacity-100 flex items-center justify-center transition-opacity duration-300">
                <span className="text-white font-medium">{post.username}</span>
              </div>
            </motion.a>
          ))}
        </motion.div>

        <div className="text-center mt-10">
          <a
            href="https://instagram.com "
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-600 text-white font-medium rounded-md hover:from-purple-700 hover:to-pink-700 transition-all group"
          >
            <Instagram size={18} />
            Follow on Instagram
            <motion.span
              initial={{ x: 0 }}
              whileHover={{ x: 4 }}
              className="inline-block transition-transform"
            >
              →
            </motion.span>
          </a>
        </div>
      </div>
    </section>
  );
}