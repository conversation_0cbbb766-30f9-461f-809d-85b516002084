import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shirt, X, ShoppingBag, Eye } from 'lucide-react';
import { useOutfit } from '../context/OutfitContext';
import { useCart } from '../context/CartContext';

const OutfitIndicator = () => {
  const { currentOutfit, currentOutfitCount, currentOutfitTotal, clearCurrentOutfit } = useOutfit();
  const { addToCart } = useCart();
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  if (currentOutfitCount === 0) return null;

  const addOutfitToCart = () => {
    // Prevent rapid double-clicks
    if (isProcessing) {
      console.log('⚠️ CURRENT OUTFIT ALREADY PROCESSING - Skipping');
      return;
    }

    setIsProcessing(true);

    const outfitName = `Current Outfit ${Date.now()}`;
    const baseOperationId = `current-outfit-${Date.now()}`;

    console.log('🛒 CURRENT OUTFIT TO CART - Starting process:', {
      outfitName,
      itemCount: currentOutfit.length,
      baseOperationId,
      items: currentOutfit.map(item => ({ id: item.id, name: item.name }))
    });

    // Add a small delay between each item to prevent race conditions
    currentOutfit.forEach((item, index) => {
      setTimeout(() => {
        // Use the selected color or first available color
        const selectedColor = item.selectedColor || item.colors?.[0];

        // Create unique operation ID for each item
        const itemOperationId = `${baseOperationId}-item-${item.id}-${index}`;

        // Format the product correctly for the cart
        const cartProduct = {
          id: item.id,
          name: item.name,
          price: item.salePrice || item.price,
          color: selectedColor?.name || item.colors?.[0]?.name || 'Default',
          size: item.selectedSize || item.sizes?.[0] || 'M',
          image: selectedColor?.images?.[0] || item.images?.[0] || item.image,
          outfitName: outfitName, // Group items under outfit name
          category: item.category,
          operationId: itemOperationId // Add unique operation ID for each item
        };

        console.log(`🛒 CURRENT OUTFIT TO CART - Adding item ${index + 1}/${currentOutfit.length}:`, cartProduct);
        addToCart(cartProduct, 1); // Add 1 quantity of each item
      }, index * 50); // 50ms delay between each item
    });

    setShowSuccess(true);
    setTimeout(() => {
      setShowSuccess(false);
      clearCurrentOutfit();
      setIsProcessing(false); // Reset processing state
    }, 2000);
  };

  return (
    <>
      {/* Floating Indicator */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0, opacity: 0 }}
        className="fixed bottom-6 right-6 z-40"
      >
        <motion.button
          onClick={() => setIsExpanded(true)}
          className="text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-3"
          style={{
            background: 'linear-gradient(237deg, #214FC3 0%, #54AEE1 100%)'
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <div className="relative">
            <Shirt size={24} />
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-2 -right-2 bg-cyan-400 text-blue-900 text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center"
            >
              {currentOutfitCount}
            </motion.div>
          </div>
          <div className="hidden sm:block">
            <div className="text-sm font-medium">Current Outfit</div>
            <div className="text-xs opacity-90">${currentOutfitTotal.toFixed(2)}</div>
          </div>
        </motion.button>
      </motion.div>

      {/* Expanded Modal */}
      <AnimatePresence>
        {isExpanded && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
              onClick={() => setIsExpanded(false)}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fixed bottom-4 right-4 w-80 max-w-[calc(100vw-2rem)] bg-[#1a1a1a] backdrop-blur-xl border border-[#404040] rounded-xl z-50 overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-[#2a2a2a]">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-[#FF6B35] to-[#F7931E]">
                    <Shirt size={18} className="text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">Your Outfit</h3>
                    <p className="text-[#AAAAAA] text-sm">{currentOutfitCount} items</p>
                  </div>
                </div>
                <button
                  onClick={() => setIsExpanded(false)}
                  className="p-2 text-slate-400 hover:text-white transition-colors"
                >
                  <X size={20} />
                </button>
              </div>

              {/* Outfit Items */}
              <div className="max-h-64 overflow-y-auto p-4 space-y-3">
                {currentOutfit.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-3 bg-slate-800/50 rounded-lg p-3"
                  >
                    <img
                      src={item.images[0]}
                      alt={item.name}
                      className="w-12 h-12 object-cover rounded-lg"
                    />
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white text-sm font-medium truncate">{item.name}</h4>
                      <p className="text-slate-400 text-xs">{item.category}</p>
                      <p className="text-cyan-400 text-sm font-semibold">${item.price}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Footer */}
              <div className="border-t border-slate-700/50 p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-slate-300">Total:</span>
                  <span className="text-white font-bold text-lg">${currentOutfitTotal.toFixed(2)}</span>
                </div>

                <div className="flex gap-2">
                  <motion.button
                    onClick={addOutfitToCart}
                    disabled={isProcessing}
                    className={`flex-1 text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 flex items-center justify-center gap-2 ${
                      isProcessing
                        ? 'opacity-50 cursor-not-allowed'
                        : 'hover:shadow-lg hover:shadow-blue-500/25'
                    }`}
                    style={{
                      background: 'linear-gradient(237deg, #214FC3 0%, #54AEE1 100%)'
                    }}
                    whileHover={!isProcessing ? { scale: 1.02 } : {}}
                    whileTap={!isProcessing ? { scale: 0.98 } : {}}
                  >
                    <ShoppingBag size={16} />
                    {isProcessing ? 'Adding...' : 'Add to Cart'}
                  </motion.button>

                  <motion.button
                    onClick={clearCurrentOutfit}
                    className="px-4 py-2 bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 transition-colors"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Clear
                  </motion.button>
                </div>
              </div>

              {/* Success Message */}
              <AnimatePresence>
                {showSuccess && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    className="absolute inset-x-4 bottom-4 bg-green-500 text-white p-3 rounded-lg flex items-center gap-2"
                  >
                    <ShoppingBag size={16} />
                    <span className="font-medium">Outfit added to cart!</span>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default OutfitIndicator;
