import { Instagram, Twitter } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function Footer() {
  return (
    <footer className="bg-[#1a1a1a] backdrop-blur-sm pt-6 sm:pt-8 md:pt-12 pb-4 sm:pb-6 border-t border-[#2a2a2a]">
      <div className="container mx-auto px-3 sm:px-4 md:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 md:gap-6">
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl sm:text-3xl font-['Bebas_Neue',sans-serif] text-[#f5f5f5] mb-3 sm:mb-4 tracking-wider">@WOLFFOXX</h3>
            <p className="text-[#9a9a9a] max-w-md mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base">
              Elevating streetwear with premium quality oversized pieces. Join our community of trendsetters.
            </p>
            <div className="flex space-x-3 sm:space-x-4">
              <a href="#" className="group bg-[#2a2a2a] p-2 rounded-lg border border-[#404040] text-[#9a9a9a] hover:text-[#d4d4d4] hover:border-[#6a6a6a] transition-all duration-300">
                <Instagram size={18} className="sm:w-5 sm:h-5 group-hover:scale-110 transition-transform" />
              </a>
              <a href="#" className="group bg-[#2a2a2a] p-2 rounded-lg border border-[#404040] text-[#9a9a9a] hover:text-[#d4d4d4] hover:border-[#6a6a6a] transition-all duration-300">
                <Twitter size={18} className="sm:w-5 sm:h-5 group-hover:scale-110 transition-transform" />
              </a>
            </div>
          </div>

          <div>
            <h4 className="font-semibold text-white mb-3 sm:mb-4 text-base sm:text-lg">Shop</h4>
            <ul className="space-y-2 sm:space-y-3">
              <li>
                <Link to="/collections" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  All Products
                </Link>
              </li>
              <li>
                <Link to="/deals" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  Deals
                </Link>
              </li>
              <li>
                <Link to="/category/bestsellers" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  Best Sellers
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="font-semibold text-white mb-3 sm:mb-4 text-base sm:text-lg">Support</h4>
            <ul className="space-y-2 sm:space-y-3">
              <li>
                <a href="/#faq" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  FAQs
                </a>
              </li>
              <li>
                <Link to="/contact" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  Contact Us
                </Link>
              </li>
              <li>
                <Link to="/returns" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  Returns & Exchange
                </Link>
              </li>
              <li>
                <Link to="/track-order" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                  Track Order
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Legal Section - Mobile Responsive */}
        <div className="mt-6 sm:mt-8 md:hidden">
          <h4 className="font-semibold text-white mb-3 sm:mb-4 text-base sm:text-lg">Legal</h4>
          <ul className="space-y-2 sm:space-y-3 flex flex-col sm:flex-row sm:space-y-0 sm:space-x-6">
            <li>
              <Link to="/privacy" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                Privacy Policy
              </Link>
            </li>
            <li>
              <Link to="/terms" className="text-slate-300 hover:text-white hover:translate-x-1 transition-all duration-200 inline-block text-sm sm:text-base">
                Terms & Conditions
              </Link>
            </li>
          </ul>
        </div>

        <div className="border-t border-slate-600/50 pt-4 sm:pt-6 mt-6 sm:mt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-4 sm:gap-6">
            <div className="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 md:gap-6">
              <p className="text-slate-300 text-xs sm:text-sm">
                © {new Date().getFullYear()} WOLFFOXX. All rights reserved.
              </p>
              {/* Legal links for desktop */}
              <div className="hidden md:flex items-center gap-4 text-sm">
                <Link to="/privacy" className="text-slate-400 hover:text-slate-300 transition-colors">
                  Privacy Policy
                </Link>
                <span className="text-slate-600">•</span>
                <Link to="/terms" className="text-slate-400 hover:text-slate-300 transition-colors">
                  Terms & Conditions
                </Link>
              </div>
            </div>

            <div className="flex items-center gap-1 sm:gap-2 flex-wrap justify-center lg:justify-end">
              {/* Visa */}
              <div className="bg-white rounded-lg p-2 flex items-center justify-center h-8 w-12 shadow-sm hover:shadow-md transition-shadow">
                <img
                  src="https://static.cdnlogo.com/logos/v/34/visa.svg"
                  alt="Visa"
                  className="h-4 w-auto"
                />
              </div>

              {/* Mastercard */}
              <div className="bg-white rounded-lg p-2 flex items-center justify-center h-8 w-12 shadow-sm hover:shadow-md transition-shadow">
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
                  alt="Mastercard"
                  className="h-4"
                />
              </div>

              {/* American Express */}
              <div className="bg-white rounded-lg p-2 flex items-center justify-center h-8 w-12 shadow-sm hover:shadow-md transition-shadow">
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg"
                  alt="American Express"
                  className="h-4"
                />
              </div>

              {/* PayPal */}
              <div className="bg-white rounded-lg p-2 flex items-center justify-center h-8 w-12 shadow-sm hover:shadow-md transition-shadow">
                <img
                  src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
                  alt="PayPal"
                  className="h-4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}