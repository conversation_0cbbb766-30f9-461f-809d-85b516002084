<?php

/**
 * Product Seeder
 * 
 * Generates comprehensive mock product data matching frontend structure
 * Creates 100-200 products across all categories with colors, sizes, images
 */

require_once __DIR__ . '/../../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
$dotenv->load();

use Wolffoxx\Config\Database;
use Wolffoxx\Utils\Logger;

class ProductSeeder
{
    private Logger $logger;
    private array $categories = [
        'Oversized Tees',
        'T-Shirts', 
        'Hoodies',
        'Shirts',
        'Capri'
    ];
    
    private array $colors = [
        ['name' => 'Black', 'value' => '#000000'],
        ['name' => 'White', 'value' => '#FFFFFF'],
        ['name' => 'Navy Blue', 'value' => '#1e3a8a'],
        ['name' => 'Forest Green', 'value' => '#16a34a'],
        ['name' => 'Charcoal Gray', 'value' => '#374151'],
        ['name' => 'Red', 'value' => '#DC2626'],
        ['name' => 'Purple', 'value' => '#7c3aed'],
        ['name' => 'Beige', 'value' => '#d4a574'],
        ['name' => 'Gray', 'value' => '#6b7280'],
        ['name' => 'Olive', 'value' => '#84cc16'],
        ['name' => 'Heather Gray', 'value' => '#9ca3af'],
        ['name' => 'Rust', 'value' => '#dc2626'],
        ['name' => 'Cream', 'value' => '#f5f5dc'],
        ['name' => 'Burgundy', 'value' => '#991b1b'],
        ['name' => 'Khaki', 'value' => '#c19a6b'],
        ['name' => 'Maroon', 'value' => '#991b1b'],
        ['name' => 'Sage Green', 'value' => '#9ca3af'],
        ['name' => 'Light Blue', 'value' => '#3b82f6'],
        ['name' => 'Light Wash', 'value' => '#6b9bd1'],
        ['name' => 'Dark Wash', 'value' => '#1e3a8a']
    ];
    
    private array $sizes = [
        'Oversized Tees' => ['M', 'L', 'XL', 'XXL', 'XXXL'],
        'T-Shirts' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
        'Hoodies' => ['S', 'M', 'L', 'XL', 'XXL'],
        'Shirts' => ['S', 'M', 'L', 'XL', 'XXL'],
        'Capri' => ['XS', 'S', 'M', 'L', 'XL']
    ];
    
    private array $materials = [
        'Oversized Tees' => ['100% Cotton', '100% Premium Cotton', '95% Cotton, 5% Elastane'],
        'T-Shirts' => ['100% Cotton', '60% Cotton, 40% Polyester', '100% Organic Cotton'],
        'Hoodies' => ['80% Cotton, 20% Polyester', '75% Cotton, 25% Polyester', '100% Cotton'],
        'Shirts' => ['100% Cotton', '100% Cotton Flannel', '100% Cotton Denim'],
        'Capri' => ['98% Cotton, 2% Elastane', '100% Cotton', '95% Cotton, 5% Spandex']
    ];
    
    private array $fits = [
        'Oversized Tees' => ['Oversized'],
        'T-Shirts' => ['Regular', 'Slim', 'Relaxed'],
        'Hoodies' => ['Regular', 'Oversized'],
        'Shirts' => ['Regular', 'Slim', 'Relaxed'],
        'Capri' => ['Regular', 'Slim', 'High-waisted']
    ];

    public function __construct()
    {
        $this->logger = new Logger('seeder');
        Database::init();
    }

    public function run(): void
    {
        echo "🌱 Starting Product Seeder...\n";
        
        try {
            // Clear existing data
            $this->clearExistingData();
            
            // Seed categories
            $this->seedCategories();
            
            // Seed products
            $this->seedProducts();
            
            echo "✅ Product seeding completed successfully!\n";
            
        } catch (Exception $e) {
            echo "❌ Seeding failed: " . $e->getMessage() . "\n";
            $this->logger->error('Product seeding failed', [
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    private function clearExistingData(): void
    {
        echo "🧹 Clearing existing product data...\n";
        
        Database::execute("DELETE FROM product_images");
        Database::execute("DELETE FROM product_colors");
        Database::execute("DELETE FROM product_sizes");
        Database::execute("DELETE FROM products WHERE id > 4"); // Keep sample products
        Database::execute("DELETE FROM product_categories");
        
        echo "✅ Existing data cleared\n";
    }

    private function seedCategories(): void
    {
        echo "📂 Seeding categories...\n";
        
        foreach ($this->categories as $index => $category) {
            $slug = strtolower(str_replace(' ', '-', $category));
            
            $sql = "INSERT INTO product_categories (name, slug, sort_order, is_active) VALUES (?, ?, ?, 1)";
            Database::execute($sql, [$category, $slug, $index + 1]);
        }
        
        echo "✅ Categories seeded\n";
    }

    private function seedProducts(): void
    {
        echo "🛍️ Seeding products...\n";
        
        $productId = 5; // Start after existing sample products
        
        foreach ($this->categories as $category) {
            $productsPerCategory = $category === 'Oversized Tees' ? 25 : 20; // More oversized tees
            
            for ($i = 1; $i <= $productsPerCategory; $i++) {
                $this->createProduct($productId, $category, $i);
                $productId++;
            }
        }
        
        echo "✅ Products seeded\n";
    }

    private function createProduct(int $id, string $category, int $index): void
    {
        $productNames = $this->getProductNames($category);
        $name = $productNames[($index - 1) % count($productNames)] . " " . $index;
        
        $basePrice = $this->getBasePriceForCategory($category);
        $price = $basePrice + rand(-10, 20);
        $salePrice = rand(0, 100) < 30 ? $price - rand(5, 15) : null; // 30% chance of sale
        
        $isNew = rand(0, 100) < 20; // 20% chance
        $isBestSeller = rand(0, 100) < 15; // 15% chance  
        $isTrending = rand(0, 100) < 25; // 25% chance
        $isOnSale = $salePrice !== null;
        
        $rating = rand(35, 50) / 10; // 3.5 to 5.0
        $material = $this->materials[$category][array_rand($this->materials[$category])];
        $fit = $this->fits[$category][array_rand($this->fits[$category])];
        
        $description = $this->generateDescription($category, $name);
        $slug = strtolower(str_replace(' ', '-', $name));
        $sku = strtoupper(substr($category, 0, 2)) . str_pad($id, 4, '0', STR_PAD_LEFT);
        
        // Insert product
        $sql = "INSERT INTO products (
            uuid, name, slug, description, price, sale_price, sku, category, 
            material, fit, stock_quantity, is_active, is_featured, is_new, 
            is_bestseller, is_trending, is_on_sale, average_rating, 
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, 0, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        Database::execute($sql, [
            $this->generateUUID(),
            $name,
            $slug,
            $description,
            $price,
            $salePrice,
            $sku,
            $category,
            $material,
            $fit,
            rand(10, 100), // stock
            $isNew ? 1 : 0,
            $isBestSeller ? 1 : 0,
            $isTrending ? 1 : 0,
            $isOnSale ? 1 : 0,
            $rating
        ]);
        
        // Add colors and sizes
        $this->addProductColors($id, $category);
        $this->addProductSizes($id, $category);
        $this->addProductImages($id);
    }

    private function getProductNames(string $category): array
    {
        $names = [
            'Oversized Tees' => [
                'Tokyo Graphic Oversized Tee', 'Vintage Wash Oversized Tee', 'Streetwear Oversized Tee',
                'Minimalist Oversized Tee', 'Urban Oversized Tee', 'Graphic Print Oversized Tee',
                'Premium Oversized Tee', 'Classic Oversized Tee', 'Bold Print Oversized Tee',
                'Retro Oversized Tee', 'Modern Oversized Tee', 'Essential Oversized Tee'
            ],
            'T-Shirts' => [
                'Classic Crew Neck Tee', 'Vintage Graphic Tee', 'Premium V-Neck Tee',
                'Polo Neck Tee', 'Essential Basic Tee', 'Striped Tee', 'Pocket Tee',
                'Henley Tee', 'Long Sleeve Tee', 'Performance Tee'
            ],
            'Hoodies' => [
                'Essential Pullover Hoodie', 'Zip-Up Hoodie', 'Oversized Hoodie',
                'Classic Hoodie', 'Premium Hoodie', 'Graphic Hoodie', 'Fleece Hoodie',
                'Cropped Hoodie', 'Vintage Hoodie', 'Tech Hoodie'
            ],
            'Shirts' => [
                'Classic Button-Down Shirt', 'Casual Flannel Shirt', 'Denim Shirt',
                'Oxford Shirt', 'Linen Shirt', 'Chambray Shirt', 'Plaid Shirt',
                'Striped Shirt', 'Solid Shirt', 'Work Shirt'
            ],
            'Capri' => [
                'Classic Capri Pants', 'Stretch Capri', 'High-Waisted Capri',
                'Cropped Capri', 'Slim Capri', 'Wide Leg Capri', 'Cargo Capri',
                'Denim Capri', 'Linen Capri', 'Athletic Capri'
            ]
        ];

        return $names[$category] ?? ['Basic Item'];
    }

    private function getBasePriceForCategory(string $category): float
    {
        $basePrices = [
            'Oversized Tees' => 45.0,
            'T-Shirts' => 30.0,
            'Hoodies' => 75.0,
            'Shirts' => 60.0,
            'Capri' => 40.0
        ];

        return $basePrices[$category] ?? 35.0;
    }

    private function generateDescription(string $category, string $name): string
    {
        $descriptions = [
            'Oversized Tees' => [
                'Ultra-comfortable oversized fit with premium cotton blend.',
                'Relaxed oversized silhouette perfect for streetwear styling.',
                'Contemporary oversized design with superior comfort.',
                'Modern oversized cut with vintage-inspired details.'
            ],
            'T-Shirts' => [
                'Classic fit t-shirt made from premium cotton.',
                'Essential wardrobe piece with perfect fit.',
                'Comfortable everyday tee with modern styling.',
                'Versatile t-shirt suitable for any occasion.'
            ],
            'Hoodies' => [
                'Cozy hoodie perfect for layering and comfort.',
                'Premium hoodie with adjustable hood and kangaroo pocket.',
                'Comfortable pullover hoodie for casual wear.',
                'Stylish hoodie with modern fit and feel.'
            ],
            'Shirts' => [
                'Classic shirt with clean lines and versatile styling.',
                'Premium shirt perfect for business casual occasions.',
                'Comfortable shirt with modern fit and quality construction.',
                'Versatile shirt suitable for both casual and formal wear.'
            ],
            'Capri' => [
                'Comfortable capri pants perfect for warm weather.',
                'Stylish capri with modern fit and premium fabric.',
                'Versatile capri pants suitable for casual occasions.',
                'Contemporary capri design with comfortable stretch.'
            ]
        ];

        $categoryDescriptions = $descriptions[$category] ?? ['Quality garment with modern styling.'];
        return $categoryDescriptions[array_rand($categoryDescriptions)];
    }

    private function addProductColors(int $productId, string $category): void
    {
        $numColors = rand(2, 4); // 2-4 colors per product
        $selectedColors = array_rand($this->colors, $numColors);

        if (!is_array($selectedColors)) {
            $selectedColors = [$selectedColors];
        }

        foreach ($selectedColors as $index => $colorIndex) {
            $color = $this->colors[$colorIndex];

            $sql = "INSERT INTO product_colors (product_id, name, hex_value, sort_order, is_available)
                    VALUES (?, ?, ?, ?, 1)";
            Database::execute($sql, [$productId, $color['name'], $color['value'], $index]);
        }
    }

    private function addProductSizes(int $productId, string $category): void
    {
        $categorySizes = $this->sizes[$category] ?? ['S', 'M', 'L', 'XL'];

        foreach ($categorySizes as $index => $size) {
            $stock = rand(5, 50);

            $sql = "INSERT INTO product_sizes (product_id, size, size_order, stock_quantity, is_available)
                    VALUES (?, ?, ?, ?, 1)";
            Database::execute($sql, [$productId, $size, $index, $stock]);
        }
    }

    private function addProductImages(int $productId): void
    {
        $imageUrls = [
            "https://images.unsplash.com/photo-1583743814966-8936f5b7be1a?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",
            "https://images.unsplash.com/photo-1562157873-818bc0726f68?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",
            "https://images.unsplash.com/photo-1564859228953-04fb1dea5cc7?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",
            "https://images.unsplash.com/photo-1503341733017-1901578f9f1e?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3",
            "https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=700&auto=format&fit=crop&ixlib=rb-4.0.3"
        ];

        foreach ($imageUrls as $index => $url) {
            $sql = "INSERT INTO product_images (product_id, image_url, alt_text, sort_order, is_primary)
                    VALUES (?, ?, ?, ?, ?)";
            Database::execute($sql, [
                $productId,
                $url,
                "Product image " . ($index + 1),
                $index,
                $index === 0 ? 1 : 0
            ]);
        }
    }

    private function generateUUID(): string
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}

// Run the seeder
if (php_sapi_name() === 'cli') {
    $seeder = new ProductSeeder();
    $seeder->run();
}
