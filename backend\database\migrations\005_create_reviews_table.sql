-- Create reviews table for product reviews
CREATE TABLE IF NOT EXISTS reviews (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    product_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Review content
    title VARCHAR(255) NOT NULL,
    comment TEXT NOT NULL,
    rating TINYINT UNSIGNED NOT NULL CHECK (rating >= 1 AND rating <= 5),
    
    -- Review metadata
    is_verified_purchase BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_approved BOOLEAN DEFAULT TRUE,
    
    -- Helpful votes
    helpful_count INT DEFAULT 0,
    not_helpful_count INT DEFAULT 0,
    
    -- Product variant details (what was actually purchased/reviewed)
    reviewed_color VARCHAR(100),
    reviewed_size VARCHAR(20),
    
    -- Review quality metrics
    review_length INT GENERATED ALWAYS AS (CHAR_LENGTH(comment)) STORED,
    has_images BOOLEAN DEFAULT FALSE,
    
    -- Moderation
    flagged_count INT DEFAULT 0,
    moderation_status ENUM('pending', 'approved', 'rejected', 'flagged') DEFAULT 'approved',
    moderation_notes TEXT,
    moderated_by INT UNSIGNED NULL,
    moderated_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (moderated_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique constraint to prevent multiple reviews per user per product
    UNIQUE KEY unique_user_product_review (user_id, product_id),
    
    -- Indexes
    INDEX idx_product_id (product_id),
    INDEX idx_user_id (user_id),
    INDEX idx_rating (rating),
    INDEX idx_is_verified_purchase (is_verified_purchase),
    INDEX idx_is_featured (is_featured),
    INDEX idx_is_approved (is_approved),
    INDEX idx_helpful_count (helpful_count),
    INDEX idx_moderation_status (moderation_status),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (title, comment)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create review_images table for review photos
CREATE TABLE IF NOT EXISTS review_images (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id INT UNSIGNED NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    sort_order INT DEFAULT 0,
    
    -- Image metadata
    file_size INT,
    width INT,
    height INT,
    format VARCHAR(10),
    
    -- Moderation
    is_approved BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_review_id (review_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_approved (is_approved)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create review_votes table for helpful/not helpful votes
CREATE TABLE IF NOT EXISTS review_votes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    vote_type ENUM('helpful', 'not_helpful') NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent multiple votes per user per review
    UNIQUE KEY unique_user_review_vote (user_id, review_id),
    
    -- Indexes
    INDEX idx_review_id (review_id),
    INDEX idx_user_id (user_id),
    INDEX idx_vote_type (vote_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create review_flags table for reporting inappropriate reviews
CREATE TABLE IF NOT EXISTS review_flags (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    reason ENUM('spam', 'inappropriate', 'fake', 'offensive', 'other') NOT NULL,
    description TEXT,
    
    -- Flag status
    status ENUM('pending', 'reviewed', 'resolved', 'dismissed') DEFAULT 'pending',
    resolved_by INT UNSIGNED NULL,
    resolved_at TIMESTAMP NULL,
    resolution_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Unique constraint to prevent multiple flags per user per review
    UNIQUE KEY unique_user_review_flag (user_id, review_id),
    
    -- Indexes
    INDEX idx_review_id (review_id),
    INDEX idx_user_id (user_id),
    INDEX idx_reason (reason),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create review_responses table for merchant responses to reviews
CREATE TABLE IF NOT EXISTS review_responses (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL, -- Admin/merchant user
    response TEXT NOT NULL,
    
    -- Response metadata
    is_official BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent multiple official responses per review
    UNIQUE KEY unique_review_official_response (review_id, is_official),
    
    -- Indexes
    INDEX idx_review_id (review_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_official (is_official),
    INDEX idx_is_public (is_public),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create review_analytics table for tracking review behavior
CREATE TABLE IF NOT EXISTS review_analytics (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    review_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NULL, -- NULL for anonymous views
    action ENUM('viewed', 'voted_helpful', 'voted_not_helpful', 'flagged', 'shared') NOT NULL,
    
    -- Context data
    source VARCHAR(100), -- 'product_page', 'review_page', 'email', etc.
    device_type VARCHAR(50),
    session_id VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (review_id) REFERENCES reviews(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_review_id (review_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_source (source),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
