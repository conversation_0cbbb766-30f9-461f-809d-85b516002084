import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, RotateCcw, Shirt, Truck } from 'lucide-react';

export default function ProductInfo() {
  const [openSection, setOpenSection] = useState(null);

  const toggleSection = (index) => {
    setOpenSection(openSection === index ? null : index);
  };

  const sections = [
    {
      title: "Fabric & Care",
      icon: <Shirt className="text-indigo-400" size={18} />,
      content: (
        <div className="space-y-3">
          <div>
            <h4 className="text-white text-sm font-medium mb-1">Fabric Composition</h4>
            <p className="text-gray-400 text-sm">100% Premium Cotton, 260g/m²</p>
          </div>
          <div>
            <h4 className="text-white text-sm font-medium mb-1">Care Instructions</h4>
            <ul className="text-gray-400 text-sm list-disc pl-5 space-y-1">
              <li>Machine wash cold with similar colors</li>
              <li>Tumble dry low or hang dry for best results</li>
              <li>Do not bleach, iron on graphic</li>
              <li>Wash inside out to preserve print quality</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "Returns & Refunds",
      icon: <RotateCcw className="text-indigo-400" size={18} />,
      content: (
        <div className="space-y-3">
          <p className="text-gray-400 text-sm">
            We want you to be completely satisfied with your purchase. If you're not happy for any reason, we offer a hassle-free return policy.
          </p>
          <div>
            <h4 className="text-white text-sm font-medium mb-1">Return Policy</h4>
            <ul className="text-gray-400 text-sm list-disc pl-5 space-y-1">
              <li>30-day return window from delivery date</li>
              <li>Items must be unworn, unwashed, and with tags attached</li>
              <li>Original packaging required</li>
              <li>Free returns for orders over $100</li>
            </ul>
          </div>
          <p className="text-gray-400 text-sm">
            For more information, please visit our <a href="/returns" className="text-indigo-400 hover:underline">Returns & Refunds</a> page.
          </p>
        </div>
      )
    },
    {
      title: "Shipping Information",
      icon: <Truck className="text-indigo-400" size={18} />,
      content: (
        <div className="space-y-3">
          <div>
            <h4 className="text-white text-sm font-medium mb-1">Delivery Options</h4>
            <ul className="text-gray-400 text-sm list-disc pl-5 space-y-1">
              <li>Standard Shipping (5-7 business days): $5.99</li>
              <li>Express Shipping (2-3 business days): $12.99</li>
              <li>Free standard shipping on orders over $150</li>
            </ul>
          </div>
          <p className="text-gray-400 text-sm">
            Orders are processed within 24 hours. Tracking information will be provided via email once your order ships.
          </p>
        </div>
      )
    },
  ];

  return (
    <div className="space-y-3 mb-2">
      {sections.map((section, index) => (
        <div key={index} className="border border-gray-800 rounded-md overflow-hidden">
          <button
            className="w-full flex items-center justify-between p-4 hover:bg-gray-800/50 transition-colors"
            onClick={() => toggleSection(index)}
          >
            <div className="flex items-center gap-2">
              {section.icon}
              <span className="font-medium text-white">{section.title}</span>
            </div>
            <motion.div
              animate={{ rotate: openSection === index ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown size={18} className="text-gray-400" />
            </motion.div>
          </button>

          <AnimatePresence>
            {openSection === index && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="overflow-hidden"
              >
                <div className="p-4 pt-0 border-t border-gray-800">
                  {section.content}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  );
}