import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Info, X, Ruler, Shirt, Award, Zap } from 'lucide-react';

export default function InteractiveHotspots({ 
  hotspots = [], 
  className = '',
  showOnHover = true,
  alwaysVisible = false 
}) {
  const [activeHotspot, setActiveHotspot] = useState(null);
  const [isContainerHovered, setIsContainerHovered] = useState(false);
  const containerRef = useRef(null);

  // Default hotspot data if none provided
  const defaultHotspots = [
    {
      id: 1,
      x: 25, // percentage from left
      y: 30, // percentage from top
      type: 'material',
      title: 'Premium Cotton',
      description: '100% organic cotton blend for ultimate comfort and breathability',
      icon: Shirt,
      color: 'blue'
    },
    {
      id: 2,
      x: 70,
      y: 45,
      type: 'feature',
      title: 'Reinforced Stitching',
      description: 'Double-stitched seams for enhanced durability and longevity',
      icon: Award,
      color: 'green'
    },
    {
      id: 3,
      x: 50,
      y: 70,
      type: 'size',
      title: 'Size Guide',
      description: 'Click to view detailed size measurements and fit guide',
      icon: Ruler,
      color: 'purple'
    },
    {
      id: 4,
      x: 80,
      y: 25,
      type: 'care',
      title: 'Easy Care',
      description: 'Machine washable, wrinkle-resistant fabric technology',
      icon: Zap,
      color: 'orange'
    }
  ];

  const activeHotspots = hotspots.length > 0 ? hotspots : defaultHotspots;

  const getHotspotColor = (color) => {
    const colors = {
      blue: 'bg-blue-500 border-blue-400 text-blue-100',
      green: 'bg-green-500 border-green-400 text-green-100',
      purple: 'bg-purple-500 border-purple-400 text-purple-100',
      orange: 'bg-orange-500 border-orange-400 text-orange-100',
      red: 'bg-red-500 border-red-400 text-red-100',
      teal: 'bg-teal-500 border-teal-400 text-teal-100'
    };
    return colors[color] || colors.blue;
  };

  const getTooltipBgColor = (color) => {
    const colors = {
      blue: 'bg-blue-600/95',
      green: 'bg-green-600/95',
      purple: 'bg-purple-600/95',
      orange: 'bg-orange-600/95',
      red: 'bg-red-600/95',
      teal: 'bg-teal-600/95'
    };
    return colors[color] || colors.blue;
  };

  const handleHotspotClick = (hotspot, e) => {
    e.stopPropagation();
    setActiveHotspot(activeHotspot?.id === hotspot.id ? null : hotspot);
  };

  const closeTooltip = () => {
    setActiveHotspot(null);
  };

  const shouldShowHotspots = alwaysVisible || (showOnHover && isContainerHovered);

  return (
    <div 
      ref={containerRef}
      className={`relative w-full h-full ${className}`}
      onMouseEnter={() => setIsContainerHovered(true)}
      onMouseLeave={() => {
        setIsContainerHovered(false);
        if (!alwaysVisible) {
          setActiveHotspot(null);
        }
      }}
    >
      {/* Hotspot Markers */}
      <AnimatePresence>
        {shouldShowHotspots && activeHotspots.map((hotspot) => {
          const IconComponent = hotspot.icon || Info;
          const isActive = activeHotspot?.id === hotspot.id;
          
          return (
            <motion.div
              key={hotspot.id}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ 
                duration: 0.3, 
                delay: hotspot.id * 0.1,
                type: "spring",
                stiffness: 300
              }}
              className="absolute z-20"
              style={{
                left: `${hotspot.x}%`,
                top: `${hotspot.y}%`,
                transform: 'translate(-50%, -50%)'
              }}
            >
              {/* Pulsing Ring */}
              <motion.div
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.7, 0, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className={`absolute inset-0 rounded-full border-2 ${
                  hotspot.color === 'blue' ? 'border-blue-400' :
                  hotspot.color === 'green' ? 'border-green-400' :
                  hotspot.color === 'purple' ? 'border-purple-400' :
                  hotspot.color === 'orange' ? 'border-orange-400' :
                  hotspot.color === 'red' ? 'border-red-400' :
                  'border-teal-400'
                }`}
              />
              
              {/* Hotspot Button */}
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => handleHotspotClick(hotspot, e)}
                className={`relative w-8 h-8 rounded-full border-2 backdrop-blur-sm transition-all duration-200 flex items-center justify-center ${
                  isActive 
                    ? `${getHotspotColor(hotspot.color)} scale-110 shadow-lg` 
                    : `bg-white/20 border-white/40 text-white hover:${getHotspotColor(hotspot.color)}`
                }`}
              >
                <IconComponent size={14} />
              </motion.button>

              {/* Tooltip */}
              <AnimatePresence>
                {isActive && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 10 }}
                    className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-30"
                  >
                    <div className={`${getTooltipBgColor(hotspot.color)} backdrop-blur-xl rounded-lg p-4 shadow-xl border border-white/20 max-w-xs`}>
                      {/* Close Button */}
                      <button
                        onClick={closeTooltip}
                        className="absolute top-2 right-2 w-6 h-6 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-colors"
                      >
                        <X size={12} className="text-white" />
                      </button>
                      
                      {/* Content */}
                      <div className="pr-6">
                        <div className="flex items-center gap-2 mb-2">
                          <IconComponent size={16} className="text-white" />
                          <h3 className="text-white font-semibold text-sm">
                            {hotspot.title}
                          </h3>
                        </div>
                        <p className="text-white/90 text-xs leading-relaxed">
                          {hotspot.description}
                        </p>
                        
                        {/* Additional Actions */}
                        {hotspot.type === 'size' && (
                          <motion.button
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                            className="mt-3 w-full bg-white/20 hover:bg-white/30 text-white text-xs py-2 px-3 rounded-md transition-colors"
                            onClick={() => {
                              // Handle size guide action
                              console.log('Open size guide');
                            }}
                          >
                            View Size Guide
                          </motion.button>
                        )}
                        
                        {hotspot.type === 'material' && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            <span className="bg-white/20 text-white text-xs px-2 py-1 rounded-full">
                              Organic
                            </span>
                            <span className="bg-white/20 text-white text-xs px-2 py-1 rounded-full">
                              Breathable
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {/* Tooltip Arrow */}
                      <div 
                        className={`absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 rotate-45 ${getTooltipBgColor(hotspot.color)} border-l border-t border-white/20`}
                      />
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Instructions Overlay */}
      <AnimatePresence>
        {shouldShowHotspots && !activeHotspot && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute top-4 left-4 bg-black/70 text-white text-xs px-3 py-2 rounded-full backdrop-blur-sm z-20"
          >
            Click hotspots to learn more
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
