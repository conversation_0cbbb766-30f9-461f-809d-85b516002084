# Wolffoxx Ecommerce Backend API

A comprehensive PHP 8.1+ backend API for the Wolffoxx ecommerce clothing platform, featuring modern architecture, robust security, and scalable design.

## 🚀 Features

### Core Functionality
- **User Authentication & Authorization** - JWT-based auth with refresh tokens
- **User Profile Management** - Complete profile CRUD with image uploads
- **Wishlist System** - Advanced wishlist with collections and sharing
- **Saved Outfits Management** - Create, organize, and share outfit collections
- **Product Management** - Full product catalog with variants and search
- **Review System** - Product reviews with moderation and analytics

### Security & Performance
- **Modern PHP 8.1+** with strict typing and OOP architecture
- **JWT Authentication** with secure token management
- **Rate Limiting** to prevent abuse and ensure fair usage
- **Input Validation** with comprehensive sanitization
- **SQL Injection Protection** using prepared statements
- **CORS Handling** with configurable origins and methods
- **Comprehensive Logging** with structured logging and rotation

### Technical Features
- **RESTful API Design** with proper HTTP status codes
- **Database Migrations** for version-controlled schema changes
- **Email Service** with template support and SMTP configuration
- **File Upload Handling** with validation and security
- **Caching Strategies** for optimal performance
- **Error Handling** with detailed logging and user-friendly responses

## 📋 Requirements

- **PHP 8.1+** with extensions:
  - PDO MySQL
  - OpenSSL
  - JSON
  - Mbstring
  - Fileinfo
- **MySQL 8.0+** or **MariaDB 10.6+**
- **Composer** for dependency management
- **Web Server** (Apache/Nginx) with URL rewriting

## 🛠️ Installation

### 1. Clone and Setup

```bash
# Navigate to your backend directory
cd backend

# Install dependencies
composer install

# Copy environment configuration
cp .env.example .env
```

### 2. Environment Configuration

Edit `.env` file with your settings:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wolffoxx_ecommerce
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Wolffoxx"

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173
```

### 3. Database Setup

```bash
# Create database
mysql -u root -p -e "CREATE DATABASE wolffoxx_ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
php scripts/migrate.php
```

### 4. Web Server Configuration

#### Apache (.htaccess included)
Ensure mod_rewrite is enabled and AllowOverride is set to All.

#### Nginx
```nginx
server {
    listen 80;
    server_name api.wolffoxx.local;
    root /path/to/backend;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### 5. Permissions

```bash
# Set proper permissions
chmod -R 755 backend/
chmod -R 777 backend/storage/
chmod -R 777 backend/storage/logs/
```

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "newsletter_subscribed": true
}
```

#### Login User
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword",
  "remember_me": true
}
```

#### Refresh Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "your_refresh_token_here"
}
```

### Wishlist Endpoints

#### Get User Wishlist
```http
GET /api/v1/wishlist
Authorization: Bearer your_access_token
```

#### Add Item to Wishlist
```http
POST /api/v1/wishlist/items
Authorization: Bearer your_access_token
Content-Type: application/json

{
  "product_id": 123,
  "notes": "Love this design!",
  "priority": "high",
  "is_public": false
}
```

#### Remove Item from Wishlist
```http
DELETE /api/v1/wishlist/items/123
Authorization: Bearer your_access_token
```

### Product Endpoints

#### Get Products
```http
GET /api/v1/products?category=t-shirts&page=1&limit=20&sort_by=price&sort_order=ASC
```

#### Get Product Details
```http
GET /api/v1/products/123
```

#### Search Products
```http
GET /api/v1/products/search?q=oversized&category=t-shirts&price_min=20&price_max=100
```

## 🔧 Development

### Running Tests
```bash
composer test
```

### Code Style
```bash
# Check code style
composer cs-check

# Fix code style
composer cs-fix
```

### Logging
Logs are stored in `storage/logs/` with automatic rotation:
- `app.log` - General application logs
- `error.log` - Error-level logs only
- `auth.log` - Authentication-related logs

### Database Migrations
```bash
# Run all pending migrations
php scripts/migrate.php

# Create new migration
php scripts/create-migration.php create_new_table
```

## 🚀 Deployment

### Production Checklist

1. **Environment Configuration**
   - Set `APP_ENV=production`
   - Set `APP_DEBUG=false`
   - Use strong `JWT_SECRET` (32+ characters)
   - Configure proper database credentials
   - Set up email service (SMTP/SendGrid/etc.)

2. **Security**
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up rate limiting
   - Regular security updates

3. **Performance**
   - Enable OPcache
   - Configure database connection pooling
   - Set up Redis for caching (optional)
   - Optimize database indexes

4. **Monitoring**
   - Set up log monitoring
   - Configure error tracking
   - Monitor API performance
   - Set up health checks

### Docker Deployment (Optional)
```dockerfile
FROM php:8.1-fpm-alpine

# Install dependencies
RUN apk add --no-cache \
    mysql-client \
    && docker-php-ext-install pdo pdo_mysql

# Copy application
COPY . /var/www/html
WORKDIR /var/www/html

# Install composer dependencies
RUN composer install --no-dev --optimize-autoloader

EXPOSE 9000
CMD ["php-fpm"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the logs in `storage/logs/`

## 🔄 API Versioning

The API uses URL versioning (`/api/v1/`). Breaking changes will increment the version number.

## 📊 Monitoring

### Health Check
```http
GET /api/v1/health
```

Returns system status and version information.

### Rate Limiting
- Default: 100 requests per hour per IP/user
- Configurable via environment variables
- Returns rate limit headers in responses
