// updateProductImages.js - Replace demo images with real Cloudinary URLs
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'wolffoxx',
  port: process.env.DB_PORT || 3306
};

// Cloudinary base URL
const CLOUDINARY_BASE = 'https://res.cloudinary.com/dsp0zmfcx/image/upload/v1752099';

// Mapping of image filenames to actual product IDs from your database
const imageMapping = {
  // Oversized Tees (category_id = 1)
  'oversized1': {
    productId: 1, // Oversized Tokyo Graphic Tee
    images: [
      { file: 'oversized1(1).jpg', isPrimary: true },
      { file: 'oversized1(2).jpg', isPrimary: false },
      { file: 'oversized1(3).jpg', isPrimary: false }
    ]
  },
  'oversized2': {
    productId: 2, // Vintage Wash Oversized Tee
    images: [
      { file: 'oversized2BL(1).jpg', isPrimary: true },
      { file: 'oversized2BL(2).jpg', isPrimary: false },
      { file: 'oversized2W(1).jpg', isPrimary: false },
      { file: 'oversized2W(2).jpg', isPrimary: false }
    ]
  },
  'oversized3': {
    productId: 3, // Streetwear Oversized Tee
    images: [
      { file: 'oversized3(1).jpg', isPrimary: true },
      { file: 'oversized3(2).jpg', isPrimary: false }
    ]
  },
  'oversized4': {
    productId: 4, // Minimalist Oversized Tee
    images: [
      { file: 'oversized4(1).jpg', isPrimary: true },
      { file: 'oversized4(2).jpg', isPrimary: false },
      { file: 'oversized4(3).jpg', isPrimary: false }
    ]
  },
  'oversized5': {
    productId: 10, // Urban Oversized Tee
    images: [
      { file: 'oversized5(1).png', isPrimary: true },
      { file: 'oversized5(2).png', isPrimary: false }
    ]
  },

  // T-Shirts (category_id = 2)
  'tshirt1': {
    productId: 5, // Classic Crew Neck Tee
    images: [
      { file: 'tshirt1(1).jpg', isPrimary: true },
      { file: 'tshirt1(2).jpg', isPrimary: false },
      { file: 'tshirt1(3).jpg', isPrimary: false }
    ]
  },
  'tshirt2': {
    productId: 6, // Vintage Graphic Tee
    images: [
      { file: 'tshirt2B(1).jpg', isPrimary: true },
      { file: 'tshirt2B(2).jpg', isPrimary: false },
      { file: 'tshirt2B(3).jpg', isPrimary: false },
      { file: 'tshirt2R(1).jpg', isPrimary: false },
      { file: 'tshirt2R(2).jpg', isPrimary: false }
    ]
  },
  'tshirt3': {
    productId: 7, // Premium V-Neck Tee
    images: [
      { file: 'tshirt3(1).jpg', isPrimary: true },
      { file: 'tshirt3(2).jpg', isPrimary: false },
      { file: 'tshirt3(3).jpg', isPrimary: false }
    ]
  },

  // Additional mappings for remaining images
  'oversized6': {
    productId: 1, // Map to existing product (can have multiple image sets)
    images: [
      { file: 'oversized6(1).png', isPrimary: false }
    ]
  },
  'oversized7': {
    productId: 2,
    images: [
      { file: 'oversized7(1).png', isPrimary: false },
      { file: 'oversized7(2).png', isPrimary: false }
    ]
  },
  'oversized8': {
    productId: 3,
    images: [
      { file: 'oversized8(1).png', isPrimary: false },
      { file: 'oversized8(2).png', isPrimary: false }
    ]
  },
  'printedshirt1': {
    productId: 4,
    images: [
      { file: 'printedshirt1(1).png', isPrimary: false },
      { file: 'printedshirt1(2).png', isPrimary: false },
      { file: 'printedshirt1(3).png', isPrimary: false }
    ]
  },
  'shirt1': {
    productId: 5,
    images: [
      { file: 'shirt1BL(1).png', isPrimary: false },
      { file: 'shirt1BL(2).png', isPrimary: false },
      { file: 'shirt1W(1).png', isPrimary: false },
      { file: 'shirt1W(2).png', isPrimary: false },
      { file: 'shirt1W(3).png', isPrimary: false },
      { file: 'shirt1W(4).jpg', isPrimary: false }
    ]
  }
};

// Function to generate Cloudinary URL
const generateCloudinaryUrl = (filename) => {
  // Remove extension and encode special characters
  const nameWithoutExt = filename.replace(/\.[^/.]+$/, "");
  const encodedName = encodeURIComponent(nameWithoutExt);
  const extension = filename.split('.').pop();
  
  return `${CLOUDINARY_BASE}/ProductsImages/${encodedName}.${extension}`;
};

// Function to find product by ID (simpler approach)
const findProductById = async (connection, productId) => {
  try {
    const sql = "SELECT id, name FROM products WHERE id = ?";
    const [rows] = await connection.execute(sql, [productId]);

    if (rows.length === 0) {
      console.log(`⚠️  No product found with ID: ${productId}`);
      return null;
    }

    return rows[0];
  } catch (error) {
    console.error(`Error finding product ${productId}:`, error.message);
    return null;
  }
};

// Function to clear existing product images
const clearProductImages = async (connection, productId) => {
  try {
    await connection.execute("DELETE FROM product_images WHERE product_id = ?", [productId]);
    console.log(`🗑️  Cleared existing images for product ID: ${productId}`);
  } catch (error) {
    console.error(`Error clearing images for product ${productId}:`, error.message);
  }
};

// Function to insert product image
const insertProductImage = async (connection, productId, imageData, sortOrder) => {
  try {
    const cloudinaryUrl = generateCloudinaryUrl(imageData.file);

    const sql = `INSERT INTO product_images
                 (product_id, image_url, alt_text, sort_order, is_primary, created_at)
                 VALUES (?, ?, ?, ?, ?, NOW())`;

    await connection.execute(sql, [
      productId,
      cloudinaryUrl,
      `Product image ${sortOrder + 1}`,
      sortOrder,
      imageData.isPrimary ? 1 : 0
    ]);

    console.log(`✅ Added image: ${imageData.file} -> Product ID: ${productId}`);
    return true;
  } catch (error) {
    console.error(`❌ Failed to insert image ${imageData.file}:`, error.message);
    return false;
  }
};

// Main function to update all product images
const updateProductImages = async () => {
  let connection;
  
  try {
    console.log('🚀 Starting product image update process...');
    console.log('📊 Connecting to database...');
    
    // Create database connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');
    
    let totalUpdated = 0;
    let totalImages = 0;
    
    // Process each product mapping
    for (const [productKey, productData] of Object.entries(imageMapping)) {
      console.log(`\n📦 Processing: ${productKey} -> Product ID: ${productData.productId}`);

      // Find the product in database
      const product = await findProductById(connection, productData.productId);

      if (!product) {
        console.log(`⚠️  Skipping ${productKey} - product ID ${productData.productId} not found`);
        continue;
      }

      console.log(`🎯 Found product: "${product.name}" (ID: ${product.id})`);

      // Clear existing images for this product
      await clearProductImages(connection, product.id);

      // Insert new images
      let imageCount = 0;
      for (const [index, imageData] of productData.images.entries()) {
        const success = await insertProductImage(connection, product.id, imageData, index);
        if (success) {
          imageCount++;
          totalImages++;
        }
      }

      if (imageCount > 0) {
        totalUpdated++;
        console.log(`✅ Updated ${imageCount} images for "${product.name}"`);
      }
    }
    
    console.log('\n' + '─'.repeat(60));
    console.log('📊 UPDATE SUMMARY:');
    console.log(`✅ Products updated: ${totalUpdated}`);
    console.log(`📸 Total images added: ${totalImages}`);
    console.log('🎉 Product image update completed successfully!');
    
  } catch (error) {
    console.error('💥 Fatal error during update:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
};

// Run the update
updateProductImages();
