<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #000; color: #fff; }
        button { padding: 10px 20px; margin: 10px; background: #FF6B35; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #e55a2b; }
        .result { background: #1a1a1a; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #404040; }
        .error { background: #2a1a1a; border-color: #ff4444; }
        .success { background: #1a2a1a; border-color: #44ff44; }
    </style>
</head>
<body>
    <h1>Wolffoxx API Test</h1>
    
    <div>
        <h2>Authentication</h2>
        <button onclick="checkAuth()">Check Auth Status</button>
        <div id="auth-result" class="result"></div>
    </div>

    <div>
        <h2>Wishlist</h2>
        <button onclick="getWishlist()">Get Wishlist</button>
        <button onclick="addToWishlist()">Add Product 1 to Wishlist</button>
        <button onclick="removeFromWishlist()">Remove Product 1 from Wishlist</button>
        <div id="wishlist-result" class="result"></div>
    </div>

    <div>
        <h2>Cart</h2>
        <button onclick="getCart()">Get Cart</button>
        <button onclick="addToCart()">Add Product 1 to Cart</button>
        <div id="cart-result" class="result"></div>
    </div>

    <div>
        <h2>Orders</h2>
        <button onclick="createOrder()">Create Test Order</button>
        <button onclick="getOrders()">Get Orders</button>
        <div id="order-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        function getAuthToken() {
            try {
                const tokens = localStorage.getItem('wolffoxx_tokens');
                if (tokens) {
                    const parsedTokens = JSON.parse(tokens);
                    return parsedTokens.access_token || parsedTokens.accessToken;
                }
                return null;
            } catch (error) {
                console.error('Error getting auth token:', error);
                return null;
            }
        }

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
            element.className = `result ${isError ? 'error' : 'success'}`;
        }

        async function checkAuth() {
            const token = getAuthToken();
            displayResult('auth-result', {
                hasToken: !!token,
                tokenPreview: token ? token.substring(0, 20) + '...' : null
            });
        }

        async function getWishlist() {
            try {
                const response = await fetch(`${API_BASE}/wishlist`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                const data = await response.json();
                displayResult('wishlist-result', data, !response.ok);
            } catch (error) {
                displayResult('wishlist-result', { error: error.message }, true);
            }
        }

        async function addToWishlist() {
            try {
                const response = await fetch(`${API_BASE}/wishlist/items`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: JSON.stringify({
                        product_id: 1,
                        notes: 'Test wishlist item'
                    })
                });
                const data = await response.json();
                displayResult('wishlist-result', data, !response.ok);
            } catch (error) {
                displayResult('wishlist-result', { error: error.message }, true);
            }
        }

        async function removeFromWishlist() {
            try {
                const response = await fetch(`${API_BASE}/wishlist/items/1`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                const data = await response.json();
                displayResult('wishlist-result', data, !response.ok);
            } catch (error) {
                displayResult('wishlist-result', { error: error.message }, true);
            }
        }

        async function getCart() {
            try {
                const response = await fetch(`${API_BASE}/cart`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                const data = await response.json();
                displayResult('cart-result', data, !response.ok);
            } catch (error) {
                displayResult('cart-result', { error: error.message }, true);
            }
        }

        async function addToCart() {
            try {
                const response = await fetch(`${API_BASE}/cart/items`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: JSON.stringify({
                        product_id: 1,
                        selected_color: 'Black',
                        selected_size: 'M',
                        quantity: 1,
                        unit_price: 29.99,
                        product_name: 'Test Product'
                    })
                });
                const data = await response.json();
                displayResult('cart-result', data, !response.ok);
            } catch (error) {
                displayResult('cart-result', { error: error.message }, true);
            }
        }

        async function createOrder() {
            try {
                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${getAuthToken()}`
                    },
                    body: JSON.stringify({
                        customer_name: 'Test Customer',
                        shipping_address: {
                            street: '123 Test St',
                            city: 'Test City',
                            state: 'Test State',
                            zip: '12345',
                            country: 'India'
                        },
                        billing_address: {
                            street: '123 Test St',
                            city: 'Test City',
                            state: 'Test State',
                            zip: '12345',
                            country: 'India'
                        },
                        payment_method: 'test_payment'
                    })
                });
                const data = await response.json();
                displayResult('order-result', data, !response.ok);
            } catch (error) {
                displayResult('order-result', { error: error.message }, true);
            }
        }

        async function getOrders() {
            try {
                const response = await fetch(`${API_BASE}/orders`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                const data = await response.json();
                displayResult('order-result', data, !response.ok);
            } catch (error) {
                displayResult('order-result', { error: error.message }, true);
            }
        }
    </script>
</body>
</html>
