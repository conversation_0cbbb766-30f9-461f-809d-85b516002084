import ProductGrid from './ProductGrid';

const newDropsProducts = [
  {
    id: "101",
    name: "Urban Tech Hoodie",
    price: 89.99,
    variants: [
      {
        color: "#000000",
        colorName: "Black",
        image: "https://images.unsplash.com/photo-1556821840-3a63f95609a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      },
      {
        color: "#C0C0C0",
        colorName: "Gray",
        image: "https://images.unsplash.com/photo-1556306535-0f09a537f0a3?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      },
      {
        color: "#800020",
        colorName: "Burgundy",
        image: "https://images.unsplash.com/photo-1552902865-b72c031ac5ea?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      }
    ]
  },
  {
    id: "102",
    name: "Streetwear Cargo Pants",
    price: 64.99,
    variants: [
      {
        color: "#5D4037",
        colorName: "Brown",
        image: "https://images.unsplash.com/photo-1551854590-dc9c6265b1b1?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      },
      {
        color: "#263238",
        colorName: "Dark Navy",
        image: "https://images.unsplash.com/photo-1551833726-b6e7210db161?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      }
    ]
  },
  {
    id: "103",
    name: "Graphic Print Tee",
    price: 34.99,
    variants: [
      {
        color: "#FFFFFF",
        colorName: "White",
        image: "https://images.unsplash.com/photo-1574180566232-aaad1b5b8450?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      },
      {
        color: "#000000",
        colorName: "Black",
        image: "https://images.unsplash.com/photo-1503341733017-1901578f9f1e?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      }
    ]
  },
  {
    id: "104",
    name: "Urban Bomber Jacket",
    price: 99.99,
    variants: [
      {
        color: "#000000",
        colorName: "Black",
        image: "https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      },
      {
        color: "#006400",
        colorName: "Olive",
        image: "https://images.unsplash.com/photo-1548883354-7622d03aca27?ixlib=rb-1.2.1&auto=format&fit=crop&w=668&q=80 "
      }
    ]
  }
];

const NewDropsSection = () => {
  return (
    <div className="py-10 bg-gray-50">
      <div className="container mx-auto">
        <div className="flex justify-between items-center mb-8 px-4">
          <h2 className="text-3xl font-bold">NEW DROPS</h2>
          <a href="/collections/new" className="text-black font-medium hover:underline">
            View All
          </a>
        </div>
        <ProductGrid products={newDropsProducts} />
      </div>
    </div>
  );
};

export default NewDropsSection;