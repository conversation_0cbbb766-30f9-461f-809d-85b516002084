<?php
// Set headers for JSON response
header('Content-Type: application/json');

// Database connection
$conn = new mysqli('localhost', 'root', '', 'wolffoxx');

// Check connection
if ($conn->connect_error) {
    die(json_encode(['error' => 'Connection failed: ' . $conn->connect_error]));
}

// Get query parameters
$is_sale = isset($_GET['is_sale']) ? $_GET['is_sale'] : null;
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$per_page = isset($_GET['per_page']) ? intval($_GET['per_page']) : 20;

// Build the query
$sql = "SELECT * FROM products";
$where_clauses = [];

if ($is_sale !== null) {
    $where_clauses[] = "is_sale = " . intval($is_sale);
}

if (!empty($where_clauses)) {
    $sql .= " WHERE " . implode(' AND ', $where_clauses);
}

// Add pagination
$offset = ($page - 1) * $per_page;
$sql .= " LIMIT $per_page OFFSET $offset";

// Execute query
$result = $conn->query($sql);

if (!$result) {
    die(json_encode(['error' => 'Query failed: ' . $conn->error]));
}

// Count total records for pagination
$count_sql = "SELECT COUNT(*) as total FROM products";
if (!empty($where_clauses)) {
    $count_sql .= " WHERE " . implode(' AND ', $where_clauses);
}
$count_result = $conn->query($count_sql);
$total_records = $count_result->fetch_assoc()['total'];
$total_pages = ceil($total_records / $per_page);

// Fetch all products
$products = [];
while ($row = $result->fetch_assoc()) {
    // Convert numeric values
    $row['price'] = floatval($row['price']);
    if ($row['sale_price'] !== null) {
        $row['sale_price'] = floatval($row['sale_price']);
    }
    $row['is_sale'] = intval($row['is_sale']);
    
    $products[] = $row;
}

// Prepare response
$response = [
    'data' => $products,
    'pagination' => [
        'page' => $page,
        'per_page' => $per_page,
        'total_pages' => $total_pages,
        'total_records' => $total_records
    ]
];

// Close connection
$conn->close();

// Output JSON response
echo json_encode($response, JSON_PRETTY_PRINT);
?>