import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    location: 'New York',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=200&auto=format&fit=crop ',
    productImage: 'https://images.unsplash.com/photo-1562572159-4efc207f5aff?q=80&w=300&auto=format&fit=crop ',
    text: 'The oversized fit is absolutely perfect. Not too baggy, just right for the street style look I was going for. The quality is insane for the price!',
    rating: 5,
  },
  {
    id: 2,
    name: '<PERSON>',
    location: 'Los Angeles',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?q=80&w=200&auto=format&fit=crop ',
    productImage: 'https://images.unsplash.com/photo-1576566588028-4147f3842f27?q=80&w=300&auto=format&fit=crop ',
    text: "I've bought from many streetwear brands, but StreetNOIR takes the cake. The graphics are unique and the fabric quality is outstanding. Definitely coming back for more.",
    rating: 5,
  },
  {
    id: 3,
    name: '<PERSON>',
    location: 'Chicago',
    image: 'https://images.unsplash.com/photo-1539571696357-5a69c17a67c6?q=80&w=200&auto=format&fit=crop ',
    productImage: 'https://images.unsplash.com/photo-1503341504253-dff4815485f1?q=80&w=300&auto=format&fit=crop ',
    text: "These tees have become my go-to for everyday wear. The attention to detail is amazing and they wash really well without losing shape. Worth every penny!",
    rating: 4,
  },
];

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const testimonialsRef = useRef(null);

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
  };

  useEffect(() => {
    const interval = setInterval(() => {
      nextTestimonial();
    }, 8000);

    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-16 bg-[#0f172a]" ref={testimonialsRef}>
      <div className="container mx-auto px-4 md:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-3">CUSTOMER LOVE</h2>
          <p className="text-[#94a3b8] max-w-2xl mx-auto">What our community is saying about our products</p>
        </motion.div>

        <div className="relative">
          <div className="overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-8 items-center">
                    <div className="md:col-span-2">
                      <div className="relative rounded-lg overflow-hidden h-80 bg-[#1e293b]">
                        <img
                          src={testimonial.productImage}
                          alt="Customer wearing product"
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>

                    <div className="md:col-span-3 bg-[#1e293b] p-8 rounded-lg">
                      <div className="flex mb-6">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={20}
                            className={
                              i < testimonial.rating
                                ? 'fill-[#f59e0b] text-[#f59e0b]'
                                : 'fill-[#475569] text-[#475569]'
                            }
                          />
                        ))}
                      </div>

                      <p className="text-[#e2e8f0] text-lg mb-6 italic">"{testimonial.text}"</p>

                      <div className="flex items-center">
                        <img
                          src={testimonial.image}
                          alt={testimonial.name}
                          className="w-12 h-12 rounded-full object-cover mr-4"
                        />
                        <div>
                          <h4 className="text-white font-medium">{testimonial.name}</h4>
                          <p className="text-[#94a3b8]">{testimonial.location}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-center mt-8 gap-4">
            <button
              onClick={prevTestimonial}
              className="w-10 h-10 bg-[#1e293b] hover:bg-[#475569] rounded-full flex items-center justify-center text-white transition-colors"
            >
              <ChevronLeft size={20} />
            </button>

            <div className="flex gap-2 items-center">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all ${
                    currentIndex === index ? 'bg-indigo-500 w-6' : 'bg-gray-700'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>

            <button
              onClick={nextTestimonial}
              className="w-10 h-10 bg-[#1e293b] hover:bg-[#475569] rounded-full flex items-center justify-center text-white transition-colors"
            >
              <ChevronRight size={20} />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}