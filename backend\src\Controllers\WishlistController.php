<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Wishlist;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Logger;
use Wolffoxx\Utils\Validator;
use Wolffoxx\Config\Database;

/**
 * Wishlist Controller
 * 
 * Handles wishlist operations including adding/removing items,
 * managing collections, and sharing functionality.
 */
class WishlistController
{
    private Wishlist $wishlistModel;
    private Product $productModel;
    private Logger $logger;

    public function __construct()
    {
        $this->wishlistModel = new Wishlist();
        $this->productModel = new Product();
        $this->logger = new Logger('wishlist');
    }

    /**
     * Get user's wishlist
     */
    public function index(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Get wishlist items with product details (avoid duplicates)
            $sql = "SELECT DISTINCT w.id, w.user_id, w.product_id, w.notes, w.priority, w.is_public,
                           w.created_at, w.updated_at,
                           p.name, p.price, p.sale_price, p.sku, p.material, p.fit,
                           p.is_new, p.is_bestseller, p.is_trending, p.is_sale,
                           p.average_rating, p.stock_quantity,
                           (SELECT pi.image_url FROM product_images pi
                            WHERE pi.product_id = p.id AND pi.is_primary = 1
                            LIMIT 1) as primary_image
                    FROM wishlists w
                    LEFT JOIN products p ON w.product_id = p.id
                    WHERE w.user_id = ?
                    ORDER BY w.created_at DESC";

            $stmt = \Wolffoxx\Config\Database::execute($sql, [$userId]);
            $items = $stmt->fetchAll();

            // Calculate stats
            $priorityBreakdown = ['high' => 0, 'medium' => 0, 'low' => 0];
            foreach ($items as $item) {
                $priority = $item['priority'] ?? 'medium';
                if (isset($priorityBreakdown[$priority])) {
                    $priorityBreakdown[$priority]++;
                }
            }

            Response::success([
                'items' => $items,
                'total_count' => count($items),
                'stats' => [
                    'total_items' => count($items),
                    'priority_breakdown' => $priorityBreakdown,
                    'average_price' => 0,
                    'total_value' => 0,
                    'categories_count' => 0
                ],
                'filters' => []
            ]);

        } catch (\Exception $e) {
            error_log('Get wishlist failed: ' . $e->getMessage());
            Response::error('Failed to retrieve wishlist');
        }
    }

    /**
     * Add item to wishlist
     */
    public function addItem(array $params = []): void
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Check if item already exists
            $checkSql = "SELECT id FROM wishlists WHERE user_id = ? AND product_id = ? LIMIT 1";
            $checkStmt = \Wolffoxx\Config\Database::execute($checkSql, [$userId, $input['product_id'] ?? 1]);
            $existing = $checkStmt->fetch();

            if ($existing) {
                Response::error('Item already in wishlist', 409);
                return;
            }

            // Insert wishlist item
            $itemSql = "INSERT INTO wishlists (
                user_id, product_id, notes, priority, is_public, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, NOW(), NOW())";

            $itemParams = [
                $userId,
                $input['product_id'] ?? 1,
                $input['notes'] ?? null,
                $input['priority'] ?? 'medium',
                $input['is_public'] ?? false
            ];

            \Wolffoxx\Config\Database::execute($itemSql, $itemParams);
            $itemId = \Wolffoxx\Config\Database::getConnection()->lastInsertId();

            Response::success([
                'message' => 'Item added to wishlist successfully',
                'item' => [
                    'id' => $itemId,
                    'user_id' => $userId,
                    'product_id' => $input['product_id'] ?? 1,
                    'product_name' => $input['product_name'] ?? 'Test Product',
                    'priority' => $input['priority'] ?? 'medium',
                    'notes' => $input['notes'] ?? null,
                    'is_public' => $input['is_public'] ?? false,
                    'added_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Add to wishlist failed: ' . $e->getMessage());
            Response::error('Failed to add item to wishlist: ' . $e->getMessage());
        }
    }

    /**
     * Remove item from wishlist
     */
    public function removeItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $productId = $params['id'] ?? '';

            if (empty($productId)) {
                Response::error('Product ID is required', 400);
                return;
            }

            // Remove from wishlist
            $removed = $this->wishlistModel->removeItem($userId, $productId);

            if (!$removed) {
                Response::notFound('Item not found in wishlist');
                return;
            }

            $this->logger->info('Item removed from wishlist', [
                'user_id' => $userId,
                'product_id' => $productId
            ]);

            Response::success([
                'message' => 'Item removed from wishlist successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Remove from wishlist failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'product_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to remove item from wishlist');
        }
    }

    /**
     * Update wishlist item
     */
    public function updateItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $productId = $params['id'] ?? '';

            if (empty($productId)) {
                Response::error('Product ID is required', 400);
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'notes' => 'nullable|string|max:500',
                'priority' => 'nullable|in:low,medium,high',
                'is_public' => 'nullable|boolean'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Update wishlist item
            $updatedItem = $this->wishlistModel->updateItem($userId, $productId, $data);

            if (!$updatedItem) {
                Response::notFound('Wishlist item not found');
                return;
            }

            $this->logger->info('Wishlist item updated', [
                'user_id' => $userId,
                'product_id' => $productId,
                'updated_fields' => array_keys($data)
            ]);

            Response::success([
                'item' => $updatedItem,
                'message' => 'Wishlist item updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Update wishlist item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'product_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to update wishlist item');
        }
    }

    /**
     * Check if item is in wishlist
     */
    public function checkItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $productId = $params['id'] ?? '';

            if (empty($productId)) {
                Response::error('Product ID is required', 400);
                return;
            }

            $inWishlist = $this->wishlistModel->isInWishlist($userId, $productId);

            Response::success([
                'in_wishlist' => $inWishlist,
                'product_id' => $productId
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Check wishlist item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'product_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to check wishlist item');
        }
    }

    /**
     * Get wishlist statistics
     */
    public function getStats(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $stats = $this->wishlistModel->getUserWishlistStats($userId);

            Response::success($stats);

        } catch (\Exception $e) {
            $this->logger->error('Get wishlist stats failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to get wishlist statistics');
        }
    }

    /**
     * Clear entire wishlist
     */
    public function clear(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Get all wishlist items for this user
            $items = $this->wishlistModel->getUserWishlist($userId);

            // Remove all items
            $sql = "DELETE FROM wishlists WHERE user_id = ?";
            Database::execute($sql, [$userId]);

            $this->logger->info('Wishlist cleared', [
                'user_id' => $userId,
                'items_removed' => count($items)
            ]);

            Response::success([
                'message' => 'Wishlist cleared successfully',
                'items_removed' => count($items)
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Clear wishlist failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to clear wishlist');
        }
    }

    /**
     * Get JSON input from request body
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Response::error('Invalid JSON input', 400);
            exit;
        }

        return $data ?? [];
    }
}
