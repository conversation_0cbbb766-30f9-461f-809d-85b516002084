import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Pa<PERSON>, <PERSON><PERSON><PERSON>, ArrowRight, Shirt } from 'lucide-react';
import { Link } from 'react-router-dom';
import { dataService } from '../services/dataService';

const SmartColorMatching = ({ currentProduct, selectedColor, onAddToOutfit, onMatchingItemsUpdate, isInModal = false }) => {
  const [matchingItems, setMatchingItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [allProducts, setAllProducts] = useState([]);

  // Fetch products from database
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await dataService.getProducts({}, 1, 100); // Get first 100 products
        setAllProducts(response.products || []);
      } catch (error) {
        console.error('Failed to fetch products for smart matching:', error);
        setAllProducts([]);
      }
    };

    fetchProducts();
  }, []);

  // Enhanced color analysis with better color family detection
  const analyzeColor = (colorValue) => {
    // Convert hex to RGB for better analysis
    const hex = colorValue.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Calculate brightness (0-255)
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    // Enhanced neutral detection
    const isBlack = brightness < 50 && Math.abs(r - g) < 20 && Math.abs(g - b) < 20 && Math.abs(r - b) < 20;
    const isWhite = brightness > 220 && Math.abs(r - g) < 30 && Math.abs(g - b) < 30 && Math.abs(r - b) < 30;
    const isGray = Math.abs(r - g) < 30 && Math.abs(g - b) < 30 && Math.abs(r - b) < 30;
    const isNeutral = isBlack || isWhite || isGray;
    const isDark = brightness < 128;
    const isLight = brightness > 200;

    // Enhanced color family detection with better thresholds
    let colorFamily = 'other';
    if (isBlack) {
      colorFamily = 'black';
    } else if (isWhite) {
      colorFamily = 'white';
    } else if (isGray) {
      colorFamily = isDark ? 'dark-neutral' : isLight ? 'light-neutral' : 'mid-neutral';
    } else {
      // Improved color family detection - focus on dominant color channel
      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      const diff = max - min;

      // Lower threshold for better color family detection
      if (diff > 20) {
        // Blue family detection - prioritize blue channel
        if (b === max && b > 80) { // Any blue with significant blue channel
          colorFamily = 'blue-family';
        }
        // Red family detection
        else if (r === max && r > g + 20 && r > b + 20) {
          colorFamily = 'red-family';
        }
        // Green family detection
        else if (g === max && g > r + 20 && g > b + 20) {
          colorFamily = 'green-family';
        }
        // Yellow family (high red and green, low blue)
        else if (r > 150 && g > 150 && b < 100) {
          colorFamily = 'yellow-family';
        }
        // Purple family (high red and blue, lower green)
        else if (r > 100 && b > 100 && g < Math.max(r, b) - 30) {
          colorFamily = 'purple-family';
        }
        // Pink family (high red, moderate green and blue)
        else if (r > 150 && g < r - 30 && b > 80 && b < r) {
          colorFamily = 'pink-family';
        }
      }
    }

    // Debug logging for color analysis (only for problematic cases)
    if (colorFamily === 'blue-family') {
      console.log(`🔵 Blue detected: ${colorValue} -> ${colorFamily}`, { r, g, b });
    }

    return { brightness, isDark, isLight, isNeutral, isBlack, isWhite, colorFamily, r, g, b };
  };

  // Enhanced garment categorization for better outfit coordination
  const categorizeGarment = (category) => {
    const upperWear = [
      'T-Shirts', 'Shirts', 'Oversized Tees', 'Oversized T-Shirt', 'Oversized Shirt',
      'Full Sleeves Shirt', 'Regular Fit T-Shirt', 'Hoodies', 'Sweatshirt', 'Jackets', 'Shacket'
    ];

    const lowerWear = [
      'Baggy Jeans', 'Fit Jeans', 'Capri', 'Cargo Pants', 'Shorts', 'Trousers'
    ];

    const accessories = [
      'Shoes', 'Bags', 'Watches', 'Belts', 'Hats', 'Sunglasses'
    ];

    let garmentType = 'other';
    if (upperWear.includes(category)) garmentType = 'upper';
    else if (lowerWear.includes(category)) garmentType = 'lower';
    else if (accessories.includes(category)) garmentType = 'accessory';

    // Debug only for T-shirts and jeans
    if (category.includes('T-Shirt') || category.includes('Jeans')) {
      console.log(`👕 Garment Classification: "${category}" -> ${garmentType}`);
    }
    return garmentType;
  };

  // Enhanced color clash prevention logic with debugging
  const shouldAvoidColorCombination = (baseColorFamily, targetColorFamily, baseGarmentType, targetGarmentType) => {
    // Allow black and white with everything (universal neutrals)
    if (baseColorFamily === 'black' || baseColorFamily === 'white' ||
        targetColorFamily === 'black' || targetColorFamily === 'white') {
      return false;
    }

    // Allow neutral grays with everything
    if (baseColorFamily.includes('neutral') || targetColorFamily.includes('neutral')) {
      return false;
    }

    // Prevent same color family combinations between upper and lower wear
    if (baseGarmentType !== targetGarmentType &&
        (baseGarmentType === 'upper' && targetGarmentType === 'lower' ||
         baseGarmentType === 'lower' && targetGarmentType === 'upper')) {

      // Avoid same color families for upper/lower combinations
      if (baseColorFamily === targetColorFamily &&
          baseColorFamily !== 'other' &&
          !baseColorFamily.includes('neutral') &&
          baseColorFamily !== 'black' &&
          baseColorFamily !== 'white') {
        console.log(`❌ BLOCKING same color family combination: ${baseColorFamily} ${baseGarmentType} + ${targetColorFamily} ${targetGarmentType}`);
        return true;
      }
    }

    return false;
  };

  // Smart color matching based on enhanced fashion rules
  const getSmartColorMatches = (baseColor, baseGarmentType) => {
    const analysis = analyzeColor(baseColor);

    // Enhanced fashion-based color matching rules
    const matchingRules = {
      'black': {
        perfect: ['#ffffff', '#f8fafc', '#e2e8f0'], // whites and light grays
        great: ['#3b82f6', '#ef4444', '#22c55e', '#fbbf24', '#a855f7', '#06b6d4'], // all bright colors
        good: ['#6b7280', '#9ca3af'] // medium grays
      },
      'white': {
        perfect: ['#000000', '#1f2937', '#374151'], // blacks and dark grays
        great: ['#1e40af', '#dc2626', '#059669', '#d97706', '#7c3aed'], // all deep colors
        good: ['#4b5563', '#6b7280'] // medium grays
      },
      'dark-neutral': {
        perfect: ['#ffffff', '#f8fafc', '#e2e8f0'], // whites
        great: ['#3b82f6', '#ef4444', '#22c55e'], // bright colors
        good: ['#fbbf24', '#a855f7', '#06b6d4'] // accent colors
      },
      'light-neutral': {
        perfect: ['#000000', '#1f2937', '#374151'], // darks
        great: ['#1e40af', '#dc2626', '#059669'], // deep colors
        good: ['#7c3aed', '#ea580c', '#0891b2'] // rich colors
      },
      'mid-neutral': {
        perfect: ['#ffffff', '#000000'], // high contrast
        great: ['#3b82f6', '#ef4444', '#22c55e'], // vibrant
        good: ['#1f2937', '#f8fafc'] // subtle contrast
      },
      'blue-family': {
        perfect: ['#ffffff', '#fbbf24', '#f59e0b'], // white, yellow, orange (complementary)
        great: ['#000000', '#1f2937', '#6b7280'], // neutrals
        good: ['#22c55e'] // green (adjacent)
      },
      'red-family': {
        perfect: ['#ffffff', '#000000'], // neutrals
        great: ['#22c55e', '#6b7280'], // green (complementary) and grays
        good: ['#fbbf24', '#1f2937'] // yellow and dark gray
      },
      'green-family': {
        perfect: ['#ffffff', '#000000'], // neutrals
        great: ['#ef4444', '#6b7280'], // red (complementary) and grays
        good: ['#fbbf24', '#3b82f6'] // yellow and blue
      },
      'yellow-family': {
        perfect: ['#ffffff', '#000000'], // neutrals
        great: ['#3b82f6', '#a855f7', '#6b7280'], // blue, purple (complementary) and grays
        good: ['#22c55e', '#ef4444'] // adjacent colors
      },
      'purple-family': {
        perfect: ['#ffffff', '#000000'], // neutrals
        great: ['#fbbf24', '#22c55e', '#6b7280'], // yellow (complementary), green and grays
        good: ['#3b82f6', '#ef4444'] // adjacent colors
      },
      'pink-family': {
        perfect: ['#ffffff', '#000000'], // neutrals
        great: ['#22c55e', '#6b7280'], // green and grays
        good: ['#3b82f6', '#fbbf24'] // blue and yellow
      },
      'other': {
        perfect: ['#ffffff', '#000000'], // safe neutrals
        great: ['#6b7280', '#1f2937'], // grays
        good: ['#3b82f6', '#ef4444'] // basic colors
      }
    };

    const rules = matchingRules[analysis.colorFamily] || matchingRules.other;
    return {
      perfect: rules.perfect,
      great: rules.great,
      good: rules.good,
      analysis
    };
  };

  // Advanced category matching with priority and style context
  const getCategoryComplements = (currentCategory) => {
    const categoryMap = {
      // TOPS - need bottoms and outerwear
      'T-Shirts': {
        essential: ['Baggy Jeans', 'Fit Jeans'], // must-have combinations
        great: ['Capri', 'Jackets'], // great additions
        layering: ['Hoodies', 'Sweatshirt'], // for layering
        style: 'casual'
      },
      'Shirts': {
        essential: ['Fit Jeans', 'Baggy Jeans'],
        great: ['Jackets', 'Capri'],
        layering: ['T-Shirts'], // shirts over tees
        style: 'smart-casual'
      },
      'Oversized Tees': {
        essential: ['Fit Jeans'], // balance oversized with fitted
        great: ['Baggy Jeans', 'Capri'],
        layering: ['Jackets'],
        style: 'streetwear'
      },
      'Oversized T-Shirt': {
        essential: ['Fit Jeans'],
        great: ['Baggy Jeans', 'Capri'],
        layering: ['Jackets'],
        style: 'relaxed'
      },
      'Oversized Shirt': {
        essential: ['Fit Jeans'],
        great: ['T-Shirts', 'Capri'], // can layer over tees
        layering: ['Jackets'],
        style: 'trendy'
      },
      'Full Sleeves Shirt': {
        essential: ['Fit Jeans', 'Baggy Jeans'],
        great: ['Jackets', 'Capri'],
        layering: ['T-Shirts'],
        style: 'classic'
      },
      'Regular Fit T-Shirt': {
        essential: ['Baggy Jeans', 'Fit Jeans'],
        great: ['Capri', 'Jackets'],
        layering: ['Hoodies', 'Sweatshirt'],
        style: 'everyday'
      },

      // BOTTOMS - need tops and outerwear
      'Baggy Jeans': {
        essential: ['T-Shirts', 'Oversized Tees'],
        great: ['Shirts', 'Hoodies'],
        layering: ['Jackets', 'Sweatshirt'],
        style: 'streetwear'
      },
      'Fit Jeans': {
        essential: ['T-Shirts', 'Shirts'],
        great: ['Oversized Tees', 'Hoodies'],
        layering: ['Jackets', 'Sweatshirt'],
        style: 'versatile'
      },
      'Capri': {
        essential: ['T-Shirts', 'Regular Fit T-Shirt'],
        great: ['Shirts', 'Oversized Tees'],
        layering: [], // usually no layering with capri
        style: 'casual'
      },

      // OUTERWEAR - complements everything
      'Hoodies': {
        essential: ['Baggy Jeans', 'Fit Jeans'],
        great: ['T-Shirts', 'Capri'],
        layering: [], // hoodies are outer layer
        style: 'casual'
      },
      'Sweatshirt': {
        essential: ['Baggy Jeans', 'Fit Jeans'],
        great: ['T-Shirts', 'Capri'],
        layering: ['Jackets'], // can layer jacket over
        style: 'comfort'
      },
      'Jackets': {
        essential: ['T-Shirts', 'Shirts'],
        great: ['Baggy Jeans', 'Fit Jeans'],
        layering: [], // jackets are outer layer
        style: 'elevated'
      },
      'Shacket': {
        essential: ['T-Shirts'],
        great: ['Baggy Jeans', 'Fit Jeans', 'Capri'],
        layering: [],
        style: 'trendy'
      }
    };

    return categoryMap[currentCategory] || {
      essential: ['T-Shirts', 'Baggy Jeans'],
      great: ['Hoodies', 'Jackets'],
      layering: [],
      style: 'basic'
    };
  };

  useEffect(() => {
    if (!currentProduct) return;

    setIsLoading(true);

    // Test color analysis with known problematic colors
    console.log('🧪 Testing color analysis:');
    const testColors = [
      { name: 'Navy Blue', value: '#1e3a8a' },
      { name: 'Light Blue', value: '#87ceeb' },
      { name: 'Light Wash Denim', value: '#6b9bd1' },
      { name: 'Dark Blue', value: '#1e3a8a' }
    ];

    testColors.forEach(color => {
      const analysis = analyzeColor(color.value);
      console.log(`${color.name} (${color.value}) -> ${analysis.colorFamily}`);
    });

    // Get the selected color or fallback to first color
    const activeColor = selectedColor || currentProduct.colors?.[0];
    const primaryColorValue = activeColor?.value || '#000000';
    const primaryColorName = activeColor?.name || 'your selection';

    // Enhanced color analysis with garment type
    const baseGarmentType = categorizeGarment(currentProduct.category);
    const baseColorAnalysis = analyzeColor(primaryColorValue);
    const colorMatches = getSmartColorMatches(primaryColorValue, baseGarmentType);
    const currentCategory = currentProduct.category;
    const categoryComplements = getCategoryComplements(currentCategory);

    const matches = [];

    // Enhanced helper function to check color similarity with clash prevention
    const isColorMatch = (productColor, targetColors, matchType, targetGarmentType) => {
      const targetColorAnalysis = analyzeColor(productColor);

      // Check for color clashes first
      if (shouldAvoidColorCombination(baseColorAnalysis.colorFamily, targetColorAnalysis.colorFamily, baseGarmentType, targetGarmentType)) {
        return false;
      }

      return targetColors.some(targetColor => {
        // Exact match
        if (productColor.toLowerCase() === targetColor.toLowerCase()) return true;

        // Similar color analysis for better matching
        const productAnalysis = analyzeColor(productColor);
        const targetAnalysis = analyzeColor(targetColor);

        // For perfect matches, be more strict
        if (matchType === 'perfect') {
          return Math.abs(productAnalysis.brightness - targetAnalysis.brightness) < 50;
        }

        // For great/good matches, be more flexible
        return Math.abs(productAnalysis.brightness - targetAnalysis.brightness) < 100;
      });
    };

    // Priority 1: Essential category matches with perfect colors
    categoryComplements.essential.forEach(category => {
      const categoryProducts = allProducts.filter(p => p.category === category);

      categoryProducts.forEach(product => {
        if (product.id !== currentProduct.id) {
          const targetGarmentType = categorizeGarment(product.category);
          const perfectColorMatch = product.colors?.find(color =>
            isColorMatch(color.value, colorMatches.perfect, 'perfect', targetGarmentType)
          );

          if (perfectColorMatch) {
            matches.push({
              ...product,
              matchingColor: perfectColorMatch,
              matchReason: `Perfect with ${primaryColorName}`,
              categoryReason: getAdvancedCategoryReason(currentCategory, category, 'essential'),
              matchScore: 95 + Math.floor(Math.random() * 5), // 95-99%
              priority: 1
            });
          }
        }
      });
    });

    // Priority 2: Essential categories with great colors
    if (matches.length < 4) {
      categoryComplements.essential.forEach(category => {
        const categoryProducts = allProducts.filter(p => p.category === category);

        categoryProducts.forEach(product => {
          if (product.id !== currentProduct.id && !matches.find(m => m.id === product.id)) {
            const targetGarmentType = categorizeGarment(product.category);
            const greatColorMatch = product.colors?.find(color =>
              isColorMatch(color.value, colorMatches.great, 'great', targetGarmentType)
            );

            if (greatColorMatch) {
              matches.push({
                ...product,
                matchingColor: greatColorMatch,
                matchReason: `Great with ${primaryColorName}`,
                categoryReason: getAdvancedCategoryReason(currentCategory, category, 'essential'),
                matchScore: 85 + Math.floor(Math.random() * 10), // 85-94%
                priority: 2
              });
            }
          }
        });
      });
    }

    // Priority 3: Great category matches with perfect colors
    if (matches.length < 5) {
      categoryComplements.great.forEach(category => {
        const categoryProducts = allProducts.filter(p => p.category === category);

        categoryProducts.forEach(product => {
          if (product.id !== currentProduct.id && !matches.find(m => m.id === product.id)) {
            const targetGarmentType = categorizeGarment(product.category);
            const perfectColorMatch = product.colors?.find(color =>
              isColorMatch(color.value, colorMatches.perfect, 'perfect', targetGarmentType)
            );

            if (perfectColorMatch) {
              matches.push({
                ...product,
                matchingColor: perfectColorMatch,
                matchReason: `Perfect with ${primaryColorName}`,
                categoryReason: getAdvancedCategoryReason(currentCategory, category, 'great'),
                matchScore: 80 + Math.floor(Math.random() * 10), // 80-89%
                priority: 3
              });
            }
          }
        });
      });
    }

    // Priority 4: Fill remaining slots with good matches
    if (matches.length < 6) {
      const allCategories = [...categoryComplements.essential, ...categoryComplements.great, ...categoryComplements.layering];

      allCategories.forEach(category => {
        if (matches.length >= 6) return;

        const categoryProducts = allProducts.filter(p => p.category === category);
        categoryProducts.forEach(product => {
          if (product.id !== currentProduct.id && !matches.find(m => m.id === product.id) && matches.length < 6) {
            const targetGarmentType = categorizeGarment(product.category);
            const goodColorMatch = product.colors?.find(color =>
              isColorMatch(color.value, colorMatches.good, 'good', targetGarmentType)
            );

            if (goodColorMatch) {
              matches.push({
                ...product,
                matchingColor: goodColorMatch,
                matchReason: `Works with ${primaryColorName}`,
                categoryReason: getAdvancedCategoryReason(currentCategory, category, 'good'),
                matchScore: 70 + Math.floor(Math.random() * 15), // 70-84%
                priority: 4
              });
            }
          }
        });
      });
    }

    // Sort by priority first, then by match score
    const topMatches = matches
      .sort((a, b) => {
        if (a.priority !== b.priority) return a.priority - b.priority;
        return b.matchScore - a.matchScore;
      })
      .slice(0, 6);

    setMatchingItems(topMatches);

    // Update parent component with matching items
    if (onMatchingItemsUpdate) {
      onMatchingItemsUpdate(topMatches);
    }

    setIsLoading(false);
  }, [currentProduct, selectedColor, allProducts]); // Added allProducts dependency

  const getAdvancedCategoryReason = (currentCat, matchCat, priority) => {
    const styleReasons = {
      essential: {
        'T-Shirts': {
          'Baggy Jeans': 'Essential casual combo',
          'Fit Jeans': 'Perfect everyday pairing',
          'Capri': 'Summer essential',
          'Jackets': 'Layering essential'
        },
        'Shirts': {
          'Fit Jeans': 'Smart-casual essential',
          'Baggy Jeans': 'Relaxed professional',
          'Jackets': 'Elevated layering',
          'Capri': 'Polished casual'
        },
        'Baggy Jeans': {
          'T-Shirts': 'Streetwear essential',
          'Oversized Tees': 'Perfect proportions',
          'Shirts': 'Elevated street style',
          'Hoodies': 'Urban comfort'
        },
        'Fit Jeans': {
          'T-Shirts': 'Versatile essential',
          'Shirts': 'Classic combination',
          'Oversized Tees': 'Balanced silhouette',
          'Hoodies': 'Modern casual'
        }
      },
      great: {
        'T-Shirts': {
          'Hoodies': 'Great layering option',
          'Sweatshirt': 'Comfortable layers',
          'Shacket': 'Trendy combination'
        },
        'Shirts': {
          'T-Shirts': 'Smart layering',
          'Hoodies': 'Unexpected cool',
          'Sweatshirt': 'Casual contrast'
        },
        'Baggy Jeans': {
          'Jackets': 'Street style edge',
          'Sweatshirt': 'Cozy streetwear'
        },
        'Fit Jeans': {
          'Jackets': 'Polished look',
          'Sweatshirt': 'Relaxed style'
        }
      },
      good: {
        default: 'Stylish combination'
      }
    };

    return styleReasons[priority]?.[currentCat]?.[matchCat] ||
           styleReasons.good.default;
  };

  if (!currentProduct || matchingItems.length === 0) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={isInModal ? "" : "bg-slate-900/50 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 mt-8"}
    >
      {/* Header - Only show if not in modal */}
      {!isInModal && (
        <div className="flex flex-col sm:flex-row sm:items-center gap-3 mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg" style={{
              background: 'linear-gradient(237deg, #214FC3 0%, #54AEE1 100%)'
            }}>
              <Palette size={20} className="text-white" />
            </div>
            <div>
              <h3 className="text-lg sm:text-xl font-bold text-white flex items-center gap-2">
                Smart Style Matching
                <Sparkles size={16} className="text-cyan-400" />
              </h3>
              <div className="flex items-center gap-2">
                <p className="text-slate-400 text-xs sm:text-sm">
                  Perfect combinations for
                </p>
                {(selectedColor || currentProduct.colors?.[0]) && (
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3 h-3 rounded-full border border-white/30"
                      style={{ backgroundColor: (selectedColor || currentProduct.colors?.[0])?.hex || (selectedColor || currentProduct.colors?.[0])?.value }}
                      title={`Base color: ${(selectedColor || currentProduct.colors?.[0])?.name}`}
                    />
                    <span className="text-blue-400 text-xs sm:text-sm font-medium">
                      {(selectedColor || currentProduct.colors?.[0])?.name}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 text-xs text-slate-500">
            <Shirt size={14} />
            <span>Smart category pairing</span>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading ? (
        <div className={`grid gap-3 ${isInModal ? 'grid-cols-2' : 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 sm:gap-4'}`}>
          {[...Array(isInModal ? 4 : 6)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-[#2a2a2a] rounded-xl aspect-square mb-3"></div>
              <div className="bg-[#2a2a2a] rounded h-3 mb-2"></div>
              <div className="bg-[#2a2a2a] rounded h-2 w-2/3 mb-1"></div>
              <div className="bg-[#2a2a2a] rounded h-2 w-1/2"></div>
            </div>
          ))}
        </div>
      ) : (
        /* Matching Items Grid */
        <div className={`grid gap-3 ${isInModal ? 'grid-cols-2' : 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 sm:gap-4'}`}>
          {matchingItems.slice(0, isInModal ? 4 : 6).map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.1 }}
              className="group relative bg-[#1a1a1a] rounded-xl p-3 sm:p-4 border border-[#404040] hover:border-[#FF6B35]/50 transition-all duration-300"
            >
              {/* Match Score Badge */}
              <div className="absolute top-2 right-2 z-10">
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  {item.matchScore}%
                </div>
              </div>

              {/* Product Image */}
              <Link to={`/product/${item.id}`}>
                <div className="relative overflow-hidden rounded-lg mb-3 aspect-square">
                  <img
                    src={item.images[0]}
                    alt={item.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Matching Color Indicator */}
                  {item.matchingColor && (
                    <div className="absolute bottom-2 left-2 flex items-center gap-1 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1">
                      <div
                        className="w-3 h-3 rounded-full border border-white/50"
                        style={{ backgroundColor: item.matchingColor.value }}
                        title={`Matches with ${item.matchingColor.name}`}
                      />
                      <span className="text-white text-xs">Match</span>
                    </div>
                  )}
                </div>
              </Link>

              {/* Product Info */}
              <div className="space-y-2">
                <h4 className="text-white font-medium text-xs sm:text-sm line-clamp-2 group-hover:text-[#FF6B35] transition-colors">
                  {item.name}
                </h4>
                <div className="space-y-1">
                  <p className="text-[#FF6B35] text-xs font-medium">
                    {item.matchReason}
                  </p>
                  <p className="text-[#AAAAAA] text-xs">
                    {item.categoryReason}
                  </p>

                  {/* Color Indicator */}
                  {item.matchingColor && (
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full border border-white/30"
                        style={{ backgroundColor: item.matchingColor.hex || item.matchingColor.value }}
                        title={`Matching color: ${item.matchingColor.name}`}
                      />
                      <span className="text-[#AAAAAA] text-xs">
                        {item.matchingColor.name}
                      </span>
                    </div>
                  )}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white font-bold text-sm">
                    ${item.price}
                  </span>
                  <button
                    onClick={() => onAddToOutfit && onAddToOutfit(item)}
                    className="text-white text-xs px-2 sm:px-3 py-1.5 rounded-lg hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300 flex items-center gap-1"
                    style={{
                      background: 'linear-gradient(135deg, #FF6B35 0%, #F7931E 100%)'
                    }}
                  >
                    <span className="hidden sm:inline">Build Outfit</span>
                    <span className="sm:hidden">Build</span>
                    <ArrowRight size={12} />
                  </button>
                </div>
              </div>

              {/* Color Swatches */}
              <div className="flex gap-1 mt-2">
                {item.colors?.slice(0, 3).map((color, colorIndex) => (
                  <div
                    key={colorIndex}
                    className={`w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 ${
                      color.value === item.matchingColor?.value
                        ? 'border-[#FF6B35]'
                        : 'border-[#404040]'
                    }`}
                    style={{ backgroundColor: color.value }}
                    title={color.name}
                  />
                ))}
                {item.colors?.length > 3 && (
                  <div className="w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 border-[#404040] bg-[#2a2a2a] flex items-center justify-center">
                    <span className="text-[#AAAAAA] text-xs">+{item.colors.length - 3}</span>
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {/* View All Button - Only show if not in modal */}
      {!isInModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-6"
        >
          <Link
            to="/collections"
            className="inline-flex items-center gap-2 text-cyan-400 hover:text-cyan-300 font-medium transition-colors"
          >
            View All Collections
            <ArrowRight size={16} />
          </Link>
        </motion.div>
      )}
    </motion.div>
  );
};

export default SmartColorMatching;
