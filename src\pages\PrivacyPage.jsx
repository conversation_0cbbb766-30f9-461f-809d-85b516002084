import { motion, useScroll, useTransform } from 'framer-motion';
import { useRef } from 'react';
import { ArrowLeft, Shield, Eye, Cookie, Database, Lock, UserCheck, Globe } from 'lucide-react';
import { Link } from 'react-router-dom';

export default function PrivacyPage() {
  const containerRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [0, -50]);

  const sections = [
    {
      id: 'collection',
      title: 'Information We Collect',
      icon: Database,
      content: `We collect information you provide directly to us, such as when you create an account, make a purchase, or contact us. This includes your name, email address, shipping address, payment information, and communication preferences. We also automatically collect certain information about your device and usage of our website through cookies and similar technologies.`
    },
    {
      id: 'usage',
      title: 'How We Use Your Information',
      icon: Eye,
      content: `We use your information to process orders, provide customer service, send marketing communications (with your consent), improve our website and services, prevent fraud, and comply with legal obligations. We may also use aggregated, non-personal information for analytics and business intelligence purposes.`
    },
    {
      id: 'sharing',
      title: 'Information Sharing',
      icon: UserCheck,
      content: `We do not sell, trade, or rent your personal information to third parties. We may share your information with trusted service providers who assist us in operating our website and conducting business, such as payment processors, shipping companies, and email service providers. These parties are bound by confidentiality agreements.`
    },
    {
      id: 'cookies',
      title: 'Cookies & Tracking',
      icon: Cookie,
      content: `We use cookies and similar technologies to enhance your browsing experience, analyze website traffic, and personalize content. You can control cookie settings through your browser preferences. Some features of our website may not function properly if cookies are disabled.`
    },
    {
      id: 'security',
      title: 'Data Security',
      icon: Lock,
      content: `We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure, and we cannot guarantee absolute security.`
    },
    {
      id: 'rights',
      title: 'Your Rights',
      icon: Shield,
      content: `You have the right to access, update, or delete your personal information. You may also opt out of marketing communications at any time. If you are located in the EU, you have additional rights under GDPR, including the right to data portability and the right to object to processing.`
    },
    {
      id: 'international',
      title: 'International Transfers',
      icon: Globe,
      content: `Your information may be transferred to and processed in countries other than your own. We ensure that such transfers are conducted in accordance with applicable data protection laws and that appropriate safeguards are in place to protect your information.`
    }
  ];

  return (
    <div ref={containerRef} className="min-h-screen bg-black text-[#d4d4d4]">
      {/* Back to Home Button - Top Left - TRULY FIXED */}
      {/*<div*/}
      {/*  style={{*/}
      {/*    position: 'fixed',*/}
      {/*    top: '80px',*/}
      {/*    left: '16px',*/}
      {/*    zIndex: 99999,*/}
      {/*    transform: 'translate3d(0, 0, 0)'*/}
      {/*  }}*/}
      {/*>*/}
      {/*  <Link*/}
      {/*    to="/"*/}
      {/*    className="inline-flex items-center gap-2 text-gray-300 hover:text-white transition-colors duration-300 group"*/}
      {/*  >*/}
      {/*    <ArrowLeft size={18} className="group-hover:-translate-x-1 transition-transform" />*/}
      {/*    <span className="hidden sm:inline">Back to Home</span>*/}
      {/*  </Link>*/}
      {/*</div>*/}
        {/* Hero Section */}
        <motion.div
        className="relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800"
        style={{ y }}
      >
        <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg%20width%3D%2260%22%20height%3D%2260%22%20viewBox%3D%220%200%2060%2060%22%20xmlns%3D%22http%3A//www.w3.org/2000/svg%22%3E%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20fill%3D%22%23ffffff%22%20fill-opacity%3D%220.02%22%3E%3Ccircle%20cx%3D%2230%22%20cy%3D%2230%22%20r%3D%221%22/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-4xl mx-auto"
          >
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-6">
              PRIVACY POLICY
            </h1>
            <p className="text-lg sm:text-xl text-gray-300 leading-relaxed max-w-2xl mx-auto">
              Your privacy is important to us. This policy explains how we collect, use, and protect your information.
            </p>
            <div className="mt-8 text-sm text-gray-400">
              Last updated: {new Date().toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Content Sections */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-20">
        <div className="max-w-4xl mx-auto">
          {/* Introduction */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-12 p-6 sm:p-8 bg-[#1a1a1a] backdrop-blur-sm rounded-2xl border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <h2 className="text-2xl sm:text-3xl font-bold text-white mb-4">Our Commitment to Privacy</h2>
            <p className="text-gray-300 leading-relaxed mb-4">
              At WOLFFOXX, we are committed to protecting your privacy and ensuring the security of your personal information.
              This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website
              or make a purchase from us.
            </p>
            <p className="text-slate-300 leading-relaxed">
              By using our services, you consent to the data practices described in this policy. 
              If you do not agree with this policy, please do not use our website or services.
            </p>
          </motion.div>

          {/* Privacy Sections */}
          <div className="space-y-8">
            {sections.map((section, index) => (
              <motion.div
                key={section.id}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 * index }}
                className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 hover:border-orange-500/30 transition-colors shadow-xl"
                style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
              >
                <div className="flex items-start gap-4 mb-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-orange-500/10 rounded-lg flex items-center justify-center hover:bg-orange-500/20 transition-colors group" style={{ backgroundColor: 'rgba(249, 115, 22, 0.1) !important' }}>
                    <section.icon size={24} className="text-orange-400 group-hover:text-orange-300 transition-colors" />
                  </div>
                  <h3 className="text-xl sm:text-2xl font-semibold text-white">
                    {section.title}
                  </h3>
                </div>
                <p className="text-gray-300 leading-relaxed pl-16">
                  {section.content}
                </p>
              </motion.div>
            ))}
          </div>

          {/* Additional Privacy Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-16 space-y-8"
          >
            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Data Retention</h3>
              <p className="text-gray-300 leading-relaxed">
                We retain your personal information only for as long as necessary to fulfill the purposes outlined in this policy,
                unless a longer retention period is required or permitted by law. When we no longer need your information,
                we will securely delete or anonymize it.
              </p>
            </div>

            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Children's Privacy</h3>
              <p className="text-gray-300 leading-relaxed">
                Our services are not intended for children under 13 years of age. We do not knowingly collect personal information
                from children under 13. If you are a parent or guardian and believe your child has provided us with personal information,
                please contact us so we can delete such information.
              </p>
            </div>

            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Third-Party Links</h3>
              <p className="text-gray-300 leading-relaxed">
                Our website may contain links to third-party websites. We are not responsible for the privacy practices or content
                of these external sites. We encourage you to review the privacy policies of any third-party sites you visit.
              </p>
            </div>

            <div className="bg-[#1a1a1a] backdrop-blur-sm rounded-xl border border-gray-800/60 p-6 sm:p-8 shadow-xl" style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}>
              <h3 className="text-xl sm:text-2xl font-semibold text-white mb-4">Changes to This Policy</h3>
              <p className="text-gray-300 leading-relaxed">
                We may update this Privacy Policy from time to time to reflect changes in our practices or for other operational,
                legal, or regulatory reasons. We will notify you of any material changes by posting the new policy on our website
                and updating the "Last updated" date.
              </p>
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
            className="mt-16 text-center p-8 bg-[#1a1a1a] backdrop-blur-sm rounded-2xl border border-gray-800/60 shadow-xl"
            style={{ backgroundColor: 'rgba(17, 24, 39, 0.6) !important' }}
          >
            <h3 className="text-2xl font-semibold text-white mb-4">Questions About Your Privacy?</h3>
            <p className="text-gray-300 mb-6">
              If you have any questions about this Privacy Policy or our data practices, please don't hesitate to contact us.
            </p>
            <Link
              to="/about"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-500 to-orange-600 text-white px-6 py-3 rounded-lg font-medium hover:from-orange-600 hover:to-orange-700 transition-all duration-300 hover:shadow-lg hover:shadow-orange-500/25"
            >
              Contact Us
              <ArrowLeft size={18} className="rotate-180" />
            </Link>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
