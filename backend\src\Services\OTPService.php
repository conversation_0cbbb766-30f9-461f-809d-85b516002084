<?php

namespace Wolffoxx\Services;

use Wolffoxx\Config\Database;
use Wolffoxx\Utils\Logger;

/**
 * OTP Service
 * 
 * Handles OTP generation, sending, verification,
 * and cleanup for phone-based authentication.
 */
class OTPService
{
    private Logger $logger;
    private int $otpLength;
    private int $otpExpiry; // in seconds
    private int $maxAttempts;
    private SMSService $smsService;

    public function __construct()
    {
        $this->logger = new Logger('otp');
        $this->otpLength = (int)($_ENV['OTP_LENGTH'] ?? 6);
        $this->otpExpiry = (int)($_ENV['OTP_EXPIRY'] ?? 300); // 5 minutes
        $this->maxAttempts = (int)($_ENV['OTP_MAX_ATTEMPTS'] ?? 3);
        $this->smsService = new SMSService();
        
        $this->createOTPTable();
    }

    /**
     * Generate and send OTP
     */
    public function generateAndSendOTP(string $phoneNumber, string $purpose = 'login'): array
    {
        try {
            // Validate phone number
            $cleanPhone = $this->cleanPhoneNumber($phoneNumber);
            if (!$this->isValidPhoneNumber($cleanPhone)) {
                throw new \Exception('Invalid phone number format');
            }

            // Check rate limiting
            if (!$this->checkRateLimit($cleanPhone)) {
                throw new \Exception('Too many OTP requests. Please try again later.');
            }

            // Generate OTP
            $otp = $this->generateOTP();
            $expiresAt = date('Y-m-d H:i:s', time() + $this->otpExpiry);

            // Store OTP in database
            $this->storeOTP($cleanPhone, $otp, $purpose, $expiresAt);

            // Send OTP via SMS
            $smsResult = $this->smsService->sendOTP($cleanPhone, $otp, $purpose);

            if (!$smsResult['success']) {
                throw new \Exception('Failed to send OTP: ' . $smsResult['error']);
            }

            $this->logger->info('OTP generated and sent', [
                'phone' => $this->maskPhoneNumber($cleanPhone),
                'purpose' => $purpose,
                'expires_at' => $expiresAt
            ]);

            return [
                'success' => true,
                'message' => 'OTP sent successfully',
                'phone' => $this->maskPhoneNumber($cleanPhone),
                'expires_in' => $this->otpExpiry,
                'can_resend_after' => 17 // seconds - UI timer duration
            ];

        } catch (\Exception $e) {
            $this->logger->error('OTP generation failed', [
                'phone' => $this->maskPhoneNumber($phoneNumber),
                'purpose' => $purpose,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify OTP
     */
    public function verifyOTP(string $phoneNumber, string $otp, string $purpose = 'login'): array
    {
        try {
            $cleanPhone = $this->cleanPhoneNumber($phoneNumber);
            
            // Get stored OTP
            $storedOTP = $this->getStoredOTP($cleanPhone, $purpose);

            if (!$storedOTP) {
                throw new \Exception('No valid OTP found for this phone number');
            }

            // Check if OTP is expired
            if (strtotime($storedOTP['expires_at']) < time()) {
                $this->deleteOTP($cleanPhone, $purpose);
                throw new \Exception('OTP has expired. Please request a new one.');
            }

            // Check attempts
            if ($storedOTP['attempts'] >= $this->maxAttempts) {
                $this->deleteOTP($cleanPhone, $purpose);
                throw new \Exception('Maximum verification attempts exceeded. Please request a new OTP.');
            }

            // Verify OTP
            if ($storedOTP['otp'] !== $otp) {
                $this->incrementAttempts($storedOTP['id']);
                throw new \Exception('Invalid OTP. Please try again.');
            }

            // OTP is valid - delete it
            $this->deleteOTP($cleanPhone, $purpose);

            $this->logger->info('OTP verified successfully', [
                'phone' => $this->maskPhoneNumber($cleanPhone),
                'purpose' => $purpose
            ]);

            return [
                'success' => true,
                'message' => 'OTP verified successfully',
                'phone' => $cleanPhone
            ];

        } catch (\Exception $e) {
            $this->logger->warning('OTP verification failed', [
                'phone' => $this->maskPhoneNumber($phoneNumber),
                'purpose' => $purpose,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Resend OTP
     */
    public function resendOTP(string $phoneNumber, string $purpose = 'login'): array
    {
        try {
            $cleanPhone = $this->cleanPhoneNumber($phoneNumber);

            // Check if can resend (rate limiting)
            $lastOTP = $this->getLastOTP($cleanPhone);
            if ($lastOTP && (time() - strtotime($lastOTP['created_at'])) < 60) {
                $waitTime = 60 - (time() - strtotime($lastOTP['created_at']));
                throw new \Exception("Please wait {$waitTime} seconds before requesting a new OTP");
            }

            // Delete existing OTP
            $this->deleteOTP($cleanPhone, $purpose);

            // Generate and send new OTP
            return $this->generateAndSendOTP($phoneNumber, $purpose);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Generate random OTP
     */
    private function generateOTP(): string
    {
        $otp = '';
        for ($i = 0; $i < $this->otpLength; $i++) {
            $otp .= random_int(0, 9);
        }
        return $otp;
    }

    /**
     * Clean and format phone number
     */
    private function cleanPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $clean = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if missing (assuming India +91)
        if (strlen($clean) === 10) {
            $clean = '91' . $clean;
        }
        
        return $clean;
    }

    /**
     * Validate phone number format
     */
    private function isValidPhoneNumber(string $phone): bool
    {
        // Check if it's a valid format (10-15 digits)
        return preg_match('/^[0-9]{10,15}$/', $phone);
    }

    /**
     * Mask phone number for logging
     */
    private function maskPhoneNumber(string $phone): string
    {
        $clean = $this->cleanPhoneNumber($phone);
        if (strlen($clean) > 6) {
            return substr($clean, 0, 3) . '****' . substr($clean, -3);
        }
        return '****';
    }

    /**
     * Check rate limiting
     */
    private function checkRateLimit(string $phone): bool
    {
        try {
            $sql = "SELECT COUNT(*) as count FROM otps 
                    WHERE phone = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
            
            $statement = Database::execute($sql, [$phone]);
            $result = $statement->fetch();

            // Allow max 5 OTP requests per hour
            return $result['count'] < 5;

        } catch (\Exception $e) {
            $this->logger->error('Rate limit check failed', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);
            return true; // Allow on error
        }
    }

    /**
     * Store OTP in database
     */
    private function storeOTP(string $phone, string $otp, string $purpose, string $expiresAt): void
    {
        try {
            // Delete any existing OTP for this phone and purpose
            $this->deleteOTP($phone, $purpose);

            $sql = "INSERT INTO otps (phone, otp, purpose, expires_at, attempts, created_at) 
                    VALUES (?, ?, ?, ?, 0, NOW())";
            
            Database::execute($sql, [$phone, $otp, $purpose, $expiresAt]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to store OTP', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get stored OTP
     */
    private function getStoredOTP(string $phone, string $purpose): ?array
    {
        try {
            $sql = "SELECT * FROM otps WHERE phone = ? AND purpose = ? ORDER BY created_at DESC LIMIT 1";
            $statement = Database::execute($sql, [$phone, $purpose]);
            
            return $statement->fetch() ?: null;

        } catch (\Exception $e) {
            $this->logger->error('Failed to get stored OTP', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get last OTP for rate limiting
     */
    private function getLastOTP(string $phone): ?array
    {
        try {
            $sql = "SELECT * FROM otps WHERE phone = ? ORDER BY created_at DESC LIMIT 1";
            $statement = Database::execute($sql, [$phone]);
            
            return $statement->fetch() ?: null;

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Delete OTP
     */
    private function deleteOTP(string $phone, string $purpose): void
    {
        try {
            $sql = "DELETE FROM otps WHERE phone = ? AND purpose = ?";
            Database::execute($sql, [$phone, $purpose]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to delete OTP', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Increment verification attempts
     */
    private function incrementAttempts(int $otpId): void
    {
        try {
            $sql = "UPDATE otps SET attempts = attempts + 1 WHERE id = ?";
            Database::execute($sql, [$otpId]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to increment attempts', [
                'otp_id' => $otpId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Create OTP table if not exists
     */
    private function createOTPTable(): void
    {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS otps (
                id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
                phone VARCHAR(20) NOT NULL,
                otp VARCHAR(10) NOT NULL,
                purpose VARCHAR(50) NOT NULL DEFAULT 'login',
                attempts INT DEFAULT 0,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                INDEX idx_phone (phone),
                INDEX idx_phone_purpose (phone, purpose),
                INDEX idx_expires_at (expires_at),
                INDEX idx_created_at (created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

            Database::execute($sql);

        } catch (\Exception $e) {
            $this->logger->error('Failed to create OTP table', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Cleanup expired OTPs (run this periodically)
     */
    public function cleanupExpiredOTPs(): int
    {
        try {
            $sql = "DELETE FROM otps WHERE expires_at < NOW()";
            $statement = Database::execute($sql);
            
            $deletedCount = $statement->rowCount();
            
            $this->logger->info('Expired OTPs cleaned up', [
                'deleted_count' => $deletedCount
            ]);

            return $deletedCount;

        } catch (\Exception $e) {
            $this->logger->error('Failed to cleanup expired OTPs', [
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Get OTP statistics
     */
    public function getOTPStats(): array
    {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_active,
                        COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as last_hour,
                        COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as last_day,
                        AVG(attempts) as avg_attempts
                    FROM otps WHERE expires_at > NOW()";
            
            $statement = Database::execute($sql);
            return $statement->fetch();

        } catch (\Exception $e) {
            $this->logger->error('Failed to get OTP stats', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }
}
