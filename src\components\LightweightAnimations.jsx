import { useState, useEffect, useRef } from 'react';

/**
 * Lightweight animation components to replace heavy framer-motion usage
 * Uses CSS transitions and transforms for better performance
 */

/**
 * Fade In Animation
 */
export function FadeIn({ children, delay = 0, duration = 300, className = '' }) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div
      ref={elementRef}
      className={`transition-opacity ease-out ${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transitionDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
}

/**
 * Slide In Animation
 */
export function SlideIn({ 
  children, 
  direction = 'up', // 'up', 'down', 'left', 'right'
  delay = 0, 
  duration = 300, 
  distance = 20,
  className = '' 
}) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  const getTransform = () => {
    if (isVisible) return 'translate3d(0, 0, 0)';
    
    switch (direction) {
      case 'up':
        return `translate3d(0, ${distance}px, 0)`;
      case 'down':
        return `translate3d(0, -${distance}px, 0)`;
      case 'left':
        return `translate3d(${distance}px, 0, 0)`;
      case 'right':
        return `translate3d(-${distance}px, 0, 0)`;
      default:
        return `translate3d(0, ${distance}px, 0)`;
    }
  };

  return (
    <div
      className={`transition-all ease-out ${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: getTransform(),
        transitionDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
}

/**
 * Scale Animation
 */
export function ScaleIn({ children, delay = 0, duration = 300, className = '' }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  return (
    <div
      className={`transition-all ease-out ${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'scale(1)' : 'scale(0.9)',
        transitionDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
}

/**
 * Stagger Animation Container
 */
export function StaggerContainer({ children, staggerDelay = 100, className = '' }) {
  return (
    <div className={className}>
      {Array.isArray(children) 
        ? children.map((child, index) => (
            <FadeIn key={index} delay={index * staggerDelay}>
              {child}
            </FadeIn>
          ))
        : children
      }
    </div>
  );
}

/**
 * Intersection Observer Animation
 * Only animates when element enters viewport
 */
export function AnimateOnScroll({ 
  children, 
  animation = 'fadeIn', // 'fadeIn', 'slideUp', 'scaleIn'
  threshold = 0.1,
  rootMargin = '0px',
  className = ''
}) {
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.unobserve(entry.target);
        }
      },
      { threshold, rootMargin }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  const getAnimationStyles = () => {
    const baseStyles = {
      transition: 'all 0.6s ease-out'
    };

    switch (animation) {
      case 'fadeIn':
        return {
          ...baseStyles,
          opacity: isVisible ? 1 : 0
        };
      case 'slideUp':
        return {
          ...baseStyles,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'translateY(0)' : 'translateY(30px)'
        };
      case 'scaleIn':
        return {
          ...baseStyles,
          opacity: isVisible ? 1 : 0,
          transform: isVisible ? 'scale(1)' : 'scale(0.9)'
        };
      default:
        return baseStyles;
    }
  };

  return (
    <div
      ref={elementRef}
      className={className}
      style={getAnimationStyles()}
    >
      {children}
    </div>
  );
}

/**
 * Hover Animation
 */
export function HoverScale({ 
  children, 
  scale = 1.05, 
  duration = 200,
  className = '' 
}) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={`transition-transform ease-out cursor-pointer ${className}`}
      style={{
        transform: isHovered ? `scale(${scale})` : 'scale(1)',
        transitionDuration: `${duration}ms`
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
}

/**
 * Loading Spinner
 */
export function LoadingSpinner({ size = 24, color = '#3b82f6', className = '' }) {
  return (
    <div
      className={`inline-block animate-spin rounded-full border-2 border-transparent ${className}`}
      style={{
        width: size,
        height: size,
        borderTopColor: color,
        borderRightColor: color
      }}
    />
  );
}

/**
 * Pulse Animation
 */
export function Pulse({ children, duration = 2000, className = '' }) {
  return (
    <div
      className={`animate-pulse ${className}`}
      style={{
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
}

/**
 * Bounce Animation
 */
export function Bounce({ children, className = '' }) {
  return (
    <div className={`animate-bounce ${className}`}>
      {children}
    </div>
  );
}

/**
 * Page Transition (lightweight replacement for framer-motion PageTransition)
 */
export function LightPageTransition({ children, className = '' }) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  return (
    <div
      className={`transition-all duration-300 ease-out ${className}`}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : 'translateY(10px)'
      }}
    >
      {children}
    </div>
  );
}
