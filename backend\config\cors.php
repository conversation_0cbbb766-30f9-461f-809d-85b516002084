<?php

namespace Wolffoxx\Config;

/**
 * CORS Configuration and Handler
 * 
 * Handles Cross-Origin Resource Sharing (CORS) for API requests
 * with configurable origins, methods, and headers.
 */
class CORS
{
    private static array $config = [
        'allowed_origins' => [
            'http://localhost:3000',
            'http://localhost:5173',
            'http://localhost:8080',
            'https://wolffoxx.com',
            'https://www.wolffoxx.com'
        ],
        'allowed_methods' => [
            'GET',
            'POST',
            'PUT',
            'DELETE',
            'OPTIONS',
            'PATCH'
        ],
        'allowed_headers' => [
            'Content-Type',
            'Authorization',
            'X-Requested-With',
            'Accept',
            'Origin',
            'Access-Control-Request-Method',
            'Access-Control-Request-Headers'
        ],
        'exposed_headers' => [
            'Authorization',
            'X-Total-Count',
            'X-Page-Count'
        ],
        'max_age' => 3600,
        'credentials' => true
    ];

    /**
     * Initialize CORS configuration from environment
     */
    public static function init(): void
    {
        // Add frontend URL from environment
        $frontendUrl = $_ENV['FRONTEND_URL'] ?? 'http://localhost:5173';
        if (!in_array($frontendUrl, self::$config['allowed_origins'])) {
            self::$config['allowed_origins'][] = $frontendUrl;
        }

        // Add additional origins from environment
        $additionalOrigins = $_ENV['CORS_ALLOWED_ORIGINS'] ?? '';
        if (!empty($additionalOrigins)) {
            $origins = explode(',', $additionalOrigins);
            self::$config['allowed_origins'] = array_merge(
                self::$config['allowed_origins'],
                array_map('trim', $origins)
            );
        }
    }

    /**
     * Handle CORS headers for incoming requests
     */
    public static function handle(): void
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Check if origin is allowed
        if (self::isOriginAllowed($origin)) {
            header('Access-Control-Allow-Origin: ' . $origin);
        } else {
            // For development, allow all origins
            if ($_ENV['APP_ENV'] === 'development') {
                header('Access-Control-Allow-Origin: *');
            }
        }

        // Set CORS headers
        header('Access-Control-Allow-Methods: ' . implode(', ', self::$config['allowed_methods']));
        header('Access-Control-Allow-Headers: ' . implode(', ', self::$config['allowed_headers']));
        header('Access-Control-Expose-Headers: ' . implode(', ', self::$config['exposed_headers']));
        header('Access-Control-Max-Age: ' . self::$config['max_age']);

        if (self::$config['credentials']) {
            header('Access-Control-Allow-Credentials: true');
        }

        // Handle preflight requests
        if ($method === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }

    /**
     * Check if origin is allowed
     */
    private static function isOriginAllowed(string $origin): bool
    {
        if (empty($origin)) {
            return false;
        }

        return in_array($origin, self::$config['allowed_origins']);
    }

    /**
     * Add allowed origin
     */
    public static function addAllowedOrigin(string $origin): void
    {
        if (!in_array($origin, self::$config['allowed_origins'])) {
            self::$config['allowed_origins'][] = $origin;
        }
    }

    /**
     * Add allowed method
     */
    public static function addAllowedMethod(string $method): void
    {
        $method = strtoupper($method);
        if (!in_array($method, self::$config['allowed_methods'])) {
            self::$config['allowed_methods'][] = $method;
        }
    }

    /**
     * Add allowed header
     */
    public static function addAllowedHeader(string $header): void
    {
        if (!in_array($header, self::$config['allowed_headers'])) {
            self::$config['allowed_headers'][] = $header;
        }
    }

    /**
     * Get CORS configuration
     */
    public static function getConfig(): array
    {
        return self::$config;
    }

    /**
     * Set CORS configuration
     */
    public static function setConfig(array $config): void
    {
        self::$config = array_merge(self::$config, $config);
    }

    /**
     * Validate CORS request
     */
    public static function validateRequest(): bool
    {
        $origin = $_SERVER['HTTP_ORIGIN'] ?? '';
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

        // Check origin
        if (!empty($origin) && !self::isOriginAllowed($origin)) {
            // Allow in development mode
            if ($_ENV['APP_ENV'] !== 'development') {
                return false;
            }
        }

        // Check method
        if (!in_array($method, self::$config['allowed_methods'])) {
            return false;
        }

        return true;
    }

    /**
     * Send CORS error response
     */
    public static function sendErrorResponse(string $message = 'CORS policy violation'): void
    {
        http_response_code(403);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'error' => $message,
            'code' => 'CORS_ERROR'
        ]);
        exit();
    }
}
