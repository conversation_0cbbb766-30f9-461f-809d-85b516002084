import { useState, useEffect, useRef, useMemo } from 'react';
import ProductCard from './ProductCard';

/**
 * Virtual Scrolling Product Grid
 * Only renders visible products to handle large datasets efficiently
 */
export default function VirtualProductGrid({
  products = [],
  itemHeight = 400,
  containerHeight = 600,
  itemsPerRow = 2,
  gap = 16,
  className = '',
  onLoadMore,
  hasMore = false,
  loading = false
}) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerSize, setContainerSize] = useState({ width: 0, height: containerHeight });
  const containerRef = useRef(null);
  const scrollElementRef = useRef(null);

  // Calculate dimensions
  const totalItems = products.length;
  const totalRows = Math.ceil(totalItems / itemsPerRow);
  const rowHeight = itemHeight + gap;
  const totalHeight = totalRows * rowHeight;

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / rowHeight);
    const endRow = Math.min(
      totalRows - 1,
      Math.ceil((scrollTop + containerSize.height) / rowHeight)
    );
    
    // Add buffer for smooth scrolling
    const bufferRows = 2;
    const bufferedStartRow = Math.max(0, startRow - bufferRows);
    const bufferedEndRow = Math.min(totalRows - 1, endRow + bufferRows);

    return {
      startRow: bufferedStartRow,
      endRow: bufferedEndRow,
      startIndex: bufferedStartRow * itemsPerRow,
      endIndex: Math.min(totalItems - 1, (bufferedEndRow + 1) * itemsPerRow - 1)
    };
  }, [scrollTop, containerSize.height, rowHeight, totalRows, itemsPerRow, totalItems]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return products.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [products, visibleRange.startIndex, visibleRange.endIndex]);

  // Handle scroll
  const handleScroll = (e) => {
    const newScrollTop = e.target.scrollTop;
    setScrollTop(newScrollTop);

    // Load more when near bottom
    if (onLoadMore && hasMore && !loading) {
      const scrollPercentage = (newScrollTop + containerSize.height) / totalHeight;
      if (scrollPercentage > 0.8) {
        onLoadMore();
      }
    }
  };

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({
          width: rect.width,
          height: rect.height
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate item width
  const itemWidth = useMemo(() => {
    if (containerSize.width === 0) return 0;
    return (containerSize.width - (gap * (itemsPerRow - 1))) / itemsPerRow;
  }, [containerSize.width, itemsPerRow, gap]);

  return (
    <div
      ref={containerRef}
      className={`relative overflow-auto ${className}`}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      {/* Virtual container with full height */}
      <div style={{ height: totalHeight, position: 'relative' }}>
        {/* Visible items */}
        <div
          style={{
            transform: `translateY(${visibleRange.startRow * rowHeight}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }}
        >
          <div
            className="grid"
            style={{
              gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)`,
              gap: `${gap}px`,
              padding: `0 ${gap / 2}px`
            }}
          >
            {visibleItems.map((product, index) => {
              const actualIndex = visibleRange.startIndex + index;
              return (
                <div
                  key={product.id || actualIndex}
                  style={{
                    height: itemHeight,
                    width: '100%'
                  }}
                >
                  <ProductCard
                    product={product}
                    index={actualIndex}
                    viewMode="grid"
                  />
                </div>
              );
            })}
          </div>
        </div>

        {/* Loading indicator */}
        {loading && (
          <div
            className="absolute bottom-0 left-0 right-0 flex justify-center py-4"
            style={{ transform: `translateY(${totalHeight}px)` }}
          >
            <div className="flex items-center space-x-2 text-gray-400">
              <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
              <span>Loading more products...</span>
            </div>
          </div>
        )}
      </div>

      {/* Scroll indicator */}
      <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
        {Math.min(visibleRange.endIndex + 1, totalItems)} / {totalItems}
      </div>
    </div>
  );
}

/**
 * Responsive Virtual Grid Hook
 * Automatically adjusts items per row based on screen size
 */
export function useResponsiveGrid() {
  const [itemsPerRow, setItemsPerRow] = useState(2);

  useEffect(() => {
    const updateItemsPerRow = () => {
      const width = window.innerWidth;
      if (width >= 1536) setItemsPerRow(4); // 2xl
      else if (width >= 1280) setItemsPerRow(3); // xl
      else if (width >= 1024) setItemsPerRow(3); // lg
      else if (width >= 768) setItemsPerRow(2); // md
      else setItemsPerRow(2); // sm and below
    };

    updateItemsPerRow();
    window.addEventListener('resize', updateItemsPerRow);
    return () => window.removeEventListener('resize', updateItemsPerRow);
  }, []);

  return itemsPerRow;
}

/**
 * Optimized Product Grid with Virtual Scrolling
 */
export function OptimizedProductGrid({ products, onLoadMore, hasMore, loading, className }) {
  const itemsPerRow = useResponsiveGrid();
  
  return (
    <VirtualProductGrid
      products={products}
      itemsPerRow={itemsPerRow}
      itemHeight={400}
      containerHeight={800}
      gap={16}
      className={className}
      onLoadMore={onLoadMore}
      hasMore={hasMore}
      loading={loading}
    />
  );
}
