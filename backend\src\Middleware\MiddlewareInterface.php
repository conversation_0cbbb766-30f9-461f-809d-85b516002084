<?php

namespace Wolffoxx\Middleware;

/**
 * Middleware Interface
 * 
 * Defines the contract for all middleware classes
 * in the application.
 */
interface MiddlewareInterface
{
    /**
     * Handle the middleware logic
     * 
     * @param array $params Additional parameters passed to middleware
     * @return bool True to continue, false to stop execution
     */
    public function handle(array $params = []): bool;
}
