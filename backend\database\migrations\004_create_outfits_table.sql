-- Create outfits table for saved outfit collections
CREATE TABLE IF NOT EXISTS outfits (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    uuid CHAR(36) NOT NULL UNIQUE,
    user_id INT UNSIGNED NOT NULL,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    
    -- Outfit metadata
    occasion VARCHAR(100), -- 'casual', 'formal', 'party', 'work', etc.
    season VARCHAR(20), -- 'spring', 'summer', 'fall', 'winter', 'all'
    style VARCHAR(100), -- 'streetwear', 'minimalist', 'vintage', etc.
    color_scheme VARCHAR(100), -- 'monochrome', 'colorful', 'neutral', etc.
    
    -- Pricing
    total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    total_sale_price DECIMAL(10,2) NULL,
    
    -- Status and visibility
    is_public BOOLEAN DEFAULT FALSE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_complete BOOLEAN DEFAULT FALSE,
    
    -- Social features
    like_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    share_count INT DEFAULT 0,
    
    -- Tags and categorization
    tags JSON NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_uuid (uuid),
    INDEX idx_name (name),
    INDEX idx_occasion (occasion),
    INDEX idx_season (season),
    INDEX idx_style (style),
    INDEX idx_is_public (is_public),
    INDEX idx_is_featured (is_featured),
    INDEX idx_like_count (like_count),
    INDEX idx_created_at (created_at),
    FULLTEXT idx_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_items table for products in outfits
CREATE TABLE IF NOT EXISTS outfit_items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    outfit_id INT UNSIGNED NOT NULL,
    product_id INT UNSIGNED NOT NULL,
    
    -- Product variant details
    selected_color VARCHAR(100),
    selected_size VARCHAR(20),
    selected_color_hex VARCHAR(7),
    
    -- Item positioning and styling
    category_type VARCHAR(50), -- 'top', 'bottom', 'outerwear', 'footwear', 'accessory'
    sort_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE, -- Main piece of the outfit
    
    -- Pricing at time of addition
    price_at_addition DECIMAL(10,2) NOT NULL,
    sale_price_at_addition DECIMAL(10,2) NULL,
    
    -- Notes and styling tips
    styling_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate items in same outfit
    UNIQUE KEY unique_outfit_product (outfit_id, product_id),
    
    -- Indexes
    INDEX idx_outfit_id (outfit_id),
    INDEX idx_product_id (product_id),
    INDEX idx_category_type (category_type),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_primary (is_primary)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_collections table for organizing outfits
CREATE TABLE IF NOT EXISTS outfit_collections (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Collection metadata
    theme VARCHAR(100), -- 'work_wardrobe', 'vacation', 'date_night', etc.
    color VARCHAR(7) DEFAULT '#3b82f6',
    icon VARCHAR(50),
    
    -- Visibility
    is_public BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    
    -- Sorting
    sort_order INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_theme (theme),
    INDEX idx_is_public (is_public),
    INDEX idx_is_default (is_default),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_collection_items table for outfits in collections
CREATE TABLE IF NOT EXISTS outfit_collection_items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    collection_id INT UNSIGNED NOT NULL,
    outfit_id INT UNSIGNED NOT NULL,
    sort_order INT DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (collection_id) REFERENCES outfit_collections(id) ON DELETE CASCADE,
    FOREIGN KEY (outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    
    -- Unique constraint
    UNIQUE KEY unique_collection_outfit (collection_id, outfit_id),
    
    -- Indexes
    INDEX idx_collection_id (collection_id),
    INDEX idx_outfit_id (outfit_id),
    INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_likes table for social features
CREATE TABLE IF NOT EXISTS outfit_likes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    outfit_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint
    UNIQUE KEY unique_outfit_user_like (outfit_id, user_id),
    
    -- Indexes
    INDEX idx_outfit_id (outfit_id),
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_shares table for sharing outfits
CREATE TABLE IF NOT EXISTS outfit_shares (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    outfit_id INT UNSIGNED NOT NULL,
    share_token VARCHAR(255) NOT NULL UNIQUE,
    shared_by_user_id INT UNSIGNED NOT NULL,
    
    -- Share settings
    is_active BOOLEAN DEFAULT TRUE,
    allow_comments BOOLEAN DEFAULT TRUE,
    password_protected BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255) NULL,
    
    -- Access tracking
    view_count INT DEFAULT 0,
    last_viewed_at TIMESTAMP NULL,
    
    -- Expiration
    expires_at TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    FOREIGN KEY (shared_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes
    INDEX idx_share_token (share_token),
    INDEX idx_outfit_id (outfit_id),
    INDEX idx_shared_by_user_id (shared_by_user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create outfit_analytics table for tracking outfit behavior
CREATE TABLE IF NOT EXISTS outfit_analytics (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    outfit_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NULL, -- NULL for anonymous views
    action ENUM('created', 'viewed', 'liked', 'shared', 'copied', 'purchased') NOT NULL,
    
    -- Context data
    source VARCHAR(100), -- 'profile', 'explore', 'search', 'shared_link', etc.
    device_type VARCHAR(50),
    session_id VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign keys
    FOREIGN KEY (outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    
    -- Indexes
    INDEX idx_outfit_id (outfit_id),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_source (source),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
