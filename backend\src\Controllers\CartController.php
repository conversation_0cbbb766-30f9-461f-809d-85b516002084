<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Cart;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Validator;
use Wolffoxx\Utils\Logger;

/**
 * Cart Controller
 * 
 * Handles shopping cart operations including add, remove, update items
 */
class CartController extends BaseController
{
    private Cart $cartModel;
    private Product $productModel;
    private Logger $logger;

    public function __construct()
    {
        $this->cartModel = new Cart();
        $this->productModel = new Product();
        $this->logger = new Logger('cart');
    }

    /**
     * Get user's cart
     */
    public function getCart(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Get cart items for user
            $sql = "SELECT ci.*, c.user_id
                    FROM cart_items ci
                    JOIN carts c ON ci.cart_id = c.id
                    WHERE c.user_id = ?
                    ORDER BY ci.created_at DESC";

            $stmt = \Wolffoxx\Config\Database::execute($sql, [$userId]);
            $items = $stmt->fetchAll();

            // Calculate summary
            $subtotal = 0;
            $totalItems = 0;

            foreach ($items as $item) {
                // Use sale price if available, otherwise use unit price
                $price = (!empty($item['sale_price']) && $item['sale_price'] > 0) ? $item['sale_price'] : $item['unit_price'];
                $subtotal += $price * $item['quantity'];
                $totalItems += $item['quantity'];
            }

            Response::success([
                'items' => $items,
                'summary' => [
                    'subtotal' => $subtotal,
                    'total_items' => $totalItems,
                    'item_count' => count($items)
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get cart failed: ' . $e->getMessage());
            Response::error('Failed to retrieve cart');
        }
    }

    /**
     * Add item to cart
     */
    public function addItem(array $params = []): void
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            // Use Cart model to handle cart operations properly
            $cartModel = new \Wolffoxx\Models\Cart();

            // Get or create cart for user
            $cart = $cartModel->getOrCreateUserCart($userId);
            $cartId = $cart['id'];

            // Prepare item data for the Cart model
            $itemData = [
                'product_id' => $input['product_id'] ?? 1,
                'quantity' => $input['quantity'] ?? 1,
                'selected_color' => $input['selected_color'] ?? 'Default',
                'selected_size' => $input['selected_size'] ?? 'M',
                'selected_color_hex' => $input['selected_color_hex'] ?? '#000000',
                'unit_price' => $input['unit_price'] ?? 59.99,
                'sale_price' => $input['sale_price'] ?? null,
                'product_name' => $input['product_name'] ?? 'Test Product',
                'product_image' => $input['product_image'] ?? null,
                'product_sku' => $input['product_sku'] ?? 'TEST001',
                'outfit_id' => $input['outfit_id'] ?? null,
                'outfit_name' => $input['outfit_name'] ?? null
            ];

            // Use Cart model's addItem method which handles duplicates properly
            $result = $cartModel->addItem($cartId, $itemData);

            if ($result) {
                Response::success([
                    'message' => 'Item added to cart successfully',
                    'item' => [
                        'cart_id' => $cartId,
                        'product_id' => $itemData['product_id'],
                        'product_name' => $itemData['product_name'],
                        'quantity' => $itemData['quantity'],
                        'selected_color' => $itemData['selected_color'],
                        'selected_size' => $itemData['selected_size'],
                        'unit_price' => $itemData['unit_price'],
                        'outfit_name' => $itemData['outfit_name']
                    ]
                ]);
            } else {
                Response::error('Failed to add item to cart');
            }

        } catch (\Exception $e) {
            error_log('Add to cart failed: ' . $e->getMessage());
            Response::error('Failed to add item to cart: ' . $e->getMessage());
        }
    }

    /**
     * Update cart item quantity
     */
    public function updateItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $itemId = (int)($params['id'] ?? 0);
            if (!$itemId) {
                Response::error('Item ID is required', 400);
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'quantity' => 'required|integer|min:0'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Update item quantity
            $success = $this->cartModel->updateItemQuantity($itemId, $data['quantity']);

            if ($success) {
                // Get updated cart
                $cart = $this->cartModel->getOrCreateUserCart($userId);
                $updatedCart = $this->cartModel->getCartWithItems($cart['id']);
                
                $this->logger->info('Cart item updated', [
                    'user_id' => $userId,
                    'item_id' => $itemId,
                    'new_quantity' => $data['quantity']
                ]);

                Response::success([
                    'message' => 'Cart item updated successfully',
                    'cart' => $updatedCart
                ]);
            } else {
                Response::error('Failed to update cart item');
            }

        } catch (\Exception $e) {
            $this->logger->error('Update cart item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'item_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to update cart item');
        }
    }

    /**
     * Remove item from cart
     */
    public function removeItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();

            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $itemId = (int)($params['id'] ?? 0);
            if (!$itemId) {
                Response::error('Item ID is required', 400);
                return;
            }

            // Verify that the item belongs to the user's cart before removing
            $sql = "SELECT ci.id, ci.cart_id, c.user_id
                    FROM cart_items ci
                    JOIN carts c ON ci.cart_id = c.id
                    WHERE ci.id = ? AND c.user_id = ?";

            $statement = \Wolffoxx\Config\Database::execute($sql, [$itemId, $userId]);
            $item = $statement->fetch();

            if (!$item) {
                Response::error('Cart item not found or access denied', 404);
                return;
            }

            // Remove item using the Cart model
            $success = $this->cartModel->removeItem($itemId);

            if ($success) {
                $this->logger->info('Item removed from cart', [
                    'user_id' => $userId,
                    'item_id' => $itemId
                ]);

                Response::success([
                    'message' => 'Item removed from cart successfully'
                ]);
            } else {
                Response::error('Failed to remove item from cart');
            }

        } catch (\Exception $e) {
            $this->logger->error('Remove cart item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'item_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to remove cart item: ' . $e->getMessage());
        }
    }

    /**
     * Clear entire cart
     */
    public function clearCart(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $cart = $this->cartModel->getOrCreateUserCart($userId);
            $success = $this->cartModel->clearCart($cart['id']);

            if ($success) {
                $this->logger->info('Cart cleared', [
                    'user_id' => $userId,
                    'cart_id' => $cart['id']
                ]);

                Response::success([
                    'message' => 'Cart cleared successfully'
                ]);
            } else {
                Response::error('Failed to clear cart');
            }

        } catch (\Exception $e) {
            $this->logger->error('Clear cart failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to clear cart');
        }
    }

    /**
     * Get cart count
     */
    public function getCartCount(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $count = $this->cartModel->getUserCartCount($userId);

            Response::success([
                'count' => $count
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Get cart count failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to get cart count');
        }
    }
}
