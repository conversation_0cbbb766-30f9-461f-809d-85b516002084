<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Address Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        #response {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Test Address Form</h1>
    
    <form id="addressForm">
        <div class="form-group">
            <label for="first_name">First Name:</label>
            <input type="text" id="first_name" name="first_name" required>
        </div>
        
        <div class="form-group">
            <label for="last_name">Last Name:</label>
            <input type="text" id="last_name" name="last_name" required>
        </div>
        
        <div class="form-group">
            <label for="address_line_1">Address Line 1:</label>
            <input type="text" id="address_line_1" name="address_line_1" required>
        </div>
        
        <div class="form-group">
            <label for="address_line_2">Address Line 2:</label>
            <input type="text" id="address_line_2" name="address_line_2">
        </div>
        
        <div class="form-group">
            <label for="city">City:</label>
            <input type="text" id="city" name="city" required>
        </div>
        
        <div class="form-group">
            <label for="state">State:</label>
            <input type="text" id="state" name="state" required>
        </div>
        
        <div class="form-group">
            <label for="postal_code">Postal Code:</label>
            <input type="text" id="postal_code" name="postal_code" required>
        </div>
        
        <div class="form-group">
            <label for="country">Country:</label>
            <input type="text" id="country" name="country" required value="US">
        </div>
        
        <div class="form-group">
            <label for="phone">Phone:</label>
            <input type="text" id="phone" name="phone">
        </div>
        
        <div class="form-group">
            <label for="type">Address Type:</label>
            <select id="type" name="type">
                <option value="shipping">Shipping</option>
                <option value="billing">Billing</option>
                <option value="both">Both</option>
            </select>
        </div>
        
        <div class="form-group">
            <label>
                <input type="checkbox" id="is_default" name="is_default" checked>
                Set as default address
            </label>
        </div>
        
        <button type="submit">Submit</button>
    </form>
    
    <div id="response"></div>
    
    <script>
        document.getElementById('addressForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const addressData = {};
            
            for (const [key, value] of formData.entries()) {
                if (key === 'is_default') {
                    addressData[key] = true;
                } else {
                    addressData[key] = value;
                }
            }
            
            // Create the payload
            const payload = {
                address_data: addressData
            };
            
            const responseDiv = document.getElementById('response');
            responseDiv.textContent = 'Sending request...';
            
            // Send the request
            fetch('/api/v1/profile', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + localStorage.getItem('token')
                },
                body: JSON.stringify(payload)
            })
            .then(response => response.json())
            .then(data => {
                responseDiv.textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                responseDiv.textContent = 'Error: ' + error.message;
            });
        });
    </script>
</body>
</html>