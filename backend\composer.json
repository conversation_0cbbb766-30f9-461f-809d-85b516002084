{"name": "wolffoxx/ecommerce-backend", "description": "PHP Backend API for Wolffoxx Ecommerce Platform", "type": "project", "license": "MIT", "require": {"php": ">=8.1", "firebase/php-jwt": "^6.8", "vlucas/phpdotenv": "^5.5", "phpmailer/phpmailer": "^6.8", "intervention/image": "^2.7", "ramsey/uuid": "^4.7", "monolog/monolog": "^3.4"}, "require-dev": {"phpunit/phpunit": "^10.3", "squizlabs/php_codesniffer": "^3.7"}, "autoload": {"psr-4": {"Wolffoxx\\": "src/"}}, "autoload-dev": {"psr-4": {"Wolffoxx\\Tests\\": "tests/"}}, "scripts": {"test": "phpunit", "cs-check": "phpcs", "cs-fix": "phpcbf"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}