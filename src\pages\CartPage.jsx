import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import {
  ShoppingBag,
  ChevronLeft,
  Minus,
  Plus,
  Trash2,
  CreditCard,
  ShieldCheck,
  Truck,
  CheckCircle,
  Mail,
  Edit
} from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { dataService } from '../services/dataService';
import { emailService } from '../services/emailService';
// No need for AddressForm as we use the profile's address

function CartPage() {
  const navigate = useNavigate();
  const { items, removeFromCart, updateQuantity, clearCart, subtotal } = useCart();
  const { user, isAuthenticated, updateProfile } = useAuth();
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(null);
  const [animateTotal, setAnimateTotal] = useState(false);
  const [orderConfirmed, setOrderConfirmed] = useState(false);
  const [orderDetails, setOrderDetails] = useState(null);
  // No need for address form state variables as we use the profile's address

  // Helper to extract a usable shipping address from the user object
  const extractShippingAddress = (user) => {
    if (!user) return null;
    // 1. Direct shipping_address field
    if (user.shipping_address && Object.keys(user.shipping_address).length > 0) {
      return {
        line1: user.shipping_address.line1 || user.shipping_address.street || user.shipping_address.address_line_1 || '',
        line2: user.shipping_address.line2 || user.shipping_address.address_line_2 || '',
        city: user.shipping_address.city || '',
        state: user.shipping_address.state || '',
        postal_code: user.shipping_address.postal_code || user.shipping_address.zip || '',
        country: user.shipping_address.country || 'India'
      };
    }
    // 2. Fallback to addresses array
    if (Array.isArray(user.addresses)) {
      const found = user.addresses.find(
        (addr) => addr.type === 'shipping' || addr.type === 'both' || addr.is_default
      );
      if (found && Object.keys(found).length > 0) {
        return {
          line1: found.address_line_1 || found.line1 || found.street || '',
          line2: found.address_line_2 || found.line2 || '',
          city: found.city || '',
          state: found.state || '',
          postal_code: found.postal_code || found.zip || '',
          country: found.country || 'India'
        };
      }
    }
    return null;
  };

  // Email validation function
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Handle address form submission
  // No need for address form handling as we use the profile's address

  useEffect(() => {
    setAnimateTotal(true);
    const timer = setTimeout(() => setAnimateTotal(false), 500);
    return () => clearTimeout(timer);
  }, [subtotal]);

  const handleCheckout = async () => {
    console.log('🛒 handleCheckout - Starting checkout process');
    
    // 1. User Authentication Check
    if (!isAuthenticated || !user) {
      console.error('🛒 Authentication required - User not logged in');
      alert('You must be logged in to place an order.');
      navigate('/login');
      return;
    }

    // 2. Cart Validation
    if (items.length === 0) {
      alert('Your cart is empty');
      return;
    }

    // 3. Profile Completeness Validation
    // 3.1 Email validation
    if (!user.email || user.email.trim() === '' || !isValidEmail(user.email)) {
      console.error('🛒 Profile incomplete - Missing valid email');
      alert('Please complete your profile with a valid email address before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.2 Phone validation
    if (!user.phone || user.phone.trim() === '') {
      console.error('🛒 Profile incomplete - Missing phone number');
      alert('Please complete your profile with a valid phone number before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.3 Name validation
    const customerName = user.name || `${user.first_name || ''} ${user.last_name || ''}`.trim();
    if (!customerName) {
      console.error('🛒 Profile incomplete - Missing name');
      alert('Please complete your profile with your name before placing an order.');
      navigate('/profile');
      return;
    }

    // 3.4 Shipping address validation
    const shippingAddress = extractShippingAddress(user);
    const requiredAddressFields = ['line1', 'city', 'state', 'postal_code', 'country'];
    const missingAddressFields = requiredAddressFields.filter(
      (field) => !shippingAddress || !shippingAddress[field] || shippingAddress[field].toString().trim() === ''
    );
    if (missingAddressFields.length > 0) {
      console.error('🛒 Profile incomplete - Missing shipping address fields', missingAddressFields);
      alert('Please complete your shipping address in your profile before placing an order.');
      navigate('/profile');
      return;
    }

    // Use the extracted shipping address for order creation
    const shippingAddressFromProfile = shippingAddress;

    // 4. Start checkout process
    setIsCheckingOut(true);

    try {
      // Calculate totals
      const tax = subtotal * 0.07;
      const total = subtotal + tax;
      // No discount applied in this implementation

      // 5. Prepare order data with all required fields
      const orderData = {
        // Customer details
        customer_name: customerName,
        customer_email: user.email,
        customer_phone: user.phone,
        
        // Addresses - Use the shipping address from the user profile
        shipping_address: shippingAddressFromProfile,
        billing_address: shippingAddressFromProfile, // Always use shipping address for billing
        
        // Payment and pricing details
        payment_method: 'demo_payment',
        subtotal: subtotal,
        total_amount: total,
        tax_amount: tax,
        shipping_amount: 0,
        discount_amount: 0, // No discount applied
        
        // Additional info
        order_notes: 'Order from cart page',
        
        // Cart items for order_items table
        items: items.map(item => ({
          product_id: item.id,
          selected_color: item.color,
          selected_size: item.size,
          selected_color_hex: item.colorHex,
          quantity: item.quantity,
          unit_price: parseFloat(item.price) || 0,
          sale_price: parseFloat(item.salePrice) || 0,
          total_price: (parseFloat(item.salePrice || item.price) || 0) * item.quantity,
          product_name: item.name,
          product_image: item.image,
          product_sku: item.sku || `SKU-${item.id}`,
          product_category: item.category || 'Uncategorized',
          outfit_id: item.outfitId,
          outfit_name: item.outfitName
        }))
      };

      console.log('🛒 Creating order with data:', orderData);
      
      // 6. Begin database transaction via API
      const order = await dataService.createOrder(user.id, orderData);
      console.log('✅ Order created successfully:', order);

      // 7. The backend now handles email sending with correct cart data from database
      // Set order details for frontend confirmation display using the order response
      const orderDetails = {
        order_number: order.order_number,
        total_amount: total,
        subtotal: subtotal,
        tax_amount: tax,
        shipping_amount: 0,
        // Use the items from the order response which contains the correct quantities from database
        items: order.items || items.map(item => ({
          name: item.name,
          quantity: item.quantity,
          price: parseFloat(item.salePrice || item.price) || 0,
          color: item.color,
          size: item.size,
          total: (parseFloat(item.salePrice || item.price) || 0) * item.quantity
        })),
        customer_email: user.email,
        customer_name: orderData.customer_name,
        shipping_address: orderData.shipping_address
      };

      setOrderDetails(orderDetails);

      // 8. Email is now sent by the backend during order creation with correct database quantities
      console.log('📧 Order confirmation email sent by backend with correct quantities');

      // 9. Clear cart and show confirmation
      clearCart();
      setOrderConfirmed(true);

    } catch (error) {
      console.error('🚨 Order creation failed:', error);
      
      // 10. Handle specific error cases
      if (error.message?.includes('Authentication required')) {
        alert('You must be logged in to place an order. Please log in and try again.');
        navigate('/login');
      } else if (error.message?.includes('Profile incomplete')) {
        alert('Please complete your profile before placing an order.');
        navigate('/profile');
      } else if (error.message?.includes('address')) {
        alert('Please provide complete shipping address in your profile.');
        navigate('/profile');
      } else {
        // Generic error message
        alert('Something went wrong. Please try again.');
      }
    } finally {
      setIsCheckingOut(false);
    }
  };

  const handleRemoveItem = (itemId, color, size) => {
    removeFromCart(itemId, color, size);
    setShowDeleteConfirm(null);
  };

  const handleQuantityDecrease = (item) => {
    if (item.quantity > 1) {
      updateQuantity(item.id, item.quantity - 1, item.color, item.size);
    }
  };

  const handleQuantityIncrease = (item) => {
    updateQuantity(item.id, item.quantity + 1, item.color, item.size);
  };



  const tax = subtotal * 0.07;
  const total = subtotal + tax;

  // Order Confirmation Component
  if (orderConfirmed && orderDetails) {
    // Calculate estimated delivery date (7 days from now)
    const estimatedDelivery = new Date();
    estimatedDelivery.setDate(estimatedDelivery.getDate() + 7);
    
    return (
      <div className="min-h-screen bg-[#000000]">
        <div className="container mx-auto px-4 py-6 max-w-4xl">
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="bg-green-500 p-6 rounded-full">
                <CheckCircle size={48} className="text-white" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold text-white mb-4">
              Order Confirmed!
            </h1>
            <p className="text-[#AAAAAA] text-lg mb-2">
              Thank you for your order, {orderDetails.customer_name}
            </p>
            <p className="text-[#AAAAAA] mb-6">
              Order #{orderDetails.order_number}
            </p>
          </div>

          <div className="bg-[#1a1a1a] rounded-xl p-6 border border-[#404040] mb-6">
            <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Mail size={20} />
              Confirmation Email Sent
            </h2>
            <p className="text-[#AAAAAA] mb-4">
              A confirmation email has been sent to <span className="text-white font-medium">{orderDetails.customer_email}</span> with your order details and tracking information.
            </p>
            
            {/* Order Summary */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
              <h3 className="text-white font-medium mb-3">Order Summary</h3>
              <div className="space-y-2">
                {orderDetails.items.map((item, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">
                      {item.name} ({item.color}, {item.size}) x {item.quantity}
                    </span>
                    <span className="text-white">₹{item.total.toFixed(2)}</span>
                  </div>
                ))}
                <div className="border-t border-[#404040] pt-2 mt-2 space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white">₹{orderDetails.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Tax (7%)</span>
                    <span className="text-white">₹{orderDetails.tax_amount.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white">{orderDetails.shipping_amount > 0 ? `₹${orderDetails.shipping_amount.toFixed(2)}` : 'Free'}</span>
                  </div>
                  <div className="border-t border-[#404040] pt-2">
                    <div className="flex justify-between font-semibold">
                      <span className="text-white">Total</span>
                      <span className="text-white">₹{orderDetails.total_amount.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Shipping Information */}
            {orderDetails.shipping_address && (
              <div className="bg-[#2a2a2a] p-4 rounded-lg mb-4">
                <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                  <Truck size={16} className="text-amber-500" />
                  Shipping Information
                </h3>
                <div className="text-sm text-[#AAAAAA]">
                  <p className="text-white font-medium">{orderDetails.customer_name}</p>
                  <p>{orderDetails.shipping_address.line1 || orderDetails.shipping_address.street}</p>
                  {orderDetails.shipping_address.line2 && <p>{orderDetails.shipping_address.line2}</p>}
                  <p>
                    {orderDetails.shipping_address.city}, {orderDetails.shipping_address.state} {orderDetails.shipping_address.postal_code || orderDetails.shipping_address.zip}
                  </p>
                  <p>{orderDetails.shipping_address.country}</p>
                </div>
              </div>
            )}
            
            {/* Estimated Delivery */}
            <div className="bg-[#2a2a2a] p-4 rounded-lg">
              <h3 className="text-white font-medium mb-3 flex items-center gap-2">
                <Truck size={16} className="text-emerald-500" />
                Estimated Delivery
              </h3>
              <p className="text-[#AAAAAA] text-sm">
                Your order should arrive by <span className="text-white font-medium">
                  {estimatedDelivery.toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </span>
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => navigate('/orders')}
              className="px-6 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E] text-white font-medium rounded-lg hover:from-[#1a3f9e] hover:to-[#4a9dd4] transition-all duration-300"
            >
              View Order History
            </button>
            <button
              onClick={() => navigate('/collections')}
              className="px-6 py-3 bg-[#2a2a2a] text-white font-medium rounded-lg hover:bg-[#404040] transition-colors border border-[#404040]"
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#000000]">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Mobile Header */}
        <div className="mb-6 flex items-center justify-between">
          <button
            onClick={() => navigate(-1)}
            className="text-[#AAAAAA] hover:text-white flex items-center gap-2 transition-all duration-300 group"
          >
            <div className="bg-[#2a2a2a] p-2 rounded-full group-hover:bg-[#404040] transition-colors">
              <ChevronLeft size={16} />
            </div>
            <span className="font-medium hidden sm:inline">Continue Shopping</span>
          </button>
          {/* Mobile trust badges */}
          <div className="flex items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <ShieldCheck size={16} className="text-emerald-500" />
              <span className="hidden sm:inline">Secure</span>
            </div>
            <div className="flex items-center gap-1 text-[#AAAAAA] text-xs sm:text-sm">
              <Truck size={16} className="text-amber-500" />
              <span className="hidden sm:inline">Free Ship</span>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="mb-8 border-b border-[#2a2a2a] pb-6">
          <h1 className="text-3xl sm:text-4xl font-bold text-white mb-2 tracking-tighter">
            Your <span className="text-[#AAAAAA]">Cart</span>
          </h1>
          <p className="text-[#AAAAAA] text-sm sm:text-base">
            {items.length} {items.length === 1 ? 'item' : 'items'} in your shopping bag
          </p>
        </div>

        {items.length === 0 ? (
          <div className="text-center py-16 bg-[#1a1a1a] rounded-xl border border-[#404040] backdrop-blur-sm shadow-xl">
            <div className="flex justify-center mb-6">
              <div className="bg-[#2a2a2a] p-6 rounded-full">
                <ShoppingBag size={40} className="text-[#AAAAAA]" />
              </div>
            </div>
            <h2 className="text-xl sm:text-2xl text-white mb-4 font-bold">Your cart is empty</h2>
            <p className="text-[#AAAAAA] mb-8 max-w-md mx-auto text-sm sm:text-base px-4">
              Looks like you haven't added anything to your cart yet. Discover our premium selection and find something amazing.
            </p>
            <button
              onClick={() => navigate('/collections')}
              className="px-8 py-3 bg-gradient-to-br from-[#FF6B35] to-[#F7931E]  text-white font-medium rounded-lg transition-all duration-300 shadow-lg"
            >
              Start Shopping
            </button>
          </div>
        ) : (
          <div className="space-y-6 lg:grid lg:grid-cols-12 lg:gap-8 lg:space-y-0">
            {/* Cart Items - Mobile First */}
            <div className="lg:col-span-8">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl overflow-hidden shadow-xl border border-[#404040]">
                {/* Desktop Header - Hidden on Mobile */}
                <div className="hidden md:block p-6 border-b border-[#2a2a2a]">
                  <div className="flex justify-between text-[#AAAAAA] text-sm font-medium">
                    <span className="uppercase tracking-wider">Product</span>
                    <div className="grid grid-cols-3 gap-4 w-[280px]">
                      <span className="text-center uppercase tracking-wider">Price</span>
                      <span className="text-center uppercase tracking-wider">Qty</span>
                      <span className="text-center uppercase tracking-wider">Total</span>
                    </div>
                  </div>
                </div>

                {/* Cart Items */}
                {items.map((item) => (
                  <div
                    key={`${item.id}-${item.color}-${item.size}`}
                    className="p-3 sm:p-6 border-b border-[#2a2a2a] hover:bg-[#2a2a2a] transition-colors duration-300"
                  >
                    {/* Mobile Layout */}
                    <div className="md:hidden">
                      <div className="flex gap-3 mb-4">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-20 h-20 sm:w-24 sm:h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg flex-shrink-0 hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                        <div className="flex-1 min-w-0 pr-2">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white font-medium text-sm sm:text-base mb-2 line-clamp-2 hover:text-cyan-400 transition-colors leading-tight">
                              {item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right flex-shrink-0 min-w-[90px] flex flex-col items-end">
                          <div className="text-white font-medium text-base sm:text-lg mb-3">
                            ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                          </div>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1">
                              <button
                                className="bg-red-600 text-white p-2 rounded-md hover:bg-red-700 transition-colors active:scale-95"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={14} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-2 rounded-md hover:bg-[#6a6a6a] transition-colors active:scale-95"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={14} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-red-400 hover:text-red-500 transition-colors p-2 hover:scale-110 active:scale-95 bg-[#2a2a2a] rounded-md"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                      {/* Mobile Quantity Controls */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={16} />
                          </button>
                          <div className="w-12 h-10 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-10 h-10 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={16} />
                          </button>
                        </div>
                        <div className="text-white font-semibold text-lg">
                          ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                        </div>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden md:flex md:items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Link
                          to={`/product/${item.id}`}
                          className="relative overflow-hidden rounded-lg w-24 h-24 bg-gradient-to-br from-gray-800 to-gray-900 shadow-lg hover:scale-105 transition-transform duration-300"
                        >
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-full h-full object-cover"
                          />
                        </Link>
                        <div className="min-w-0 flex-1">
                          <Link to={`/product/${item.id}`}>
                            <h3 className="text-white hover:text-cyan-400 font-medium text-lg transition-colors duration-300 cursor-pointer line-clamp-2 leading-tight">
                              {item.name}
                            </h3>
                          </Link>
                          <div className="text-[#AAAAAA] text-sm mt-2 space-y-1">
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full border border-[#404040]"
                                style={{
                                  backgroundColor: !item.color || item.color === 'Default' ? '#6b7280' : (item.color?.toLowerCase?.()?.replace(' ', '') || '#6b7280'),
                                  opacity: !item.color || item.color === 'Default' ? 0.5 : 1
                                }}
                              ></div>
                              <span>{item.color}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 rounded-sm border border-[#404040] flex items-center justify-center text-[9px]">
                                {item.size}
                              </div>
                              <span>Size: {item.size}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 items-center w-[280px]">
                        <div className="text-white text-center font-medium">
                          ₹{(parseFloat(item.salePrice || item.price) || 0).toFixed(2)}
                        </div>
                        <div className="flex items-center justify-center">
                          <button
                            className="w-8 h-8 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-l-md transition-colors active:scale-95"
                            onClick={() => handleQuantityDecrease(item)}
                            disabled={item.quantity <= 1}
                          >
                            <Minus size={14} />
                          </button>
                          <div className="w-10 h-8 bg-[#2a2a2a] flex items-center justify-center text-white font-medium">
                            {item.quantity}
                          </div>
                          <button
                            className="w-8 h-8 bg-[#2a2a2a] text-[#AAAAAA] flex items-center justify-center hover:bg-[#404040] rounded-r-md transition-colors active:scale-95"
                            onClick={() => handleQuantityIncrease(item)}
                          >
                            <Plus size={14} />
                          </button>
                        </div>
                        <div className="flex items-center justify-between min-w-[120px]">
                          <span className="text-white font-medium">
                            ₹{((parseFloat(item.salePrice || item.price) || 0) * item.quantity).toFixed(2)}
                          </span>
                          {showDeleteConfirm === `${item.id}-${item.color}-${item.size}` ? (
                            <div className="flex items-center gap-1 ml-2">
                              <button
                                className="bg-red-600 text-white p-1.5 rounded-md hover:bg-red-700 transition-colors"
                                onClick={() => handleRemoveItem(item.id, item.color, item.size)}
                              >
                                <Trash2 size={12} />
                              </button>
                              <button
                                className="bg-[#404040] text-white p-1.5 rounded-md hover:bg-[#6a6a6a] transition-colors"
                                onClick={() => setShowDeleteConfirm(null)}
                              >
                                <ChevronLeft size={12} />
                              </button>
                            </div>
                          ) : (
                            <button
                              className="text-[#AAAAAA] hover:text-red-500 transition-colors p-1.5 hover:scale-110 active:scale-95 ml-2"
                              onClick={() => setShowDeleteConfirm(`${item.id}-${item.color}-${item.size}`)}
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                <div className="p-4 sm:p-6 flex justify-end">
                  <button
                    onClick={clearCart}
                    className="text-[#AAAAAA] hover:text-red-500 text-sm flex items-center gap-1 transition-colors duration-300 p-2"
                  >
                    <Trash2 size={14} />
                    <span>Clear cart</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-4">
              <div className="bg-[#1a1a1a] backdrop-blur-md rounded-xl p-4 sm:p-6 border border-[#404040] shadow-xl lg:sticky lg:top-8">
                <h2 className="text-xl sm:text-2xl font-semibold text-white mb-6 sm:mb-8 border-b border-[#2a2a2a] pb-4">
                  Order Summary
                </h2>
                <div className="space-y-3 sm:space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Subtotal</span>
                    <span className="text-white font-medium">₹{subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Shipping</span>
                    <span className="text-white font-medium">Free</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-[#AAAAAA]">Tax (7%)</span>
                    <span className="text-white font-medium">₹{tax.toFixed(2)}</span>
                  </div>
                </div>
                <div className="border-t border-[#2a2a2a] pt-4 sm:pt-6 mb-6">
                  <div className="flex justify-between text-lg sm:text-xl font-semibold">
                    <span className="text-white">Total</span>
                    <span
                      className={`text-white transition-all duration-500 ${
                        animateTotal ? 'scale-110 text-gray-300' : ''
                      }`}
                    >
                      ₹{total.toFixed(2)}
                    </span>
                  </div>
                
                {/* Shipping Address Section */}
                <div className="mb-6 border-t border-[#2a2a2a] pt-4">
                  {/* <div className="flex justify-between items-center mb-3">
                    <h3 className="text-white font-medium">Shipping Address</h3>
                    <button 
                      onClick={() => navigate('/profile')}
                      className="text-cyan-400 hover:text-cyan-300 flex items-center gap-1 text-sm"
                    >
                      <Edit size={14} />
                      {user?.shipping_address && user.shipping_address.city ? 'Edit' : 'Add'}
                    </button>
                  </div> */}
                  
                  {user?.shipping_address && user.shipping_address.city ? (
                    <div className="bg-[#2a2a2a] p-3 rounded-lg text-sm text-[#AAAAAA]">
                      <p className="text-white">{user?.name || `${user?.first_name || ''} ${user?.last_name || ''}`.trim() || 'Your Name'}</p>
                      <p>{user.shipping_address.line1 || user.shipping_address.street}</p>
                      {user.shipping_address.line2 && <p>{user.shipping_address.line2}</p>}
                      <p>{user.shipping_address.city}, {user.shipping_address.state} {user.shipping_address.postal_code || user.shipping_address.zip}</p>
                      <p>{user.shipping_address.country}</p>
                    </div>
                  ) : (
                    <div className="bg-[#2a2a2a] p-3 rounded-lg text-sm text-[#AAAAAA] text-center">
                      <p>No shipping address added</p>
                      <button 
                        onClick={() => navigate('/profile')}
                        className="text-cyan-400 hover:text-cyan-300 mt-2"
                      >
                        Add Address
                      </button>
                    </div>
                  )}
                  
                  {/* Note about billing address */}
                  <div className="mt-3 flex items-center">
                    <input
                      type="checkbox"
                      id="sameAsBilling"
                      checked={true}
                      disabled={true}
                      className="mr-2 h-4 w-4 accent-cyan-500"
                    />
                    <label htmlFor="sameAsBilling" className="text-[#AAAAAA] text-sm">
                      Using shipping address for billing
                    </label>
                  </div>
                </div>
                <button
                  className={`w-full py-4 bg-[#FF6F35]  text-white font-medium rounded-lg transition-all duration-300 flex items-center justify-center gap-2 shadow-lg mb-6 text-base sm:text-lg ${
                    isCheckingOut ? 'opacity-75 cursor-wait' : 'hover:scale-105 active:scale-95'
                  }`}
                  onClick={handleCheckout}
                  disabled={isCheckingOut}
                >
                  {isCheckingOut ? (
                    <>
                      <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin " />
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard size={18}/>
                      Complete Order
                    </>
                  )}
                </button>



                {/* Payment Methods */}
<div className="grid grid-cols-3 gap-3">
  {/* Visa */}
<div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
  <img
    src="https://static.cdnlogo.com/logos/v/34/visa.svg"
    alt="Visa"
    className="h-6 w-auto"
  />
</div>

  {/* Mastercard */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/2/2a/Mastercard-logo.svg"
      alt="Mastercard"
      className="h-6"
    />
  </div>

  {/* PayPal */}
  <div className="bg-white rounded-lg p-3 flex items-center justify-center h-12 shadow-sm hover:shadow-md transition-shadow">
    <img
      src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
      alt="PayPal"
      className="h-6"
    />
  </div>
</div>
                {/* Trust Badges */}
                <div className="space-y-3 sm:space-y-4">
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <ShieldCheck size={18} className="text-emerald-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">100% Secure Checkout</span>
                      <br />All transactions are secured and encrypted
                    </p>
                  </div>
                  <div className="bg-[#2a2a2a] p-3 sm:p-4 rounded-lg flex gap-3 border border-[#404040]">
                    <Truck size={18} className="text-amber-500 flex-shrink-0 mt-1" />
                    <p className="text-[#AAAAAA] text-sm">
                      <span className="font-medium">Free Shipping</span>
                      <br />On all orders over ₹2000
                    </p>
                  </div>
                </div>
              </div>
            </div>
        
          </div>
      </div>
              )}

    </div>
              
    </div>
  );
}
  
export default CartPage;