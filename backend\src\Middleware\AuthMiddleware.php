<?php

namespace Wolffoxx\Middleware;

use Wolffoxx\Config\JWTConfig;
use Wolffoxx\Models\User;
use <PERSON>oxx\Utils\Logger;
use Wolffoxx\Utils\Response;

/**
 * Authentication Middleware
 * 
 * Validates JWT tokens and ensures user authentication
 * for protected routes.
 */
class AuthMiddleware implements MiddlewareInterface
{
    private Logger $logger;
    private ?array $currentUser = null;

    public function __construct()
    {
        $this->logger = new Logger('auth_middleware');
    }

    /**
     * Handle authentication middleware
     */
    public function handle(array $params = []): bool
    {
        $requiredRole = $params[0] ?? null;

        $this->logger->debug('Auth middleware executing', [
            'required_role' => $requiredRole,
            'has_auth_header' => isset($_SERVER['HTTP_AUTHORIZATION'])
        ]);

        // Extract token from Authorization header
        $token = JWTConfig::extractTokenFromHeader();

        $this->logger->debug('Auth token extraction', [
            'token_present' => !empty($token),
            'token_preview' => $token ? substr($token, 0, 20) . '...' : 'none',
            'auth_header' => $_SERVER['HTTP_AUTHORIZATION'] ?? 'none'
        ]);

        if (!$token) {
            $this->logger->warning('No authentication token provided');
            Response::unauthorized('Authentication token required');
            return false;
        }

        // Validate token
        $payload = JWTConfig::validateToken($token);

        if (!$payload) {
            $this->logger->warning('Invalid authentication token', [
                'token_preview' => substr($token, 0, 20) . '...'
            ]);
            Response::unauthorized('Invalid or expired token');
            return false;
        }

        // Check token type
        if (($payload['type'] ?? '') !== 'access') {
            $this->logger->warning('Invalid token type', [
                'token_type' => $payload['type'] ?? 'unknown'
            ]);
            Response::unauthorized('Invalid token type');
            return false;
        }

        // Get user from database
        $userId = $payload['user_id'] ?? null;
        if (!$userId) {
            $this->logger->error('Token missing user_id');
            Response::unauthorized('Invalid token payload');
            return false;
        }

        $userModel = new User();
        $user = $userModel->findById($userId);

        if (!$user) {
            $this->logger->warning('User not found for token', [
                'user_id' => $userId
            ]);
            Response::unauthorized('User not found');
            return false;
        }

        // Check if user is active
        if (!$user['is_active']) {
            $this->logger->warning('Inactive user attempted access', [
                'user_id' => $userId,
                'email' => $user['email']
            ]);
            Response::forbidden('Account is inactive');
            return false;
        }

        // Check role requirements
        if ($requiredRole) {
            if (!$this->checkUserRole($user, $requiredRole)) {
                $this->logger->warning('Insufficient permissions', [
                    'user_id' => $userId,
                    'required_role' => $requiredRole,
                    'user_role' => $user['is_admin'] ? 'admin' : 'user'
                ]);
                Response::forbidden('Insufficient permissions');
                return false;
            }
        }

        // Store current user for use in controllers
        $this->currentUser = $user;
        
        // Store in global context for easy access
        $GLOBALS['current_user'] = $user;
        $GLOBALS['current_user_id'] = $user['id'];

        $this->logger->info('User authenticated successfully', [
            'user_id' => $userId,
            'email' => $user['email'],
            'role' => $user['is_admin'] ? 'admin' : 'user'
        ]);

        return true;
    }

    /**
     * Check if user has required role
     */
    private function checkUserRole(array $user, string $requiredRole): bool
    {
        switch ($requiredRole) {
            case 'admin':
                return $user['is_admin'] == 1;
            
            case 'user':
                return true; // Any authenticated user
            
            default:
                return false;
        }
    }

    /**
     * Get current authenticated user
     */
    public function getCurrentUser(): ?array
    {
        return $this->currentUser;
    }

    /**
     * Get current user ID
     */
    public static function getCurrentUserId(): ?int
    {
        // Try global first (from full middleware)
        if (isset($GLOBALS['current_user_id'])) {
            return $GLOBALS['current_user_id'];
        }

        // Fallback to JWT token validation for direct API calls
        try {
            $token = JWTConfig::extractTokenFromHeader();
            if (!$token) {
                return null;
            }

            $payload = JWTConfig::validateToken($token);
            if (!$payload || !isset($payload['user_id'])) {
                return null;
            }

            return (int)$payload['user_id'];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get current user data
     */
    public static function getCurrentUserData(): ?array
    {
        return $GLOBALS['current_user'] ?? null;
    }

    /**
     * Check if current user is admin
     */
    public static function isCurrentUserAdmin(): bool
    {
        $user = self::getCurrentUserData();
        return $user && $user['is_admin'] == 1;
    }

    /**
     * Require admin role for current user
     */
    public static function requireAdmin(): void
    {
        if (!self::isCurrentUserAdmin()) {
            Response::forbidden('Admin access required');
        }
    }

    /**
     * Check if user owns resource
     */
    public static function checkResourceOwnership(int $resourceUserId): bool
    {
        $currentUserId = self::getCurrentUserId();
        
        // Admin can access any resource
        if (self::isCurrentUserAdmin()) {
            return true;
        }

        // User can only access their own resources
        return $currentUserId === $resourceUserId;
    }

    /**
     * Require resource ownership
     */
    public static function requireResourceOwnership(int $resourceUserId): void
    {
        if (!self::checkResourceOwnership($resourceUserId)) {
            Response::forbidden('Access denied to this resource');
        }
    }
}
