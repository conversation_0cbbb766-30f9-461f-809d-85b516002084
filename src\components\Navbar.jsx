import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, Heart, LogIn, Menu, Search, ShoppingBag, User, X, Home, Info, ArrowRight, Shirt, Package } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useWishlist } from '../context/WishlistContext';
import { useOutfit } from '../context/OutfitContext';
import { useAuth } from '../context/AuthContext';
import SearchBar from './SearchBar';
import drawingLogo from '../assets/logo30.svg';

// Professional categories data - clean and minimal
const categories = [
  // Special Categories
  { id: 1, name: "Deal of the Day", route: "/deals", isSpecial: true },
  { id: 2, name: "Bestsellers", route: "/category/bestsellers", isSpecial: true },

  // Oversized Collection
  { id: 3, name: "Oversized Te<PERSON>", route: "/category/oversized-tees" },
  { id: 4, name: "Oversized Shirts", route: "/category/oversized-shirt" },

  // Shirts Collection
  { id: 5, name: "Shirts", route: "/category/shirts" },
  { id: 6, name: "Full Sleeves Shirts", route: "/category/full-sleeves-shirt" },
  { id: 7, name: "Shackets", route: "/category/shacket" },

  // T-Shirts Collection
  { id: 8, name: "T-Shirts", route: "/category/t-shirts" },
  { id: 9, name: "Regular Fit T-Shirts", route: "/category/regular-fit-t-shirt" },

  // Jeans Collection
  { id: 10, name: "Baggy Jeans", route: "/category/baggy-jeans" },
  { id: 11, name: "Fit Jeans", route: "/category/fit-jeans" },

  // Other Categories
  { id: 12, name: "Hoodies", route: "/category/hoodies" },
  { id: 13, name: "Sweatshirts", route: "/category/sweatshirt" },
  { id: 14, name: "Jackets", route: "/category/jackets" },
  { id: 15, name: "Capri", route: "/category/capri" }
];

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isShopDropdownOpen, setIsShopDropdownOpen] = useState(false);
  const { totalItems } = useCart();
  const { totalWishlistItems } = useWishlist();
  const { totalSavedOutfits } = useOutfit();
  const { isAuthenticated, user } = useAuth();
  const shopDropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      // Only handle click outside for desktop dropdown (when mobile menu is closed)
      if (!isMobileMenuOpen && shopDropdownRef.current && !shopDropdownRef.current.contains(event.target)) {
        setIsShopDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  // Close mobile menu when clicking outside and manage body scroll
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
      // Close shop dropdown when mobile menu closes
      setIsShopDropdownOpen(false);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 bg-[#1a1a1a] backdrop-blur-xl border-b border-[#2a2a2a] shadow-lg shadow-black/20 transition-all duration-300">
        <nav className="container mx-auto px-4 md:px-8 py-3 md:py-3">
          {/* Desktop & Mobile Header */}
          <div className="flex items-center justify-between">
            {/* Logo */}
            <Link to="/" className="group transition-all duration-300">
              <motion.img
                src={drawingLogo}
                alt="WOLFFOXX"
                className="h-8 md:h-10 w-auto transition-all duration-300 filter brightness-90 group-hover:brightness-100"
                whileHover={{ scale: 1.05 }}
              />
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <div ref={shopDropdownRef} className="relative group">
                <button
                  className="text-[#f5f5f5] hover:text-white transition-colors relative group flex items-center gap-1 font-medium"
                  onClick={() => setIsShopDropdownOpen(!isShopDropdownOpen)}
                >
                  Shop
                  <ChevronDown
                    size={16}
                    className={`transition-transform ${isShopDropdownOpen ? 'rotate-180' : ''}`}
                  />
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-[#f5f5f5] transition-all duration-300 group-hover:w-full"></span>
                </button>

                <AnimatePresence>
                  {isShopDropdownOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 8 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 8 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                      className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-[700px] max-w-[90vw] bg-[#1a1a1a] rounded-lg shadow-2xl shadow-black/80 overflow-hidden z-50 border border-[#2a2a2a]"
                    >
                      {/* Clean Header */}
                      <div className="px-6 py-4 border-b border-[#2a2a2a]">
                        <h3 className="text-lg font-semibold text-white">Shop Categories</h3>
                        <p className="text-[#AAAAAA] text-sm mt-1">Browse our complete collection</p>
                      </div>

                      {/* Professional Categories List */}
                      <div className="p-6">
                        <div className="grid grid-cols-3 gap-3">
                          {categories.map((category) => (
                            <Link
                              key={category.id}
                              to={category.route}
                              className="group flex items-center justify-between p-3 rounded-md border border-[#2a2a2a] hover:border-[#404040] hover:bg-[#2a2a2a] transition-all duration-200"
                              onClick={() => setIsShopDropdownOpen(false)}
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-2 h-2 rounded-full bg-[#404040] group-hover:bg-blue-500 transition-colors duration-200" />
                                <span className="text-[#AAAAAA] group-hover:text-white text-sm font-medium transition-colors duration-200">
                                  {category.name}
                                </span>
                              </div>



                              <ArrowRight size={14} className="text-[#404040] group-hover:text-[#AAAAAA] group-hover:translate-x-1 transition-all duration-200" />
                            </Link>
                          ))}
                        </div>

                        {/* Clean Bottom Action */}
                        <div className="mt-6 pt-4 border-t border-slate-700/30">
                          <Link
                            to="/collections"
                            className="group flex items-center justify-center gap-2 w-full py-3 bg-slate-800/50 hover:bg-slate-700/50 border border-slate-700/30 hover:border-slate-600/50 text-slate-300 hover:text-white font-medium rounded-md transition-all duration-200"
                            onClick={() => setIsShopDropdownOpen(false)}
                          >
                            <span>View All Collections</span>
                            <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform duration-200" />
                          </Link>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* <Link
                to="/bestsellers"
                className="text-slate-300 hover:text-white transition-colors relative group font-medium"
              >
                Best Sellers
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-slate-400 transition-all duration-300 group-hover:w-full"></span>
              </Link> */}

              <Link
                to="/track-order"
                className="text-[#f5f5f5] hover:text-white transition-colors relative group font-medium"
              >
                Track Order
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-slate-400 transition-all duration-300 group-hover:w-full"></span>
              </Link>

              <Link
                to="/about"
                className="text-[#f5f5f5] hover:text-white transition-colors relative group font-medium"
              >
                About
                <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-slate-400 transition-all duration-300 group-hover:w-full"></span>
              </Link>
            </div>

            {/* Mobile & Desktop Icons */}
            <div className="flex items-center">
              {/* Desktop Icons */}
              <div className="hidden md:flex items-center space-x-4">
                <Link to="/search">
                  <motion.button
                    className="p-2 text-slate-300 hover:text-white transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* <Search size={20} /> */}
                  </motion.button>
                </Link>

                <Link
                  to="/wishlist"
                  className="p-2 text-slate-300 hover:text-orange-400 transition-colors relative"
                >
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <Heart size={20} />
                    {totalWishlistItems > 0 && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 bg-[#FF6F35] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                      >
                        {totalWishlistItems}
                      </motion.span>
                    )}
                  </motion.div>
                </Link>

                <Link
                  to="/outfits"
                  className="p-2 text-slate-300 hover:text-orange-400 transition-colors relative"
                >
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <Shirt size={20} />
                    {totalSavedOutfits > 0 && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 bg-[#FF6F35] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                      >
                        {totalSavedOutfits}
                      </motion.span>
                    )}
                  </motion.div>
                </Link>

                <Link
                  to={isAuthenticated ? '/profile' : '/login'}
                  className="relative group"
                >
                  <motion.div 
                    className="p-2 text-slate-300 hover:text-[#f5f5f5] transition-colors flex items-center gap-2"
                    whileHover={{ scale: 1.05 }} 
                    whileTap={{ scale: 0.95 }}
                  >
                    <div className="relative">
                      {isAuthenticated ? (
                        <div className="relative">
                          <User size={20} className="group-hover:text-orange-400 transition-colors duration-300" />
                          {/* <div className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" /> */}
                        </div>
                      ) : (
                        <div className="relative overflow-hidden rounded-full">
                          <LogIn size={20} className="group-hover:text-orange-400 transition-colors duration-300" />
                          <div className="absolute inset-0 bg-gradient-to-r from-orange-500/0 to-orange-400/0 group-hover:from-orange-500/20 group-hover:to-orange-400/20 transition-all duration-300" />
                        </div>
                      )}
                    </div>
                    <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-orange-500 group-hover:w-full transition-all duration-300" />
                  </motion.div>
                </Link>

                <Link
                  to="/cart"
                  className="p-2 text-slate-300 hover:text-[#f5f5f5] transition-colors relative"
                >
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <ShoppingBag size={20} />
                    {totalItems > 0 && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 bg-[#FF6F35] text-[#f5f5f5] text-xs rounded-full w-5 h-5 flex items-center justify-center"
                      >
                        {totalItems}
                      </motion.span>
                    )}
                  </motion.div>
                </Link>
              </div>

              {/* Mobile Icons - Search, Cart and Menu */}
              <div className="md:hidden flex items-center space-x-2">
                <Link to="/search">
                  <motion.button
                    className="p-2 text-slate-300 hover:text-white transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* <Search size={20} /> */}
                  </motion.button>
                </Link>

                <Link
                  to="/cart"
                  className="p-2 text-[#9a9a9a] hover:text-[#f5f5f5] transition-colors relative"
                >
                  <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
                    <ShoppingBag size={22} />
                    {totalItems > 0 && (
                      <motion.span
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 -right-1 bg-[#FF6F35] text-[#f5f5f5] text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium"
                      >
                        {totalItems}
                      </motion.span>
                    )}
                  </motion.div>
                </Link>

                <motion.button
                  className="p-2 text-[#9a9a9a] hover:text-[#f5f5f5] transition-colors"
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
                </motion.button>
              </div>
            </div>
          </div>
        </nav>
      </header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[60] md:hidden"
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Mobile Menu */}
            <motion.div
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'tween', duration: 0.3 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-[#0a0a0a]/95 backdrop-blur-xl border-l border-[#2a2a2a] z-[70] md:hidden overflow-y-auto"
            >
              {/* Mobile Menu Header */}
              <div className="flex items-center justify-between p-6 border-b border-[#2a2a2a]">
                <Link
                  to="/"
                  className="transition-all duration-300"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <img
                    src={drawingLogo}
                    alt="WOLFFOXX"
                    className="h-8 w-auto filter brightness-90"
                  />
                </Link>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 text-[#6a6a6a] hover:text-[#f5f5f5] transition-colors"
                >
                  <X size={24} />
                </button>
              </div>

              {/* Mobile Menu Content */}
              <div className="p-6 space-y-6">
                {/* User Section */}
                <div className="pb-6 border-b border-[#2a2a2a]">
                  <Link
                    to={isAuthenticated ? '/profile' : '/login'}
                    className="relative group flex items-center gap-4 p-4 rounded-xl transition-all duration-300 bg-[#0a0a0a] hover:bg-[#1a1a1a] border border-[#2a2a2a] hover:border-[#FF8800]/30"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {/* Profile Avatar */}
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#0a0a0a] border border-[#3a3a3a]">
                      {isAuthenticated ? (
                        user && user.first_name && user.last_name ? (
                          <span className="text-[#FF8800] font-semibold text-lg">
                            {user.first_name.charAt(0)}{user.last_name.charAt(0)}
                          </span>
                        ) : (
                          <User size={20} className="text-[#FF8800]" />
                        )
                      ) : (
                        <LogIn size={20} className="text-[#FF8800]" />
                      )}
                    </div>

                    {/* Text content */}
                    <div className="flex-1">
                      <div className="text-white font-medium text-base">
                        {isAuthenticated ? (user ? `${user.first_name} ${user.last_name}` : 'My Account') : 'Sign In'}
                      </div>
                      <div className="text-[#AAAAAA] text-sm">
                        {isAuthenticated ? 'View your profile & orders' : 'Access exclusive features'}
                      </div>
                    </div>
                  </Link>
                </div>

                {/* Navigation Links */}
                <div className="space-y-2">
                  <Link
                    to="/"
                    className="flex items-center gap-4 p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Home size={20} />
                    <span className="font-medium">Home</span>
                  </Link>

                  {/* Shop Dropdown */}
                  <div className="space-y-2">
                    <button
                      className="flex items-center justify-between w-full p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                      onClick={() => setIsShopDropdownOpen(!isShopDropdownOpen)}
                    >
                      <div className="flex items-center gap-4">
                        <ShoppingBag size={20} />
                        <span className="font-medium">Shop</span>
                      </div>
                      <ChevronDown
                        size={16}
                        className={`transition-transform ${isShopDropdownOpen ? 'rotate-180' : ''}`}
                      />
                    </button>

                    <AnimatePresence>
                      {isShopDropdownOpen && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          className="overflow-hidden"
                        >
                          {/* Mobile Professional Categories */}
                          <div className="px-4 py-3 bg-[#1a1a1a] rounded-lg mx-4 mb-2">
                            <div className="space-y-2">
                              {categories.map((category) => (
                                <Link
                                  key={category.id}
                                  to={category.route}
                                  className="group flex items-center justify-between p-3 rounded-md border border-[#2a2a2a] hover:border-[#404040] hover:bg-[#2a2a2a] transition-all duration-200"
                                  onClick={() => {
                                    setIsMobileMenuOpen(false);
                                    setIsShopDropdownOpen(false);
                                  }}
                                >
                                  <div className="flex items-center gap-3">
                                    <div className="w-2 h-2 rounded-full bg-[#6a6a6a] group-hover:bg-[#FF8800] transition-colors duration-200" />
                                    <span className="text-[#AAAAAA] group-hover:text-white text-sm font-medium transition-colors duration-200">
                                      {category.name}
                                    </span>
                                  </div>



                                  <ArrowRight size={14} className="text-[#6a6a6a] group-hover:text-[#AAAAAA] group-hover:translate-x-1 transition-all duration-200" />
                                </Link>
                              ))}
                            </div>

                            {/* Mobile Clean Bottom Action */}
                            <div className="mt-4 pt-3 border-t border-[#2a2a2a]">
                              <Link
                                to="/collections"
                                className="group flex items-center justify-center gap-2 w-full py-3 bg-[#1a1a1a] hover:bg-[#2a2a2a] border border-[#2a2a2a] hover:border-[#FF8800]/30 text-[#AAAAAA] hover:text-white font-medium rounded-md transition-all duration-200"
                                onClick={() => {
                                  setIsMobileMenuOpen(false);
                                  setIsShopDropdownOpen(false);
                                }}
                              >
                                <span className="text-sm">View All Collections</span>
                                <ArrowRight size={14} className="group-hover:translate-x-1 transition-transform duration-200" />
                              </Link>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>

                  {/* <Link
                    to="/bestsellers"
                    className="flex items-center gap-4 p-4 text-slate-300 hover:text-white hover:bg-slate-800/50 rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Star size={20} />
                    <span className="font-medium">Best Sellers</span>
                  </Link> */}

                  <Link
                    to="/wishlist"
                    className="flex items-center gap-4 p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <div className="relative">
                      <Heart size={20} />
                      {totalWishlistItems > 0 && (
                        <span className="absolute -top-2 -right-2 bg-[#FF6F35] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {totalWishlistItems}
                        </span>
                      )}
                    </div>
                    <span className="font-medium">Wishlist</span>
                    {/*{totalWishlistItems > 0 && (*/}
                    {/*  <span className="text-sm text-[#6a6a6a]">({totalWishlistItems})</span>*/}
                    {/*)}*/}
                  </Link>

                  <Link
                    to="/outfits"
                    className="flex items-center gap-4 p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <div className="relative">
                      <Shirt size={20} />
                      {totalSavedOutfits > 0 && (
                        <span className="absolute -top-2 -right-2 bg-[#FF6F35] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                          {totalSavedOutfits}
                        </span>
                      )}
                    </div>
                    <span className="font-medium">My Outfits</span>
                  </Link>

                  <Link
                    to="/track-order"
                    className="flex items-center gap-4 p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Package size={20} />
                    <span className="font-medium">Track Order</span>
                  </Link>

                  <Link
                    to="/about"
                    className="flex items-center gap-4 p-4 text-[#f5f5f5] hover:text-white hover:bg-[#2a2a2a] rounded-xl transition-colors"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    <Info size={20} />
                    <span className="font-medium">About</span>
                  </Link>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      <SearchBar isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} />
    </>
  );
}