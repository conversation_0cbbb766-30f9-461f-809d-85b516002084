import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown } from 'lucide-react';

export default function SizeChart() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mb-6">
      <button
        className="w-full flex items-center justify-between py-3 px-4 bg-gray-800 rounded-md hover:bg-gray-700 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="font-medium text-white">Size Chart</span>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <ChevronDown size={18} />
        </motion.div>
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="p-4 bg-gray-800/50 rounded-b-md mt-1">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-700">
                      <th className="py-2 px-4 text-left text-gray-400">Size</th>
                      <th className="py-2 px-4 text-left text-gray-400">Chest (in)</th>
                      <th className="py-2 px-4 text-left text-gray-400">Length (in)</th>
                      <th className="py-2 px-4 text-left text-gray-400">Shoulder (in)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr className="border-b border-gray-700">
                      <td className="py-2 px-4 text-white">M</td>
                      <td className="py-2 px-4 text-gray-300">42-44</td>
                      <td className="py-2 px-4 text-gray-300">27.5-28</td>
                      <td className="py-2 px-4 text-gray-300">19-20</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="py-2 px-4 text-white">L</td>
                      <td className="py-2 px-4 text-gray-300">44-46</td>
                      <td className="py-2 px-4 text-gray-300">28.5-29</td>
                      <td className="py-2 px-4 text-gray-300">20-21</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="py-2 px-4 text-white">XL</td>
                      <td className="py-2 px-4 text-gray-300">46-48</td>
                      <td className="py-2 px-4 text-gray-300">29.5-30</td>
                      <td className="py-2 px-4 text-gray-300">21-22</td>
                    </tr>
                    <tr className="border-b border-gray-700">
                      <td className="py-2 px-4 text-white">XXL</td>
                      <td className="py-2 px-4 text-gray-300">48-50</td>
                      <td className="py-2 px-4 text-gray-300">30.5-31</td>
                      <td className="py-2 px-4 text-gray-300">22-23</td>
                    </tr>
                    <tr>
                      <td className="py-2 px-4 text-white">XXXL</td>
                      <td className="py-2 px-4 text-gray-300">50-52</td>
                      <td className="py-2 px-4 text-gray-300">31.5-32</td>
                      <td className="py-2 px-4 text-gray-300">23-24</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <p className="mt-3 text-xs text-gray-400">
                Note: These are general guidelines. Oversized fit is deliberately cut larger than standard sizes for the street style look.
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}