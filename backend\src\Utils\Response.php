<?php

namespace Wolffoxx\Utils;

/**
 * HTTP Response Utility
 *
 * Provides standardized JSON responses with proper
 * HTTP status codes and headers.
 */
class Response
{
    /**
     * Send successful JSON response
     */
    public static function success($data = null, int $statusCode = 200, array $headers = []): void
    {
        self::sendJson([
            'success' => true,
            'data' => $data,
            'timestamp' => date('c')
        ], $statusCode, $headers);
    }

    /**
     * Send error JSON response
     */
    public static function error($message, int $statusCode = 400, array $headers = []): void
    {
        $response = [
            'success' => false,
            'timestamp' => date('c')
        ];

        if (is_string($message)) {
            $response['error'] = $message;
        } elseif (is_array($message)) {
            $response = array_merge($response, $message);
        }

        self::sendJson($response, $statusCode, $headers);
    }

    /**
     * Send paginated response
     */
    public static function paginated(array $data, int $total, int $page, int $perPage, array $meta = []): void
    {
        $totalPages = ceil($total / $perPage);

        $response = [
            'success' => true,
            'data' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ],
            'timestamp' => date('c')
        ];

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        self::sendJson($response, 200, [
            'X-Total-Count' => $total,
            'X-Page-Count' => $totalPages
        ]);
    }

    /**
     * Send created response (201)
     */
    public static function created($data = null, array $headers = []): void
    {
        self::success($data, 201, $headers);
    }

    /**
     * Send no content response (204)
     */
    public static function noContent(array $headers = []): void
    {
        self::sendResponse('', 204, $headers);
    }

    /**
     * Send unauthorized response (401)
     */
    public static function unauthorized(string $message = 'Unauthorized'): void
    {
        self::error($message, 401);
    }

    /**
     * Send forbidden response (403)
     */
    public static function forbidden(string $message = 'Forbidden'): void
    {
        self::error($message, 403);
    }

    /**
     * Send not found response (404)
     */
    public static function notFound(string $message = 'Not found'): void
    {
        self::error($message, 404);
    }

    /**
     * Send validation error response (422)
     */
    public static function validationError(array $errors, string $message = 'Validation failed'): void
    {
        self::error([
            'error' => $message,
            'validation_errors' => $errors
        ], 422);
    }

    /**
     * Send rate limit exceeded response (429)
     */
    public static function rateLimitExceeded(string $message = 'Rate limit exceeded', array $headers = []): void
    {
        self::error($message, 429, $headers);
    }

    /**
     * Send internal server error response (500)
     */
    public static function serverError(string $message = 'Internal server error'): void
    {
        self::error($message, 500);
    }

    /**
     * Send JSON response
     */
    private static function sendJson(array $data, int $statusCode = 200, array $headers = []): void
    {
        $json = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

        if ($json === false) {
            $json = json_encode([
                'success' => false,
                'error' => 'JSON encoding failed',
                'timestamp' => date('c')
            ]);
            $statusCode = 500;
        }

        $headers['Content-Type'] = 'application/json; charset=utf-8';

        self::sendResponse($json, $statusCode, $headers);
    }

    /**
     * Send raw response
     */
    private static function sendResponse(string $content, int $statusCode = 200, array $headers = []): void
    {
        // Set HTTP status code
        http_response_code($statusCode);

        // Set default headers
        $defaultHeaders = [
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'DENY',
            'X-XSS-Protection' => '1; mode=block'
        ];

        // Merge with custom headers
        $allHeaders = array_merge($defaultHeaders, $headers);

        // Send headers
        foreach ($allHeaders as $name => $value) {
            header("{$name}: {$value}");
        }

        // Send content
        echo $content;

        // Response sent successfully

        exit;
    }

    /**
     * Get HTTP status text
     */
    public static function getStatusText(int $statusCode): string
    {
        $statusTexts = [
            200 => 'OK',
            201 => 'Created',
            204 => 'No Content',
            400 => 'Bad Request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Not Found',
            422 => 'Unprocessable Entity',
            429 => 'Too Many Requests',
            500 => 'Internal Server Error'
        ];

        return $statusTexts[$statusCode] ?? 'Unknown Status';
    }

    /**
     * Send file download response
     */
    public static function download(string $filePath, string $filename = null, string $mimeType = null): void
    {
        if (!file_exists($filePath)) {
            self::notFound('File not found');
            return;
        }

        $filename = $filename ?: basename($filePath);
        $mimeType = $mimeType ?: mime_content_type($filePath) ?: 'application/octet-stream';
        $fileSize = filesize($filePath);

        $headers = [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Content-Length' => $fileSize,
            'Cache-Control' => 'private, max-age=0, must-revalidate',
            'Pragma' => 'public'
        ];

        foreach ($headers as $name => $value) {
            header("{$name}: {$value}");
        }

        // Output file content
        readfile($filePath);
        exit;
    }

    /**
     * Send streaming response for large files
     */
    public static function stream(string $filePath, string $filename = null, string $mimeType = null): void
    {
        if (!file_exists($filePath)) {
            self::notFound('File not found');
            return;
        }

        $filename = $filename ?: basename($filePath);
        $mimeType = $mimeType ?: mime_content_type($filePath) ?: 'application/octet-stream';
        $fileSize = filesize($filePath);

        $headers = [
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
            'Content-Length' => $fileSize,
            'Accept-Ranges' => 'bytes',
            'Cache-Control' => 'public, max-age=3600'
        ];

        foreach ($headers as $name => $value) {
            header("{$name}: {$value}");
        }

        // Handle range requests for video/audio streaming
        if (isset($_SERVER['HTTP_RANGE'])) {
            self::handleRangeRequest($filePath, $fileSize);
        } else {
            readfile($filePath);
        }

        exit;
    }

    /**
     * Handle HTTP range requests for streaming
     */
    private static function handleRangeRequest(string $filePath, int $fileSize): void
    {
        $range = $_SERVER['HTTP_RANGE'];

        if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
            $start = intval($matches[1]);
            $end = $matches[2] ? intval($matches[2]) : $fileSize - 1;

            $length = $end - $start + 1;

            http_response_code(206);
            header("Content-Range: bytes {$start}-{$end}/{$fileSize}");
            header("Content-Length: {$length}");

            $file = fopen($filePath, 'rb');
            fseek($file, $start);

            $buffer = 8192;
            while (!feof($file) && ($pos = ftell($file)) <= $end) {
                if ($pos + $buffer > $end) {
                    $buffer = $end - $pos + 1;
                }
                echo fread($file, $buffer);
                flush();
            }

            fclose($file);
        }
    }
}
