<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * UserAddress Model
 *
 * Handles user shipping and billing addresses
 */
class UserAddress extends BaseModel
{
    protected string $table = 'user_addresses'; // Make sure this matches the actual table name in the database

    protected array $fillable = [
        'user_id',
        'type',
        'is_default',
        'first_name',
        'last_name',
        'company',
        'address_line_1',
        'address_line_2',
        'city',
        'state',
        'postal_code',
        'country',
        'phone'
    ];

    /**
     * Get all addresses for a user
     */
    public function getUserAddresses(int $userId): array
    {
        try {
            $sql = "SELECT * FROM {$this->table} WHERE user_id = ? ORDER BY is_default DESC, created_at DESC";
            $statement = Database::execute($sql, [$userId]);
            $results = $statement->fetchAll();

            return array_map([$this, 'processResult'], $results);
        } catch (\Exception $e) {
            error_log('Get user addresses failed: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get default address for a user
     */
    public function getDefaultAddress(int $userId, string $type = 'shipping'): ?array
    {
        try {
            $sql = "SELECT * FROM {$this->table} 
                    WHERE user_id = ? AND (type = ? OR type = 'both') AND is_default = 1 
                    LIMIT 1";
            $statement = Database::execute($sql, [$userId, $type]);
            $result = $statement->fetch();

            return $result ? $this->processResult($result) : null;
        } catch (\Exception $e) {
            error_log('Get default address failed: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Add a new address for a user
     */
    public function addAddress(array $addressData): ?array
    {
        try {
            // Log the input data for debugging
            error_log('Adding address with data: ' . json_encode($addressData));
            
            // Validate required fields
            $requiredFields = ['user_id', 'first_name', 'last_name', 'address_line_1', 'city', 'state', 'postal_code', 'country'];
            foreach ($requiredFields as $field) {
                if (empty($addressData[$field])) {
                    error_log("Missing required field for address: {$field}");
                    throw new \InvalidArgumentException("Missing required field: {$field}");
                }
            }
            
            $this->beginTransaction();

            // If this is set as default, unset any existing default of the same type
            if (!empty($addressData['is_default']) && $addressData['is_default']) {
                $type = $addressData['type'] ?? 'shipping';
                $this->unsetDefaultAddress($addressData['user_id'], $type);
            }

            // Log before creating the address
            error_log('Creating address in database...');
            
            $address = $this->create($addressData);
            
            // Log after creating the address
            error_log('Address created with ID: ' . ($address['id'] ?? 'unknown'));

            // Update the user's address field in the users table for backward compatibility
            if ($address && !empty($addressData['is_default']) && $addressData['is_default']) {
                $this->updateUserAddressField($addressData['user_id'], $address);
            }

            $this->commit();
            error_log('Transaction committed successfully');
            
            return $address;
        } catch (\Exception $e) {
            $this->rollback();
            error_log('Add address failed: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * Update an existing address
     */
    public function updateAddress(int $addressId, array $addressData): ?array
    {
        try {
            $this->beginTransaction();

            // Get the address to check ownership
            $address = $this->findById($addressId);
            if (!$address) {
                $this->rollback();
                return null;
            }

            // If this is set as default, unset any existing default of the same type
            if (!empty($addressData['is_default']) && $addressData['is_default']) {
                $type = $addressData['type'] ?? $address['type'];
                $this->unsetDefaultAddress($address['user_id'], $type);
            }

            $updatedAddress = $this->update($addressId, $addressData);

            // Update the user's address field in the users table for backward compatibility
            if ($updatedAddress && !empty($addressData['is_default']) && $addressData['is_default']) {
                $this->updateUserAddressField($address['user_id'], $updatedAddress);
            }

            $this->commit();
            return $updatedAddress;
        } catch (\Exception $e) {
            $this->rollback();
            error_log('Update address failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete an address
     */
    public function deleteAddress(int $addressId, int $userId): bool
    {
        try {
            $this->beginTransaction();

            // Check if address belongs to user
            $address = $this->findById($addressId);
            if (!$address || $address['user_id'] !== $userId) {
                $this->rollback();
                return false;
            }

            // If this was a default address, we need to set another one as default
            $wasDefault = !empty($address['is_default']) && $address['is_default'];
            $type = $address['type'];

            $deleted = $this->delete($addressId);

            // If the deleted address was default, set another one as default
            if ($deleted && $wasDefault) {
                $this->setNewDefaultAddress($userId, $type);
            }

            $this->commit();
            return $deleted;
        } catch (\Exception $e) {
            $this->rollback();
            error_log('Delete address failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Unset default address of a specific type
     */
    private function unsetDefaultAddress(int $userId, string $type): void
    {
        try {
            $sql = "UPDATE {$this->table} 
                    SET is_default = 0 
                    WHERE user_id = ? AND (type = ? OR type = 'both')";
            Database::execute($sql, [$userId, $type]);
        } catch (\Exception $e) {
            error_log('Unset default address failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Set a new default address after deleting the previous default
     */
    private function setNewDefaultAddress(int $userId, string $type): void
    {
        try {
            // Find the most recently created address of the same type
            $sql = "SELECT id FROM {$this->table} 
                    WHERE user_id = ? AND (type = ? OR type = 'both') 
                    ORDER BY created_at DESC LIMIT 1";
            $statement = Database::execute($sql, [$userId, $type]);
            $result = $statement->fetch();

            if ($result) {
                $sql = "UPDATE {$this->table} SET is_default = 1 WHERE id = ?";
                Database::execute($sql, [$result['id']]);

                // Update the user's address field
                $address = $this->findById($result['id']);
                if ($address) {
                    $this->updateUserAddressField($userId, $address);
                }
            }
        } catch (\Exception $e) {
            error_log('Set new default address failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Update the address field in the users table for backward compatibility
     */
    private function updateUserAddressField(int $userId, array $address): void
    {
        try {
            $formattedAddress = $this->formatAddressForUser($address);
            
            $sql = "UPDATE users SET address = ? WHERE id = ?";
            Database::execute($sql, [$formattedAddress, $userId]);
        } catch (\Exception $e) {
            error_log('Update user address field failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Format address for storage in the users table
     */
    private function formatAddressForUser(array $address): string
    {
        $parts = [];
        
        if (!empty($address['address_line_1'])) {
            $parts[] = $address['address_line_1'];
        }
        
        if (!empty($address['address_line_2'])) {
            $parts[] = $address['address_line_2'];
        }
        
        $cityStateZip = [];
        if (!empty($address['city'])) {
            $cityStateZip[] = $address['city'];
        }
        
        if (!empty($address['state'])) {
            $cityStateZip[] = $address['state'];
        }
        
        if (!empty($address['postal_code'])) {
            $cityStateZip[] = $address['postal_code'];
        }
        
        if (!empty($cityStateZip)) {
            $parts[] = implode(', ', $cityStateZip);
        }
        
        if (!empty($address['country'])) {
            $parts[] = $address['country'];
        }
        
        return implode(', ', $parts);
    }
}