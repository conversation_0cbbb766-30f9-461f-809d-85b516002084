import { Star } from 'lucide-react';
import { getAverageRating, getProductReviews } from '../data/reviews';

export default function ProductRating({ productId, size = 'md', showCount = true }) {
  const averageRating = getAverageRating(productId);
  const reviews = getProductReviews(productId);
  const reviewCount = reviews.length;

  // Define size values
  const starSizes = {
    sm: 12,
    md: 16,
    lg: 20,
  };

  const textSizes = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  if (reviewCount === 0) return null;

  return (
    <div className="flex items-center gap-1">
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={starSizes[size]}
            className={`${
              averageRating >= star
                ? 'fill-amber-400 text-amber-400'
                : averageRating >= star - 0.5
                ? 'fill-amber-400/50 text-amber-400'
                : 'fill-gray-600 text-gray-600'
            }`}
          />
        ))}
      </div>
      {showCount && (
        <>
          <span className={`${textSizes[size]} text-white font-medium ml-2`}>
            {averageRating}
          </span>
          <span className={`${textSizes[size]} text-gray-400`}>({reviewCount})</span>
        </>
      )}
    </div>
  );
}