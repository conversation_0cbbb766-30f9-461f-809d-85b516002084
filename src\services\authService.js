// Auth Service - Helper functions for authentication

/**
 * Get the authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
export const getAuthToken = () => {
  try {
    const tokens = localStorage.getItem('wolffoxx_tokens');
    console.log('🔑 getAuthToken - Raw tokens from localStorage:', tokens);

    if (tokens) {
      const parsedTokens = JSON.parse(tokens);
      console.log('🔑 getAuthToken - Parsed tokens:', parsedTokens);

      // Backend returns access_token, not accessToken
      const token = parsedTokens.access_token || parsedTokens.accessToken;
      console.log('🔑 getAuthToken - Final token:', token ? token.substring(0, 20) + '...' : 'NO TOKEN');
      return token;
    }

    console.log('🔑 getAuthToken - No tokens in localStorage');
    return null;
  } catch (error) {
    console.error('🔑 getAuthToken - Error getting auth token:', error);
    return null;
  }
};