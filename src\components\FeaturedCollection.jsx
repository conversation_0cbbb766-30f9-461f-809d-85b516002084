import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { <PERSON>R<PERSON>, ChevronLeft, ChevronRight, Heart, ShoppingBag, Eye } from 'lucide-react';
import { products } from '../data/products';
import ColorPreviewSlider from './ColorPreviewSlider';
import WishlistButton from './WishlistButton';
import { getOptimizedImageUrl } from '../services/imageOptimization';

export default function FeaturedCollection() {
  const featuredProducts = products.filter((p) => p.isBestSeller || p.isNew).slice(0, 6);
  const [currentScrollIndex, setCurrentScrollIndex] = useState(0);
  const scrollContainerRef = useRef(null);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const scrollLeft = container.scrollLeft;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // 4 * 4px (gap-4)
      const totalItemWidth = itemWidth + gap;
      const index = Math.round(scrollLeft / totalItemWidth);
      setCurrentScrollIndex(Math.min(index, featuredProducts.length - 1));
    }
  };

  const scrollToItem = (index) => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current;
      const itemWidth = container.children[0]?.offsetWidth || 0;
      const gap = 16; // gap-4
      const totalItemWidth = itemWidth + gap;
      const scrollLeft = index * totalItemWidth;
      container.scrollTo({ left: scrollLeft, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  return (
    <section className="py-12 md:py-16 bg-black">
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 md:mb-10 gap-4">
          <div>
            <motion.h2
              className="text-3xl md:text-4xl font-['Bebas_Neue',sans-serif] tracking-wider text-[#f5f5f5] mb-2"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              FEATURED DROPS
            </motion.h2>
            <motion.p
              className="text-[#9a9a9a] text-sm md:text-base"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              Limited edition oversized pieces from our latest collection
            </motion.p>
          </div>
          <Link
            to="/collections"
            className="text-[#9a9a9a] hover:text-[#d4d4d4] font-medium flex items-center gap-1 group text-sm md:text-base"
          >
            View All Collections <ArrowRight size={16} className="group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>

        {/* Mobile: Horizontal Scroller */}
        <div className="block sm:hidden">
          <div className="relative">
            <div
              ref={scrollContainerRef}
              className="flex gap-4 overflow-x-auto scrollbar-hide pb-4 px-1 scroll-smooth pr-4"
            >
              {featuredProducts.map((product, index) => (
                <div key={product.id} className="flex-none w-64 mobile-scroller-item">
                  <FeaturedProductCard product={product} index={index} />
                </div>
              ))}
            </div>

            {/* Right Gradient Overlay only - to show there are more cards */}
            <div className="absolute right-0 top-0 bottom-4 w-6 bg-gradient-to-l from-black to-transparent pointer-events-none z-10" />

            {/* Working Scroll indicator */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 flex gap-1">
              {featuredProducts.map((_, index) => (
                <button
                  key={index}
                  onClick={() => scrollToItem(index)}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-300 cursor-pointer scroll-indicator ${
                    index === currentScrollIndex
                      ? 'bg-[#f5f5f5] scale-125 active'
                      : 'bg-[#6a6a6a] hover:bg-[#9a9a9a]'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* Tablet and Desktop: Grid Layout */}
        <div className="hidden sm:grid sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
          {featuredProducts.map((product, index) => (
            <FeaturedProductCard key={product.id} product={product} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
}

function FeaturedProductCard({ product, index }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const handleColorSelect = (colorIndex) => {
    setSelectedColorIndex(colorIndex);
    // Reset to first image when color changes
    setCurrentImageIndex(0);
  };

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === currentImages.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === 0 ? currentImages.length - 1 : prev - 1
    );
  };

  const goToImage = (imageIndex, e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex(imageIndex);
  };



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] rounded-xl overflow-hidden group border border-[#2a2a2a]"
    >
      <Link to={`/product/${product.id}`} className="block relative">
        {/* Image Carousel Container */}
        <div className="h-72 md:h-80 overflow-hidden relative">
          <div className="relative w-full h-full">
            {currentImages.map((image, idx) => (
              <img
                key={idx}
                src={getOptimizedImageUrl(image, {
                  context: 'card',
                  width: 400,
                  height: 500,
                  quality: 'auto:good'
                })}
                alt={`${product.name} - ${product.colors[selectedColorIndex]?.name || 'Default'} - Image ${idx + 1}`}
                loading="lazy"
                className={`absolute inset-0 w-full h-full object-cover transform group-hover:scale-105 transition-all duration-700 ease-in-out ${
                  idx === currentImageIndex
                    ? 'opacity-100 z-10'
                    : 'opacity-0 z-0'
                }`}
                onError={(e) => {
                  e.target.src = '/api/placeholder/400/400';
                }}
              />
            ))}
          </div>

          {/* Navigation Arrows - Always visible */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={nextImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/50 hover:bg-black/70 text-white rounded-full flex items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}

          {/* Image Dots Indicator */}
          {currentImages.length > 1 && (
            <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
              {currentImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => goToImage(idx, e)}
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex
                      ? 'bg-white scale-110'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
          {product.isNew && (
            <div className="bg-[#6a6a6a] text-[#f5f5f5] text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              NEW
            </div>
          )}
          {product.is_sale === 1 && (
            <div className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-medium px-2 py-1 rounded-md shadow-lg">
              SALE
            </div>
          )}
        </div>

        {/* Image Counter */}
        {currentImages.length > 1 && (
          <div className="absolute top-3 right-12 bg-black/50 text-[#f5f5f5] text-xs px-2 py-1 rounded-full backdrop-blur-sm">
            {currentImageIndex + 1}/{currentImages.length}
          </div>
        )}

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 z-10">
          <WishlistButton
            productId={product.id}
            productName={product.name}
            productPrice={product.is_sale === 1 ? product.sale_price : product.price}
            productImage={currentImages[0] || product.images?.[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] text-[#d4d4d4] w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
          />
        </div>

        {/* Quick Actions Overlay */}
        {/* <AnimatedInfo product={product} isVisible={isHovered} /> */}
      </Link>

      {/* Product Info */}
      <div className="p-4 md:p-5">
        <div className="mb-3">
          <span className="text-[#6a6a6a] text-xs font-medium uppercase tracking-wide">
            {product.category}
          </span>
          <h3 className="text-[#d4d4d4] font-semibold text-lg hover:text-[#9a9a9a] transition-colors mt-1 line-clamp-1 truncate">
            <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
          </h3>
        </div>

        {/* Price */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {product.is_sale === 1 && product.sale_price ? (
              <>
                <span className="text-[#f5f5f5] font-bold text-lg">${product.sale_price}</span>
                <span className="text-[#6a6a6a] line-through text-sm">${product.price}</span>
                <span className="text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded-full font-medium">
                  {Math.round(((product.price - product.sale_price) / product.price) * 100)}% OFF
                </span>
              </>
            ) : (
              <span className="text-[#f5f5f5] font-bold text-lg">${product.price}</span>
            )}
          </div>
        </div>

        {/* Color Selection */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex gap-1.5">
              {product.colors.slice(0, 4).map((color, idx) => (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleColorSelect(idx);
                  }}
                  className={`w-5 h-5 rounded-full border-2 transition-all duration-200 ${
                    selectedColorIndex === idx
                      ? 'border-[#f5f5f5] scale-110 shadow-lg shadow-white/25'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
              {product.colors.length > 4 && (
                <div className="w-5 h-5 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                  +{product.colors.length - 4}
                </div>
              )}
            </div>
          </div>

          {/* Stock Status */}
          <div className="text-xs text-[#6a6a6a]">
            {product.stock >0 ? (
              <span className="text-[#9a9a9a]">● Out of Stock</span>
            ) : (
              <span className="text-[#9a9a9a]">● In Stock</span>
            )}
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// function AnimatedInfo({ product, isVisible }) {
//   return (
//     <motion.div
//       initial={false}
//       animate={{
//         opacity: isVisible ? 1 : 0,
//         y: isVisible ? 0 : 20,
//       }}
//       transition={{ duration: 0.2 }}
//       className="absolute inset-x-0 bottom-0 p-4 md:p-5 bg-gradient-to-t from-gray-900/95 via-gray-900/80 to-transparent backdrop-blur-sm"
//     >
//       <div className="flex items-center justify-between gap-3">
//         <div className="flex-1">
//           <div className="text-white font-medium text-sm">{product.name}</div>
//           <div className="text-gray-300 text-xs">{product.category}</div>
//         </div>

//         <div className="flex gap-2">
//           <Link
//             to={`/product/${product.id}`}
//             className="bg-white/10 hover:bg-white/20 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-all duration-200 backdrop-blur-sm border border-white/20"
//           >
//             <Eye size={12} />
//             Quick View
//           </Link>
//           <button className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-colors">
//             <ShoppingBag size={12} />
//             Add to Cart
//           </button>
//         </div>
//       </div>
//     </motion.div>
//   );
// }