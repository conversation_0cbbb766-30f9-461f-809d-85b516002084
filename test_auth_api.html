<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9; }
        .error { background: #ffebee; border-color: #f44336; }
        .success { background: #e8f5e8; border-color: #4caf50; }
        button { margin: 5px; padding: 10px 15px; }
        input { margin: 5px; padding: 8px; width: 200px; }
    </style>
</head>
<body>
    <h1>Authentication API Test</h1>
    
    <div>
        <h3>1. Login with OTP</h3>
        <input type="text" id="phone" placeholder="Phone number" value="9876543210">
        <button onclick="sendOTP()">Send OTP</button>
        <br>
        <input type="text" id="otp" placeholder="OTP Code" value="123456">
        <button onclick="verifyOTP()">Verify OTP</button>
        <div id="login-result" class="result"></div>
    </div>

    <div>
        <h3>2. Test Protected Endpoint (Cart)</h3>
        <button onclick="testCart()">Get Cart</button>
        <div id="cart-result" class="result"></div>
    </div>

    <div>
        <h3>3. Test Order Creation</h3>
        <button onclick="testOrder()">Create Test Order</button>
        <div id="order-result" class="result"></div>
    </div>

    <div>
        <h3>4. Test Outfit Creation</h3>
        <button onclick="testOutfit()">Create Test Outfit</button>
        <div id="outfit-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = localStorage.getItem('authToken');

        function displayResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = `result ${isError ? 'error' : 'success'}`;
            element.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }

        async function sendOTP() {
            try {
                const phone = document.getElementById('phone').value;
                const response = await fetch(`${API_BASE}/auth/send-otp`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ phone })
                });
                const data = await response.json();
                displayResult('login-result', data, !response.ok);
            } catch (error) {
                displayResult('login-result', { error: error.message }, true);
            }
        }

        async function verifyOTP() {
            try {
                const phone = document.getElementById('phone').value;
                const otp = document.getElementById('otp').value;
                const response = await fetch(`${API_BASE}/auth/verify-otp`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ phone, otp })
                });
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    authToken = data.access_token;
                    localStorage.setItem('authToken', authToken);
                    displayResult('login-result', { ...data, message: 'Login successful! Token saved.' }, false);
                } else {
                    displayResult('login-result', data, true);
                }
            } catch (error) {
                displayResult('login-result', { error: error.message }, true);
            }
        }

        async function testCart() {
            try {
                if (!authToken) {
                    displayResult('cart-result', { error: 'Please login first' }, true);
                    return;
                }

                const response = await fetch(`${API_BASE}/cart`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });
                const data = await response.json();
                displayResult('cart-result', data, !response.ok);
            } catch (error) {
                displayResult('cart-result', { error: error.message }, true);
            }
        }

        async function testOrder() {
            try {
                if (!authToken) {
                    displayResult('order-result', { error: 'Please login first' }, true);
                    return;
                }

                const orderData = {
                    customer_name: 'Test Customer',
                    shipping_address: {
                        street: '123 Test Street',
                        city: 'Test City',
                        state: 'Test State',
                        zip: '12345',
                        country: 'India'
                    },
                    billing_address: {
                        street: '123 Test Street',
                        city: 'Test City',
                        state: 'Test State',
                        zip: '12345',
                        country: 'India'
                    },
                    payment_method: 'test_payment',
                    shipping_amount: 0,
                    order_notes: 'Test order'
                };

                const response = await fetch(`${API_BASE}/orders`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(orderData)
                });
                const data = await response.json();
                displayResult('order-result', data, !response.ok);
            } catch (error) {
                displayResult('order-result', { error: error.message }, true);
            }
        }

        async function testOutfit() {
            try {
                if (!authToken) {
                    displayResult('outfit-result', { error: 'Please login first' }, true);
                    return;
                }

                const outfitData = {
                    name: 'Test Outfit',
                    description: 'Test outfit description',
                    occasion: 'casual',
                    season: 'all',
                    is_public: false,
                    items: [
                        {
                            product_id: 1,
                            selected_color: 'Black',
                            selected_size: 'M',
                            category_type: 'top',
                            is_primary: false
                        }
                    ]
                };

                const response = await fetch(`${API_BASE}/outfits`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify(outfitData)
                });
                const data = await response.json();
                displayResult('outfit-result', data, !response.ok);
            } catch (error) {
                displayResult('outfit-result', { error: error.message }, true);
            }
        }

        // Display current token status
        if (authToken) {
            document.body.insertAdjacentHTML('afterbegin', 
                `<div class="result success">Current Token: ${authToken.substring(0, 50)}...</div>`
            );
        }
    </script>
</body>
</html>
