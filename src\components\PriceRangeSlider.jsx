import React, { useState, useEffect, memo } from 'react';
import { Range } from 'react-range';

const PriceRangeSlider = ({ min = 0, max = 200, values, onChange }) => {
  // Internal state mirrors the prop to allow knob dragging without immediately committing
  const [internalValues, setInternalValues] = useState(values || [min, max]);

  // Keep internal state in sync when the parent updates the values
  useEffect(() => {
    if (Array.isArray(values) && (values[0] !== internalValues[0] || values[1] !== internalValues[1])) {
      setInternalValues(values);
    }
  }, [values]);
  
  

  // Update local position while dragging – do NOT notify parent yet for smoothness
  const handleChange = (newValues) => {
    setInternalValues(newValues);
  };

  // Notify parent once user releases thumb(s)
  const handleFinalChange = (finalValues) => {
    if (onChange) onChange(finalValues);
  };


  const renderTrack = ({ props, children }) => (
    <div
      {...props}
      className="relative h-2 w-full bg-gray-700/60 rounded-full"
      style={{
        ...props.style,
      }}
    >
      {/* Highlighted range */}
      <div
        className="absolute top-0 h-full bg-orange-400 rounded-full"
        style={{
          left: `${((internalValues[0] - min) / (max - min)) * 100}%`,
          width: `${((internalValues[1] - internalValues[0]) / (max - min)) * 100}%`
        }}
      />
      {children}
    </div>
  );

  const renderThumb = ({ props, isDragged, index }) => {
    const { key, style, ...rest } = props; // extract key to pass explicitly
    return (
      <div
        key={key}
        {...rest}
        className={`h-4 w-4 rounded-full bg-orange-400 border-2 border-blue-200 shadow focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-50 transition-transform duration-150 ${
          isDragged ? 'scale-110' : 'hover:scale-105'
        }`}
        style={{ ...style }}
      />
    );
  };

  return (
    <div className="w-full px-2 py-4">

      
      <div className="px-4 py-8">
        <Range
          step={1}
          min={min}
          max={max}
          values={internalValues}
          onChange={handleChange}
          onFinalChange={handleFinalChange}
          renderTrack={renderTrack}
          renderThumb={renderThumb}
        />
      </div>
      {/* Selected values */}
      <div className="mt-3 flex justify-between text-xs text-gray-400">
        <span>₹{internalValues[0]}</span>
        <span>₹{internalValues[1]}</span>
      </div>
    </div>
  );
};

export default memo(PriceRangeSlider);