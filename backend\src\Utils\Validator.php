<?php

namespace Wolffoxx\Utils;

/**
 * Input Validation Utility
 * 
 * Provides comprehensive input validation with
 * customizable rules and error messages.
 */
class Validator
{
    private array $data;
    private array $rules;
    private array $errors = [];
    private array $validatedData = [];

    public function __construct(array $data, array $rules)
    {
        $this->data = $data;
        $this->rules = $rules;
    }

    /**
     * Validate data against rules
     */
    public function validate(): bool
    {
        $this->errors = [];
        $this->validatedData = [];

        foreach ($this->rules as $field => $ruleString) {
            $rules = explode('|', $ruleString);
            $value = $this->data[$field] ?? null;

            foreach ($rules as $rule) {
                if (!$this->validateRule($field, $value, $rule)) {
                    break; // Stop validating this field on first error
                }
            }

            // Add to validated data if no errors for this field
            if (!isset($this->errors[$field])) {
                $this->validatedData[$field] = $value;
            }
        }

        return empty($this->errors);
    }

    /**
     * Validate single rule
     */
    private function validateRule(string $field, $value, string $rule): bool
    {
        // Parse rule and parameters
        $ruleParts = explode(':', $rule);
        $ruleName = $ruleParts[0];
        $parameters = isset($ruleParts[1]) ? explode(',', $ruleParts[1]) : [];

        switch ($ruleName) {
            case 'required':
                return $this->validateRequired($field, $value);

            case 'nullable':
                return true; // Always passes, just marks field as optional

            case 'string':
                return $this->validateString($field, $value);

            case 'email':
                return $this->validateEmail($field, $value);

            case 'min':
                return $this->validateMin($field, $value, (int)$parameters[0]);

            case 'max':
                return $this->validateMax($field, $value, (int)$parameters[0]);

            case 'integer':
            case 'int':
                return $this->validateInteger($field, $value);

            case 'numeric':
                return $this->validateNumeric($field, $value);

            case 'boolean':
            case 'bool':
                return $this->validateBoolean($field, $value);

            case 'date':
                return $this->validateDate($field, $value);

            case 'in':
                return $this->validateIn($field, $value, $parameters);

            case 'same':
                return $this->validateSame($field, $value, $parameters[0]);

            case 'different':
                return $this->validateDifferent($field, $value, $parameters[0]);

            case 'url':
                return $this->validateUrl($field, $value);

            case 'regex':
                return $this->validateRegex($field, $value, $parameters[0]);

            case 'alpha':
                return $this->validateAlpha($field, $value);

            case 'alpha_num':
                return $this->validateAlphaNum($field, $value);

            case 'array':
                return $this->validateArray($field, $value);

            default:
                return true; // Unknown rules pass by default
        }
    }

    /**
     * Validate required field
     */
    private function validateRequired(string $field, $value): bool
    {
        if ($value === null || $value === '' || (is_array($value) && empty($value))) {
            $this->addError($field, 'The ' . $field . ' field is required.');
            return false;
        }
        return true;
    }

    /**
     * Validate string type
     */
    private function validateString(string $field, $value): bool
    {
        if ($value !== null && !is_string($value)) {
            $this->addError($field, 'The ' . $field . ' field must be a string.');
            return false;
        }
        return true;
    }

    /**
     * Validate email format
     */
    private function validateEmail(string $field, $value): bool
    {
        if ($value !== null && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
            $this->addError($field, 'The ' . $field . ' field must be a valid email address.');
            return false;
        }
        return true;
    }

    /**
     * Validate minimum length/value
     */
    private function validateMin(string $field, $value, int $min): bool
    {
        if ($value === null) {
            return true;
        }

        // Always treat as string length if it's a string, regardless of numeric content
        if (is_string($value)) {
            if (strlen($value) < $min) {
                $this->addError($field, 'The ' . $field . ' field must be at least ' . $min . ' characters.');
                return false;
            }
            return true;
        }

        // Only apply numeric validation for actual numbers
        if (is_numeric($value) && !is_string($value) && $value < $min) {
            $this->addError($field, 'The ' . $field . ' field must be at least ' . $min . '.');
            return false;
        }

        return true;
    }

    /**
     * Validate maximum length/value
     */
    private function validateMax(string $field, $value, int $max): bool
    {
        if ($value === null) {
            return true;
        }

        // Always treat as string length if it's a string, regardless of numeric content
        if (is_string($value)) {
            if (strlen($value) > $max) {
                $this->addError($field, 'The ' . $field . ' field must not exceed ' . $max . ' characters.');
                return false;
            }
            return true;
        }

        // Only apply numeric validation for actual numbers
        if (is_numeric($value) && !is_string($value) && $value > $max) {
            $this->addError($field, 'The ' . $field . ' field must not exceed ' . $max . '.');
            return false;
        }

        return true;
    }

    /**
     * Validate integer type
     */
    private function validateInteger(string $field, $value): bool
    {
        if ($value !== null && !filter_var($value, FILTER_VALIDATE_INT)) {
            $this->addError($field, 'The ' . $field . ' field must be an integer.');
            return false;
        }
        return true;
    }

    /**
     * Validate numeric type
     */
    private function validateNumeric(string $field, $value): bool
    {
        if ($value !== null && !is_numeric($value)) {
            $this->addError($field, 'The ' . $field . ' field must be numeric.');
            return false;
        }
        return true;
    }

    /**
     * Validate boolean type
     */
    private function validateBoolean(string $field, $value): bool
    {
        if ($value !== null && !is_bool($value) && !in_array($value, [0, 1, '0', '1', 'true', 'false'], true)) {
            $this->addError($field, 'The ' . $field . ' field must be true or false.');
            return false;
        }
        return true;
    }

    /**
     * Validate date format
     */
    private function validateDate(string $field, $value): bool
    {
        if ($value !== null && !strtotime($value)) {
            $this->addError($field, 'The ' . $field . ' field must be a valid date.');
            return false;
        }
        return true;
    }

    /**
     * Validate value is in array
     */
    private function validateIn(string $field, $value, array $options): bool
    {
        if ($value !== null && !in_array($value, $options, true)) {
            $this->addError($field, 'The ' . $field . ' field must be one of: ' . implode(', ', $options) . '.');
            return false;
        }
        return true;
    }

    /**
     * Validate field matches another field
     */
    private function validateSame(string $field, $value, string $otherField): bool
    {
        $otherValue = $this->data[$otherField] ?? null;
        
        if ($value !== $otherValue) {
            $this->addError($field, 'The ' . $field . ' field must match ' . $otherField . '.');
            return false;
        }
        return true;
    }

    /**
     * Validate field is different from another field
     */
    private function validateDifferent(string $field, $value, string $otherField): bool
    {
        $otherValue = $this->data[$otherField] ?? null;
        
        if ($value === $otherValue) {
            $this->addError($field, 'The ' . $field . ' field must be different from ' . $otherField . '.');
            return false;
        }
        return true;
    }

    /**
     * Validate URL format
     */
    private function validateUrl(string $field, $value): bool
    {
        if ($value !== null && !filter_var($value, FILTER_VALIDATE_URL)) {
            $this->addError($field, 'The ' . $field . ' field must be a valid URL.');
            return false;
        }
        return true;
    }

    /**
     * Validate regex pattern
     */
    private function validateRegex(string $field, $value, string $pattern): bool
    {
        if ($value !== null && !preg_match($pattern, $value)) {
            $this->addError($field, 'The ' . $field . ' field format is invalid.');
            return false;
        }
        return true;
    }

    /**
     * Validate alphabetic characters only
     */
    private function validateAlpha(string $field, $value): bool
    {
        if ($value !== null && !ctype_alpha($value)) {
            $this->addError($field, 'The ' . $field . ' field must contain only letters.');
            return false;
        }
        return true;
    }

    /**
     * Validate alphanumeric characters only
     */
    private function validateAlphaNum(string $field, $value): bool
    {
        if ($value !== null && !ctype_alnum($value)) {
            $this->addError($field, 'The ' . $field . ' field must contain only letters and numbers.');
            return false;
        }
        return true;
    }

    /**
     * Validate array type
     */
    private function validateArray(string $field, $value): bool
    {
        if ($value !== null && !is_array($value)) {
            $this->addError($field, 'The ' . $field . ' field must be an array.');
            return false;
        }
        return true;
    }

    /**
     * Add validation error
     */
    private function addError(string $field, string $message): void
    {
        if (!isset($this->errors[$field])) {
            $this->errors[$field] = [];
        }
        $this->errors[$field][] = $message;
    }

    /**
     * Get validation errors
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Get validated data
     */
    public function getValidatedData(): array
    {
        return $this->validatedData;
    }

    /**
     * Check if validation passed
     */
    public function passes(): bool
    {
        return empty($this->errors);
    }

    /**
     * Check if validation failed
     */
    public function fails(): bool
    {
        return !empty($this->errors);
    }
}
