<?php

namespace Wolffoxx\Controllers;

use Wolffoxx\Models\User;
use Wolffoxx\Models\UserAddress;
use Wolffoxx\Services\OTPService;
use Wolffoxx\Config\JWTConfig;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Logger;
use Wolffoxx\Utils\Validator;
use Wolffoxx\Middleware\AuthMiddleware;

/**
 * OTP Authentication Controller
 * 
 * Handles phone number based authentication using OTP,
 * including registration, login, and verification.
 */
class OTPAuthController
{
    private User $userModel;
    private UserAddress $addressModel;
    private OTPService $otpService;
    private Logger $logger;

    public function __construct()
    {
        $this->userModel = new User();
        $this->addressModel = new UserAddress();
        $this->otpService = new OTPService();
        $this->logger = new Logger('otp_auth');
    }

    /**
     * Send OTP for login/registration
     */
    public function sendOTP(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();



            // Validate input
            $validator = new Validator($input, [
                'phone' => 'required|string|min:10|max:15',
                'purpose' => 'nullable|in:login,registration'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $phone = $data['phone'];
            $purpose = $data['purpose'] ?? 'login';

            // Send OTP
            $result = $this->otpService->generateAndSendOTP($phone, $purpose);

            if ($result['success']) {
                $this->logger->info('OTP send request successful', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'purpose' => $purpose
                ]);

                Response::success([
                    'message' => $result['message'],
                    'phone' => $result['phone'],
                    'expires_in' => $result['expires_in'],
                    'can_resend_after' => $result['can_resend_after']
                ]);
            } else {
                Response::error($result['error'], 400);
            }

        } catch (\Exception $e) {
            $this->logger->error('Send OTP failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to send OTP. Please try again.');
        }
    }

    /**
     * Verify OTP and login/register user
     */
    public function verifyOTP(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'phone' => 'required|string|min:10|max:15',
                'otp' => 'required|string|min:4|max:8',
                'purpose' => 'nullable|in:login,registration',
                'user_details' => 'nullable|array' // For new user registration
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $phone = $data['phone'];
            $otp = $data['otp'];
            $purpose = $data['purpose'] ?? 'login';
            $userDetails = $data['user_details'] ?? [];

            // Verify OTP
            $otpResult = $this->otpService->verifyOTP($phone, $otp, $purpose);

            if (!$otpResult['success']) {
                Response::error($otpResult['error'], 400);
                return;
            }

            // Clean phone number
            $cleanPhone = $this->cleanPhoneNumber($phone);

            // Check if user exists
            $user = $this->userModel->findBy('phone', $cleanPhone);

            if (!$user) {
                // Create new user
                $user = $this->createNewUser($cleanPhone, $userDetails);
                
                if (!$user) {
                    Response::error('Failed to create user account');
                    return;
                }

                $isNewUser = true;
            } else {
                // Existing user
                if (!$user['is_active']) {
                    Response::forbidden('Account is inactive. Please contact support.');
                    return;
                }

                $isNewUser = false;
            }

            // Update last login
            $this->userModel->updateLastLogin($user['id']);

            // Generate JWT tokens
            $tokens = JWTConfig::generateTokenPair([
                'user_id' => $user['id'],
                'phone' => $user['phone'],
                'role' => $user['is_admin'] ? 'admin' : 'user'
            ]);

            // Debug token generation
            $this->logger->debug('Generated tokens for user', [
                'user_id' => $user['id'],
                'access_token_preview' => substr($tokens['access_token'], 0, 50) . '...',
                'refresh_token_preview' => substr($tokens['refresh_token'], 0, 50) . '...',
                'token_type' => $tokens['token_type'],
                'expires_in' => $tokens['expires_in']
            ]);

            $this->logger->info('OTP verification and login successful', [
                'user_id' => $user['id'],
                'phone' => $this->maskPhoneNumber($phone),
                'is_new_user' => $isNewUser
            ]);

            Response::success([
                'user' => $user,
                'tokens' => $tokens,
                'is_new_user' => $isNewUser,
                'message' => $isNewUser ? 'Account created and logged in successfully' : 'Login successful'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Verify OTP failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('OTP verification failed. Please try again.');
        }
    }

    /**
     * Resend OTP
     */
    public function resendOTP(array $params = []): void
    {
        try {
            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'phone' => 'required|string|min:10|max:15',
                'purpose' => 'nullable|in:login,registration'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();
            $phone = $data['phone'];
            $purpose = $data['purpose'] ?? 'login';

            // Resend OTP
            $result = $this->otpService->resendOTP($phone, $purpose);

            if ($result['success']) {
                $this->logger->info('OTP resend successful', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'purpose' => $purpose
                ]);

                Response::success([
                    'message' => 'OTP resent successfully',
                    'phone' => $result['phone'],
                    'expires_in' => $result['expires_in'],
                    'can_resend_after' => $result['can_resend_after']
                ]);
            } else {
                Response::error($result['error'], 400);
            }

        } catch (\Exception $e) {
            $this->logger->error('Resend OTP failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to resend OTP. Please try again.');
        }
    }

    /**
     * Update user profile after OTP login
     */
    public function updateProfile(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $input = $this->getJsonInput();

            // DEBUG: Log the received input
            $this->logger->info('DEBUG: Received profile update input', [ 'input' => $input ]);

            // Validate input
            $validator = new Validator($input, [
                'first_name' => 'required|string|max:100',
                'last_name' => 'required|string|max:100',
                'email' => 'required|email|max:255',
                'date_of_birth' => 'nullable|date',
                'gender' => 'required|in:male,female,other,prefer_not_to_say',
                'address' => 'nullable|string|max:500', // Make address optional since we now use user_addresses table
                'newsletter_subscribed' => 'nullable|boolean',
                'marketing_emails' => 'nullable|boolean',
                'sms_notifications' => 'nullable|boolean',
                'address_data' => 'nullable|array' // Allow address data to be passed as an array
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Check if email already exists (if provided)
            if (!empty($data['email'])) {
                $existingUser = $this->userModel->findByEmail($data['email']);
                if ($existingUser && $existingUser['id'] !== $userId) {
                    Response::error('Email already registered with another account', 409);
                    return;
                }
            }

            // Handle address data if provided
            if (!empty($data['address_data']) && is_array($data['address_data'])) {
                $this->logger->info('OTP update - address data found', [
                    'user_id' => $userId,
                    'address_data' => $data['address_data']
                ]);
                
                // Add user_id to address data
                $data['address_data']['user_id'] = $userId;
                
                // Set default values if not provided
                if (!isset($data['address_data']['type'])) {
                    $data['address_data']['type'] = 'shipping';
                }
                
                if (!isset($data['address_data']['is_default'])) {
                    $data['address_data']['is_default'] = true;
                }
                
                // Make sure all required fields are present
                $requiredFields = ['first_name', 'last_name', 'address_line_1', 'city', 'state', 'postal_code', 'country'];
                $missingFields = [];
                
                foreach ($requiredFields as $field) {
                    if (empty($data['address_data'][$field])) {
                        $missingFields[] = $field;
                    }
                }
                
                if (!empty($missingFields)) {
                    $this->logger->error('Missing required address fields', [
                        'user_id' => $userId,
                        'missing_fields' => $missingFields
                    ]);
                    Response::error('Missing required address fields: ' . implode(', ', $missingFields), 400);
                    return;
                }
                
                try {
                    // Add the address
                    $this->logger->info('Attempting to add address', [
                        'user_id' => $userId
                    ]);
                    
                    $address = $this->addressModel->addAddress($data['address_data']);
                    
                    $this->logger->info('Address added result', [
                        'user_id' => $userId,
                        'success' => (bool)$address,
                        'address_id' => $address['id'] ?? null
                    ]);
                } catch (\Exception $e) {
                    $this->logger->error('Error adding address', [
                        'user_id' => $userId,
                        'error' => $e->getMessage()
                    ]);
                    // Continue with profile update even if address fails
                }
                
                // Remove address_data from the profile update data
                unset($data['address_data']);
            }

            // Update profile
            $updatedProfile = $this->userModel->updateProfile($userId, $data);

            if (!$updatedProfile) {
                Response::error('Failed to update profile');
                return;
            }

            // Get the updated profile with addresses
            $fullProfile = $this->userModel->getProfile($userId);
            
            // Add addresses to the response
            if ($fullProfile) {
                $fullProfile['addresses'] = $this->addressModel->getUserAddresses($userId);
            }

            $this->logger->info('Profile updated after OTP login', [
                'user_id' => $userId,
                'updated_fields' => array_keys($data)
            ]);

            Response::success([
                'profile' => $fullProfile ?: $updatedProfile,
                'message' => 'Profile updated successfully'
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Update profile failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to update profile: ' . $e->getMessage());
        }
    }

    /**
     * Create new user from phone number
     */
    private function createNewUser(string $phone, array $userDetails = []): ?array
    {
        try {
            $userData = [
                'phone' => $phone,
                'first_name' => $userDetails['first_name'] ?? 'User',
                'last_name' => $userDetails['last_name'] ?? '',
                'email' => $userDetails['email'] ?? null,
                'is_active' => true,
                'is_admin' => false,
                'newsletter_subscribed' => $userDetails['newsletter_subscribed'] ?? false,
                'marketing_emails' => $userDetails['marketing_emails'] ?? true,
                'sms_notifications' => $userDetails['sms_notifications'] ?? true
            ];

            // Generate a UUID
            $userData['uuid'] = \Ramsey\Uuid\Uuid::uuid4()->toString();

            // Create user without password (phone-based auth)
            $user = $this->userModel->create($userData);

            $this->logger->info('New user created via OTP', [
                'user_id' => $user['id'],
                'phone' => $this->maskPhoneNumber($phone)
            ]);

            return $user;

        } catch (\Exception $e) {
            $this->logger->error('Failed to create new user', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Clean phone number
     */
    private function cleanPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $clean = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if missing (assuming India +91)
        if (strlen($clean) === 10) {
            $clean = '91' . $clean;
        }
        
        return $clean;
    }

    /**
     * Mask phone number for logging
     */
    private function maskPhoneNumber(string $phone): string
    {
        $clean = $this->cleanPhoneNumber($phone);
        if (strlen($clean) > 6) {
            return substr($clean, 0, 3) . '****' . substr($clean, -3);
        }
        return '****';
    }

    /**
     * Get JSON input from request body
     */
    private function getJsonInput(): array
    {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            Response::error('Invalid JSON input', 400);
            exit;
        }

        return $data ?? [];
    }

    /**
     * Get OTP service statistics (admin only)
     */
    public function getOTPStats(array $params = []): void
    {
        try {
            // Check if user is admin
            if (!AuthMiddleware::isCurrentUserAdmin()) {
                Response::forbidden('Admin access required');
                return;
            }

            $stats = $this->otpService->getOTPStats();

            Response::success($stats);

        } catch (\Exception $e) {
            $this->logger->error('Get OTP stats failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to get OTP statistics');
        }
    }

    /**
     * Cleanup expired OTPs (admin only)
     */
    public function cleanupOTPs(array $params = []): void
    {
        try {
            // Check if user is admin
            if (!AuthMiddleware::isCurrentUserAdmin()) {
                Response::forbidden('Admin access required');
                return;
            }

            $deletedCount = $this->otpService->cleanupExpiredOTPs();

            Response::success([
                'message' => 'Expired OTPs cleaned up successfully',
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            $this->logger->error('OTP cleanup failed', [
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to cleanup expired OTPs');
        }
    }
}
