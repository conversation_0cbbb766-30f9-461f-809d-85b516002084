<?php

namespace Wolffoxx\Config;

use PDO;
use PDOException;

/**
 * Database Configuration and Connection Manager
 * 
 * Handles database connections with connection pooling,
 * error handling, and performance optimization.
 */
class Database
{
    private static ?PDO $connection = null;
    private static array $config = [];

    /**
     * Initialize database configuration
     */
    public static function init(): void
    {
        self::$config = [
            'host' => $_ENV['DB_HOST'] ?? 'localhost',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'dbname' => $_ENV['DB_NAME'] ?? 'wolffoxx',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
            'charset' => 'utf8mb4',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => true,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ]
        ];
    }

    /**
     * Get database connection with connection pooling
     */
    public static function getConnection(): PDO
    {
        if (self::$connection === null) {
            self::connect();
        }

        // Test connection and reconnect if needed
        try {
            self::$connection->query('SELECT 1');
        } catch (PDOException $e) {
            self::connect();
        }

        return self::$connection;
    }

    /**
     * Establish database connection
     */
    private static function connect(): void
    {
        try {
            $dsn = sprintf(
                'mysql:host=%s;port=%s;dbname=%s;charset=%s',
                self::$config['host'],
                self::$config['port'],
                self::$config['dbname'],
                self::$config['charset']
            );

            self::$connection = new PDO(
                $dsn,
                self::$config['username'],
                self::$config['password'],
                self::$config['options']
            );

        } catch (PDOException $e) {
            throw new PDOException(
                'Database connection failed: ' . $e->getMessage(),
                (int)$e->getCode()
            );
        }
    }

    /**
     * Execute a prepared statement with parameters
     */
    public static function execute(string $sql, array $params = []): \PDOStatement
    {
        try {
            $connection = self::getConnection();
            $statement = $connection->prepare($sql);

            $statement->execute($params);
            return $statement;

        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Begin database transaction
     */
    public static function beginTransaction(): bool
    {
        try {
            $result = self::getConnection()->beginTransaction();
            return $result;
        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Commit database transaction
     */
    public static function commit(): bool
    {
        try {
            $result = self::getConnection()->commit();
            return $result;
        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Rollback database transaction
     */
    public static function rollback(): bool
    {
        try {
            $result = self::getConnection()->rollback();
            return $result;
        } catch (PDOException $e) {
            throw $e;
        }
    }

    /**
     * Get the last inserted ID
     */
    public static function lastInsertId(): string
    {
        return self::getConnection()->lastInsertId();
    }

    /**
     * Close database connection
     */
    public static function close(): void
    {
        self::$connection = null;
    }

    /**
     * Check if database connection is active
     */
    public static function isConnected(): bool
    {
        if (self::$connection === null) {
            return false;
        }

        try {
            self::$connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }

    /**
     * Get database configuration (without sensitive data)
     */
    public static function getConfig(): array
    {
        return [
            'host' => self::$config['host'],
            'port' => self::$config['port'],
            'dbname' => self::$config['dbname'],
            'charset' => self::$config['charset']
        ];
    }
}
