import { motion } from 'framer-motion';
import { Flame, RotateCcw, Shirt, Truck } from 'lucide-react';

const features = [
  {
    icon: <Shirt size={36} className="text-[#3b82f6]" />,
    title: '100% Cotton Quality',
    description: 'Premium heavyweight cotton for durability and comfort in our oversized fits',
  },
  {
    icon: <Truck size={36} className="text-[#3b82f6]" />,
    title: 'Fast Shipping & COD',
    description: 'Quick delivery with cash on delivery option available nationwide',
  },
  {
    icon: <RotateCcw size={36} className="text-[#3b82f6]" />,
    title: 'Easy Exchanges',
    description: "Hassle-free 15-day exchange policy if size or fit doesn't work for you",
  },
  {
    icon: <Flame size={36} className="text-[#3b82f6]" />,
    title: 'Trend-First Designs',
    description: 'Stay ahead with our limited drops featuring exclusive graphics and colorways',
  },
];

export default function WhyChooseUs() {
  return (
    <section className="py-16 bg-[#0f172a]">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div
          className="text-center mb-6 sm:mb-8 md:mb-10 lg:mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-['Bebas_Neue',sans-serif] tracking-wider text-white mb-3 sm:mb-4 leading-tight">
            WHY CHOOSE <span className="text-[#3b82f6]">WOLFFOXX</span>
          </h2>
          <p className="text-sm sm:text-base md:text-lg lg:text-xl text-[#94a3b8] max-w-3xl mx-auto leading-relaxed font-light px-2 sm:px-0">
            What makes our oversized tees stand out from the crowd
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-[#1e293b] rounded-lg p-6 text-center hover:bg-[#1e293b]/80 transition-colors"
            >
              <motion.div
                className="flex justify-center mb-4"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ type: 'spring', stiffness: 400, damping: 10 }}
              >
                {feature.icon}
              </motion.div>
              <h3 className="text-lg sm:text-xl md:text-xl lg:text-2xl font-semibold text-white mb-2 sm:mb-3 leading-tight">{feature.title}</h3>
              <p className="text-sm sm:text-base text-[#94a3b8] leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}