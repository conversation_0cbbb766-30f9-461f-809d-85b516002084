import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ZoomIn, ZoomOut, RotateCcw, Maximize2 } from 'lucide-react';

export default function EnhancedImageZoom({ 
  src, 
  alt, 
  className = '', 
  zoomLevel = 2.5,
  enableMagnifier = true,
  showControls = true,
  onImageLoad,
  highResSrc = null // Optional high-res version for zoom
}) {
  const [isZoomed, setIsZoomed] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 50, y: 50 });
  const [magnifierPosition, setMagnifierPosition] = useState({ x: 0, y: 0 });
  const [showMagnifier, setShowMagnifier] = useState(false);
  const [currentZoomLevel, setCurrentZoomLevel] = useState(zoomLevel);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [highResLoaded, setHighResLoaded] = useState(false);
  
  const imageRef = useRef(null);
  const containerRef = useRef(null);
  const magnifierRef = useRef(null);

  // Preload high-res image when component mounts
  useEffect(() => {
    if (highResSrc) {
      const img = new Image();
      img.onload = () => setHighResLoaded(true);
      img.src = highResSrc;
    }
  }, [highResSrc]);

  const handleMouseMove = (e) => {
    if (!imageRef.current || !containerRef.current) return;

    const container = containerRef.current;
    const image = imageRef.current;
    const rect = container.getBoundingClientRect();
    
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    // Calculate percentage position
    const xPercent = (x / rect.width) * 100;
    const yPercent = (y / rect.height) * 100;
    
    setZoomPosition({ x: xPercent, y: yPercent });
    
    // Magnifier position
    if (enableMagnifier && showMagnifier) {
      setMagnifierPosition({ x: x - 75, y: y - 75 }); // 75 = half magnifier size
    }
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (enableMagnifier) {
      setShowMagnifier(true);
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setShowMagnifier(false);
    if (!isZoomed) {
      setZoomPosition({ x: 50, y: 50 });
    }
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
    if (!isZoomed) {
      setShowMagnifier(false);
    }
  };

  const resetZoom = () => {
    setIsZoomed(false);
    setCurrentZoomLevel(zoomLevel);
    setZoomPosition({ x: 50, y: 50 });
    setShowMagnifier(false);
  };

  const adjustZoomLevel = (delta) => {
    setCurrentZoomLevel(prev => Math.max(1.5, Math.min(4, prev + delta)));
  };

  const handleImageLoad = () => {
    setImageLoaded(true);
    if (onImageLoad) onImageLoad();
  };

  const currentSrc = isZoomed && highResSrc && highResLoaded ? highResSrc : src;

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden cursor-zoom-in group ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={toggleZoom}
    >
      {/* Main Image */}
      <motion.img
        ref={imageRef}
        src={currentSrc}
        alt={alt}
        className={`w-full h-full object-cover transition-all duration-500 ${
          isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'
        }`}
        style={
          isZoomed
            ? {
                transform: `scale(${currentZoomLevel})`,
                transformOrigin: `${zoomPosition.x}% ${zoomPosition.y}%`,
              }
            : isHovered
            ? { transform: 'scale(1.05)' }
            : {}
        }
        onLoad={handleImageLoad}
        loading="lazy"
      />

      {/* Loading State */}
      <AnimatePresence>
        {!imageLoaded && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-slate-800 flex items-center justify-center"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
              className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Magnifier */}
      <AnimatePresence>
        {showMagnifier && !isZoomed && enableMagnifier && imageLoaded && (
          <motion.div
            ref={magnifierRef}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute w-32 h-32 border-2 border-white rounded-full pointer-events-none z-20 shadow-xl"
            style={{
              left: magnifierPosition.x,
              top: magnifierPosition.y,
              backgroundImage: `url(${currentSrc})`,
              backgroundSize: `${imageRef.current?.offsetWidth * 2.5}px ${imageRef.current?.offsetHeight * 2.5}px`,
              backgroundPosition: `-${(zoomPosition.x / 100) * imageRef.current?.offsetWidth * 2.5 - 64}px -${(zoomPosition.y / 100) * imageRef.current?.offsetHeight * 2.5 - 64}px`,
              backgroundRepeat: 'no-repeat',
            }}
          >
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-white/20 to-transparent" />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Zoom Controls */}
      <AnimatePresence>
        {showControls && (isHovered || isZoomed) && imageLoaded && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute bottom-4 right-4 flex gap-2 z-30"
          >
            {isZoomed && (
              <>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustZoomLevel(-0.5);
                  }}
                  className="w-8 h-8 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
                >
                  <ZoomOut size={14} />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustZoomLevel(0.5);
                  }}
                  className="w-8 h-8 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
                >
                  <ZoomIn size={14} />
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    resetZoom();
                  }}
                  className="w-8 h-8 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
                >
                  <RotateCcw size={14} />
                </motion.button>
              </>
            )}
            {!isZoomed && (
              <motion.button
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleZoom();
                }}
                className="w-8 h-8 bg-black/70 hover:bg-black/80 text-white rounded-full flex items-center justify-center backdrop-blur-sm transition-colors"
              >
                <Maximize2 size={14} />
              </motion.button>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Zoom Level Indicator */}
      <AnimatePresence>
        {isZoomed && showControls && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute top-4 right-4 bg-black/70 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm z-30"
          >
            {Math.round(currentZoomLevel * 100)}%
          </motion.div>
        )}
      </AnimatePresence>

      {/* High-res loading indicator */}
      <AnimatePresence>
        {isZoomed && highResSrc && !highResLoaded && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute top-4 left-4 bg-blue-500/80 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm z-30 flex items-center gap-1"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
              className="w-3 h-3 border border-white border-t-transparent rounded-full"
            />
            Loading HD...
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
