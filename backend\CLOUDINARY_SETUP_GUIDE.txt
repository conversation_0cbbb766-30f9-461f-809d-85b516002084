🖼️ CLOUDINARY INTEGRATION GUIDE FOR WOLFFOXX ECOMMERCE
================================================================

📋 OVERVIEW:
- Replace mock image URLs with Cloudinary URLs
- Professional image delivery with auto-optimization
- Easy client workflow for adding new product images
- Free tier: 25GB storage + 25GB bandwidth (perfect for 500+ images)

🚀 STEP 1: CLOUDINARY ACCOUNT SETUP
===================================

1. Go to: https://cloudinary.com/users/register/free
2. Sign up with your email
3. Choose a cloud name (e.g., "wolffoxx-store")
4. Note down your credentials:
   - Cloud Name: wolffoxx-store
   - API Key: ***************
   - API Secret: abcdefghijklmnopqrstuvwxyz123456

🛠️ STEP 2: INSTALL CLOUDINARY PHP SDK
=====================================

In your backend directory, run:
```bash
composer require cloudinary/cloudinary_php
```

🔧 STEP 3: CREATE CLOUDINARY CONFIG
===================================

Create: backend/src/Config/Cloudinary.php
```php
<?php
namespace Wolffoxx\Config;

use Cloudinary\Configuration\Configuration;
use Cloudinary\Api\Upload\UploadApi;

class CloudinaryConfig
{
    private static $instance = null;
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            Configuration::instance([
                'cloud' => [
                    'cloud_name' => 'your-cloud-name',
                    'api_key' => 'your-api-key',
                    'api_secret' => 'your-api-secret'
                ],
                'url' => [
                    'secure' => true
                ]
            ]);
            self::$instance = new UploadApi();
        }
        return self::$instance;
    }
}
```

📤 STEP 4: CREATE IMAGE UPLOAD API
===================================

Create: backend/src/Controllers/ImageController.php
```php
<?php
namespace Wolffoxx\Controllers;

use Wolffoxx\Config\CloudinaryConfig;
use Wolffoxx\Utils\Response;
use Wolffoxx\Models\Database;

class ImageController
{
    public function uploadProductImage($params)
    {
        try {
            $productId = $params['id'];
            $color = $_POST['color'] ?? null;
            
            if (!isset($_FILES['image'])) {
                Response::error('No image file provided', 400);
                return;
            }
            
            $file = $_FILES['image'];
            $cloudinary = CloudinaryConfig::getInstance();
            
            // Upload to Cloudinary
            $folder = "products/{$productId}";
            if ($color) {
                $folder .= "/{$color}";
            }
            
            $result = $cloudinary->upload($file['tmp_name'], [
                'folder' => $folder,
                'public_id' => uniqid(),
                'overwrite' => true,
                'resource_type' => 'image'
            ]);
            
            // Save URL to database
            $sql = "INSERT INTO product_images (product_id, image_url, color_variant, alt_text, sort_order) 
                    VALUES (?, ?, ?, ?, ?)";
            
            $altText = "Product {$productId}" . ($color ? " {$color}" : "");
            $sortOrder = $this->getNextSortOrder($productId, $color);
            
            Database::execute($sql, [
                $productId,
                $result['secure_url'],
                $color,
                $altText,
                $sortOrder
            ]);
            
            Response::success([
                'image_url' => $result['secure_url'],
                'public_id' => $result['public_id']
            ]);
            
        } catch (\Exception $e) {
            Response::error('Upload failed: ' . $e->getMessage(), 500);
        }
    }
    
    private function getNextSortOrder($productId, $color)
    {
        $sql = "SELECT MAX(sort_order) as max_order FROM product_images 
                WHERE product_id = ? AND color_variant = ?";
        $result = Database::execute($sql, [$productId, $color])->fetch();
        return ($result['max_order'] ?? -1) + 1;
    }
}
```

📁 STEP 5: BULK UPLOAD SCRIPT
==============================

Create: backend/scripts/bulk_upload_images.php
```php
<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/Config/Database.php';
require_once __DIR__ . '/../src/Config/Cloudinary.php';

use Wolffoxx\Config\CloudinaryConfig;
use Wolffoxx\Models\Database;

// Initialize database
Database::initialize();

function bulkUploadImages($productId, $imageFolderPath)
{
    $cloudinary = CloudinaryConfig::getInstance();
    
    // Scan folder structure: /product_1/black/*.jpg, /product_1/white/*.jpg
    $productFolder = $imageFolderPath . "/product_{$productId}";
    
    if (!is_dir($productFolder)) {
        echo "Product folder not found: {$productFolder}\n";
        return;
    }
    
    $colorFolders = scandir($productFolder);
    
    foreach ($colorFolders as $colorFolder) {
        if ($colorFolder === '.' || $colorFolder === '..') continue;
        
        $colorPath = $productFolder . '/' . $colorFolder;
        if (!is_dir($colorPath)) continue;
        
        echo "Processing color: {$colorFolder}\n";
        
        $images = glob($colorPath . '/*.{jpg,jpeg,png,JPG,JPEG,PNG}', GLOB_BRACE);
        
        foreach ($images as $imagePath) {
            try {
                // Upload to Cloudinary
                $result = $cloudinary->upload($imagePath, [
                    'folder' => "products/{$productId}/{$colorFolder}",
                    'public_id' => pathinfo($imagePath, PATHINFO_FILENAME),
                    'overwrite' => true,
                    'resource_type' => 'image'
                ]);
                
                // Save to database
                $sql = "INSERT INTO product_images (product_id, image_url, color_variant, alt_text, sort_order) 
                        VALUES (?, ?, ?, ?, ?)";
                
                $sortOrder = count(glob($colorPath . '/*.{jpg,jpeg,png,JPG,JPEG,PNG}', GLOB_BRACE));
                
                Database::execute($sql, [
                    $productId,
                    $result['secure_url'],
                    $colorFolder,
                    "Product {$productId} {$colorFolder}",
                    $sortOrder
                ]);
                
                echo "✅ Uploaded: " . basename($imagePath) . "\n";
                
            } catch (\Exception $e) {
                echo "❌ Failed: " . basename($imagePath) . " - " . $e->getMessage() . "\n";
            }
        }
    }
}

// Usage example:
// php bulk_upload_images.php
echo "🚀 Bulk Image Upload Script\n";
echo "Usage: bulkUploadImages(productId, '/path/to/images/folder')\n";

// Example:
// bulkUploadImages(1, '/path/to/client/images');
```

🔄 STEP 6: UPDATE EXISTING MOCK DATA
====================================

Create: backend/scripts/replace_mock_images.php
```php
<?php
// Script to replace existing mock URLs with Cloudinary URLs
// Run this after uploading real images

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../src/Config/Database.php';

use Wolffoxx\Models\Database;

Database::initialize();

// Clear existing mock images
$sql = "DELETE FROM product_images WHERE image_url LIKE '%unsplash%'";
Database::execute($sql);

echo "✅ Cleared mock images. Ready for real Cloudinary images!\n";
```

📋 STEP 7: CLIENT WORKFLOW
===========================

When your client sends images:

1. **Organize client images in folders:**
   ```
   client_images/
   ├── product_1/
   │   ├── black/
   │   │   ├── front.jpg
   │   │   ├── back.jpg
   │   │   └── detail.jpg
   │   ├── white/
   │   │   ├── front.jpg
   │   │   └── back.jpg
   │   └── red/
   │       └── front.jpg
   ├── product_2/
   │   ├── navy/
   │   └── gray/
   ```

2. **Run bulk upload:**
   ```bash
   cd backend/scripts
   php bulk_upload_images.php
   ```

3. **Images automatically appear on website!**

🎯 STEP 8: ADD ROUTES
=====================

Add to backend/public/index.php:
```php
// Image upload routes (add to your existing routes)
$router->post('/api/v1/admin/products/{id}/images', 'Wolffoxx\\Controllers\\ImageController@uploadProductImage');
```

💡 STEP 9: OPTIMIZATION FEATURES
=================================

Cloudinary automatically provides:

1. **Auto-format:** WebP for modern browsers, JPEG for older
2. **Auto-quality:** Optimal compression
3. **Responsive:** Different sizes for mobile/desktop
4. **Fast delivery:** Global CDN

Example optimized URLs:
- Original: https://res.cloudinary.com/wolffoxx/image/upload/v123/products/1/black/front.jpg
- Optimized: https://res.cloudinary.com/wolffoxx/image/upload/f_auto,q_auto/products/1/black/front.jpg
- Mobile: https://res.cloudinary.com/wolffoxx/image/upload/w_400,f_auto,q_auto/products/1/black/front.jpg

🚀 READY TO IMPLEMENT!
=======================

This setup gives you:
✅ Professional image delivery
✅ Automatic optimization
✅ Easy client workflow
✅ Scalable storage
✅ Fast global CDN

Total setup time: ~1 hour
Client can add new images in minutes!
