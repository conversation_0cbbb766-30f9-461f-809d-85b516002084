import ProductCard from './ProductCard';

const ProductGrid = ({ products, title }) => {
  return (
    <section className="my-8">
      {title && <h2 className="text-2xl font-bold mb-6 px-4">{title}</h2>}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 px-4">
        {products.map((product) => (
          <ProductCard
            key={product.id}
            id={product.id}
            name={product.name}
            price={product.price}
            variants={product.variants}
          />
        ))}
      </div>
    </section>
  );
};

export default ProductGrid;