import { motion } from 'framer-motion';
import { Sparkles, Zap } from 'lucide-react';

const SmartBadge = () => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className="hidden lg:flex items-center gap-2 bg-gradient-to-r from-purple-600/20 to-pink-600/20 border border-purple-500/30 rounded-full px-3 py-1.5 backdrop-blur-sm"
    >
      <motion.div
        animate={{ rotate: [0, 360] }}
        transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
      >
        <Sparkles size={14} className="text-purple-400" />
      </motion.div>
      <span className="text-purple-300 text-xs font-medium">AI-Powered</span>
      <motion.div
        animate={{ scale: [1, 1.2, 1] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Zap size={12} className="text-pink-400" />
      </motion.div>
    </motion.div>
  );
};

export default SmartBadge;
