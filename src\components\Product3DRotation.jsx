import { useState, useRef } from 'react';
import { motion, useMotionValue, useSpring, useTransform } from 'framer-motion';

export default function Product3DRotation({ children, className = '', rotationFactor = 10 }) {
  const ref = useRef(null);
  const [isHovering, setIsHovering] = useState(false);

  // Motion values for tracking mouse position relative to card center
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // Add spring physics for smoother motion
  const rotateX = useSpring(useTransform(y, [-0.5, 0.5], [rotationFactor, -rotationFactor]));
  const rotateY = useSpring(useTransform(x, [-0.5, 0.5], [-rotationFactor, rotationFactor]));

  // Handle mouse movement
  const handleMouseMove = (e) => {
    if (!ref.current || !isHovering) return;

    const rect = ref.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;

    // Calculate mouse position relative to card center (-0.5 to 0.5)
    const xPos = (e.clientX - rect.left) / width - 0.5;
    const yPos = (e.clientY - rect.top) / height - 0.5;

    x.set(xPos);
    y.set(yPos);
  };

  return (
    <motion.div
      ref={ref}
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => {
        setIsHovering(false);
        // Reset position when mouse leaves
        x.set(0);
        y.set(0);
      }}
      onMouseMove={handleMouseMove}
      style={{
        perspective: 1000,
        transformStyle: 'preserve-3d',
      }}
    >
      <motion.div
        style={{
          rotateX: rotateX,
          rotateY: rotateY,
          transition: 'transform 0.1s ease',
        }}
      >
        {children}
      </motion.div>
    </motion.div>
  );
}