<?php

namespace Wolffoxx\Services;

use Wolffoxx\Utils\Logger;

/**
 * SMS Service
 * 
 * Handles SMS sending through multiple providers including
 * FREE options for budget-conscious clients.
 */
class SMSService
{
    private Logger $logger;
    private string $provider;
    private array $config;

    public function __construct()
    {
        $this->logger = new Logger('sms');
        $this->provider = $_ENV['SMS_PROVIDER'] ?? 'textlocal'; // Default to free option
        
        $this->config = [
            // TextLocal (FREE 10 SMS for testing)
            'textlocal' => [
                'api_key' => $_ENV['TEXTLOCAL_API_KEY'] ?? '',
                'sender' => $_ENV['TEXTLOCAL_SENDER'] ?? 'WOLFFOXX',
                'endpoint' => 'https://api.textlocal.in/send/'
            ],
            
            // Fast2SMS (FREE 50 SMS daily)
            'fast2sms' => [
                'api_key' => $_ENV['FAST2SMS_API_KEY'] ?? '',
                'sender_id' => $_ENV['FAST2SMS_SENDER_ID'] ?? 'WOLFFOXX',
                'endpoint' => 'https://www.fast2sms.com/dev/bulkV2'
            ],
            
            // MSG91 (FREE trial credits)
            'msg91' => [
                'auth_key' => $_ENV['MSG91_AUTH_KEY'] ?? '',
                'sender_id' => $_ENV['MSG91_SENDER_ID'] ?? 'WOLFFOXX',
                'route' => $_ENV['MSG91_ROUTE'] ?? '4',
                'endpoint' => 'https://api.msg91.com/api/sendhttp.php'
            ],
            
            // Twilio (Paid but reliable)
            'twilio' => [
                'account_sid' => $_ENV['TWILIO_ACCOUNT_SID'] ?? '',
                'auth_token' => $_ENV['TWILIO_AUTH_TOKEN'] ?? '',
                'from_number' => $_ENV['TWILIO_FROM_NUMBER'] ?? '',
                'endpoint' => 'https://api.twilio.com/2010-04-01/Accounts/'
            ],
            
            // Development mode (logs instead of sending)
            'log' => []
        ];
    }

    /**
     * Send OTP via SMS
     */
    public function sendOTP(string $phoneNumber, string $otp, string $purpose = 'login'): array
    {
        try {
            // Format phone number
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);

            // Send SMS based on provider
            switch ($this->provider) {
                case 'textlocal':
                    $message = $this->generateOTPMessage($otp, $purpose);
                    return $this->sendViaTextLocal($formattedPhone, $message);

                case 'fast2sms':
                    // Fast2SMS uses dedicated OTP API - pass OTP directly
                    return $this->sendViaFast2SMS($formattedPhone, $otp, $purpose);

                case 'msg91':
                    $message = $this->generateOTPMessage($otp, $purpose);
                    return $this->sendViaMsg91($formattedPhone, $message);

                case 'twilio':
                    $message = $this->generateOTPMessage($otp, $purpose);
                    return $this->sendViaTwilio($formattedPhone, $message);

                case 'log':
                default:
                    $message = $this->generateOTPMessage($otp, $purpose);
                    return $this->sendViaLog($formattedPhone, $message);
            }

        } catch (\Exception $e) {
            $this->logger->error('SMS sending failed', [
                'provider' => $this->provider,
                'phone' => $this->maskPhoneNumber($phoneNumber),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send general SMS message (for notifications, order confirmations, etc.)
     */
    public function sendSMS(string $phoneNumber, string $message): array
    {
        try {
            // Format phone number
            $formattedPhone = $this->formatPhoneNumber($phoneNumber);

            // Send SMS based on provider
            switch ($this->provider) {
                case 'textlocal':
                    return $this->sendViaTextLocal($formattedPhone, $message);

                case 'fast2sms':
                    // For general SMS, use Fast2SMS regular SMS API (not OTP API)
                    return $this->sendViaFast2SMSGeneral($formattedPhone, $message);

                case 'msg91':
                    return $this->sendViaMsg91($formattedPhone, $message);

                case 'twilio':
                    return $this->sendViaTwilio($formattedPhone, $message);

                case 'log':
                default:
                    return $this->sendViaLog($formattedPhone, $message);
            }

        } catch (\Exception $e) {
            $this->logger->error('General SMS sending failed', [
                'provider' => $this->provider,
                'phone' => $this->maskPhoneNumber($phoneNumber),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send via TextLocal (FREE 10 SMS for testing)
     */
    private function sendViaTextLocal(string $phone, string $message): array
    {
        try {
            $config = $this->config['textlocal'];
            
            if (empty($config['api_key'])) {
                throw new \Exception('TextLocal API key not configured');
            }

            $data = [
                'apikey' => $config['api_key'],
                'numbers' => $phone,
                'message' => $message,
                'sender' => $config['sender']
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $config['endpoint']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $result = json_decode($response, true);

            if ($httpCode === 200 && $result['status'] === 'success') {
                $this->logger->info('SMS sent via TextLocal', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'message_id' => $result['messages'][0]['id'] ?? null
                ]);

                return [
                    'success' => true,
                    'provider' => 'textlocal',
                    'message_id' => $result['messages'][0]['id'] ?? null
                ];
            } else {
                throw new \Exception('TextLocal API error: ' . ($result['errors'][0]['message'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'TextLocal: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send via Fast2SMS (FREE 50 SMS daily) - OTP API
     */
    private function sendViaFast2SMS(string $phone, string $otp, string $purpose = 'login'): array
    {
        try {
            $config = $this->config['fast2sms'];

            if (empty($config['api_key'])) {
                throw new \Exception('Fast2SMS API key not configured');
            }

            // Validate OTP is numeric
            if (!is_numeric($otp)) {
                throw new \Exception('OTP must be numeric');
            }

            // Remove country code from phone for Fast2SMS (they expect 10-digit Indian numbers)
            $cleanPhone = $phone;
            if (strlen($phone) === 12 && substr($phone, 0, 2) === '91') {
                $cleanPhone = substr($phone, 2);
            }

            // Fast2SMS DLT SMS API data (works without website verification)
            $message = "Your Wolffoxx login OTP is: {$otp}. Valid for 5 minutes. Do not share this code.";
            $data = [
                'message' => $message,
                'language' => 'english',
                'route' => 'q',             // Quick route (works without sender ID)
                'numbers' => $cleanPhone    // 10-digit phone number
            ];

            $headers = [
                'authorization: ' . $config['api_key'],
                'accept: */*',
                'cache-control: no-cache',
                'content-type: application/json'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $config['endpoint']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                throw new \Exception('cURL Error: ' . $curlError);
            }

            $result = json_decode($response, true);

            // Fast2SMS DLT API response format
            if ($httpCode === 200 && isset($result['return']) && $result['return'] === true) {
                $this->logger->info('OTP sent via Fast2SMS DLT', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'request_id' => $result['request_id'] ?? null,
                    'message' => 'OTP: ' . $otp . ' sent successfully'
                ]);

                return [
                    'success' => true,
                    'provider' => 'fast2sms',
                    'message_id' => $result['request_id'] ?? null,
                    'message' => 'OTP sent successfully'
                ];
            } else {
                // Log the full response for debugging
                $this->logger->error('Fast2SMS API error response', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'http_code' => $httpCode,
                    'response' => $response,
                    'parsed_result' => $result
                ]);

                $errorMessage = 'Unknown error';
                if (isset($result['message'])) {
                    $errorMessage = $result['message'];

                    // Provide user-friendly error messages for common issues
                    if (strpos($errorMessage, 'DND list') !== false) {
                        $errorMessage = 'Phone number is in DND (Do Not Disturb) list. Please try a different number or contact Fast2SMS support.';
                    } elseif (strpos($errorMessage, 'Invalid Authentication') !== false) {
                        $errorMessage = 'Invalid API key. Please check your Fast2SMS configuration.';
                    } elseif (strpos($errorMessage, 'Spamming detected') !== false) {
                        $errorMessage = 'Rate limit exceeded. Please wait a few minutes before trying again.';
                    }
                } elseif (isset($result['error'])) {
                    $errorMessage = $result['error'];
                } elseif (is_array($result) && !empty($result)) {
                    $errorMessage = json_encode($result);
                }

                throw new \Exception('Fast2SMS API error: ' . $errorMessage);
            }

        } catch (\Exception $e) {
            $this->logger->error('Fast2SMS sending failed', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Fast2SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send via Fast2SMS (General SMS - not OTP)
     */
    private function sendViaFast2SMSGeneral(string $phone, string $message): array
    {
        try {
            $config = $this->config['fast2sms'];

            if (empty($config['api_key'])) {
                throw new \Exception('Fast2SMS API key not configured');
            }

            // Remove country code from phone for Fast2SMS (they expect 10-digit Indian numbers)
            $cleanPhone = $phone;
            if (strlen($phone) === 12 && substr($phone, 0, 2) === '91') {
                $cleanPhone = substr($phone, 2);
            }

            // Fast2SMS general SMS API data
            $data = [
                'sender_id' => $config['sender_id'],
                'message' => $message,
                'language' => 'english',
                'route' => 'q',  // Quick route for general SMS
                'numbers' => $cleanPhone
            ];

            $headers = [
                'authorization: ' . $config['api_key'],
                'accept: */*',
                'cache-control: no-cache',
                'content-type: application/json'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $config['endpoint']);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_ENCODING, '');
            curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
            curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_1_1);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                throw new \Exception('cURL Error: ' . $curlError);
            }

            $result = json_decode($response, true);

            if ($httpCode === 200 && isset($result['return']) && $result['return'] === true) {
                $this->logger->info('General SMS sent via Fast2SMS', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'request_id' => $result['request_id'] ?? null
                ]);

                return [
                    'success' => true,
                    'provider' => 'fast2sms',
                    'message_id' => $result['request_id'] ?? null
                ];
            } else {
                // Log the full response for debugging
                $this->logger->error('Fast2SMS General SMS API error response', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'http_code' => $httpCode,
                    'response' => $response,
                    'parsed_result' => $result
                ]);

                $errorMessage = 'Unknown error';
                if (isset($result['message'])) {
                    $errorMessage = $result['message'];
                } elseif (isset($result['error'])) {
                    $errorMessage = $result['error'];
                } elseif (is_array($result) && !empty($result)) {
                    $errorMessage = json_encode($result);
                }

                throw new \Exception('Fast2SMS API error: ' . $errorMessage);
            }

        } catch (\Exception $e) {
            $this->logger->error('Fast2SMS general SMS sending failed', [
                'phone' => $this->maskPhoneNumber($phone),
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Fast2SMS: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send via MSG91 (FREE trial credits)
     */
    private function sendViaMsg91(string $phone, string $message): array
    {
        try {
            $config = $this->config['msg91'];
            
            if (empty($config['auth_key'])) {
                throw new \Exception('MSG91 auth key not configured');
            }

            $data = [
                'authkey' => $config['auth_key'],
                'mobiles' => $phone,
                'message' => $message,
                'sender' => $config['sender_id'],
                'route' => $config['route']
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $config['endpoint'] . '?' . http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $result = json_decode($response, true);

            if ($httpCode === 200 && isset($result['type']) && $result['type'] === 'success') {
                $this->logger->info('SMS sent via MSG91', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'message_id' => $result['message'] ?? null
                ]);

                return [
                    'success' => true,
                    'provider' => 'msg91',
                    'message_id' => $result['message'] ?? null
                ];
            } else {
                throw new \Exception('MSG91 API error: ' . ($result['message'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'MSG91: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send via Twilio (Paid but reliable)
     */
    private function sendViaTwilio(string $phone, string $message): array
    {
        try {
            $config = $this->config['twilio'];
            
            if (empty($config['account_sid']) || empty($config['auth_token'])) {
                throw new \Exception('Twilio credentials not configured');
            }

            $data = [
                'From' => $config['from_number'],
                'To' => '+' . $phone,
                'Body' => $message
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $config['endpoint'] . $config['account_sid'] . '/Messages.json');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_USERPWD, $config['account_sid'] . ':' . $config['auth_token']);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            $result = json_decode($response, true);

            if ($httpCode === 201) {
                $this->logger->info('SMS sent via Twilio', [
                    'phone' => $this->maskPhoneNumber($phone),
                    'message_sid' => $result['sid'] ?? null
                ]);

                return [
                    'success' => true,
                    'provider' => 'twilio',
                    'message_id' => $result['sid'] ?? null
                ];
            } else {
                throw new \Exception('Twilio API error: ' . ($result['message'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => 'Twilio: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Development mode - log instead of sending
     */
    private function sendViaLog(string $phone, string $message): array
    {
        $this->logger->info('SMS (Development Mode)', [
            'phone' => $this->maskPhoneNumber($phone),
            'message' => $message,
            'note' => 'This is development mode - SMS not actually sent'
        ]);

        return [
            'success' => true,
            'provider' => 'log',
            'message_id' => 'dev_' . time(),
            'note' => 'Development mode - SMS logged only'
        ];
    }

    /**
     * Generate OTP message
     */
    private function generateOTPMessage(string $otp, string $purpose): string
    {
        $brandName = $_ENV['APP_NAME'] ?? 'Wolffoxx';
        
        switch ($purpose) {
            case 'login':
                return "Your {$brandName} login OTP is: {$otp}. Valid for 5 minutes. Do not share this code.";
            
            case 'registration':
                return "Welcome to {$brandName}! Your verification OTP is: {$otp}. Valid for 5 minutes.";
            
            case 'password_reset':
                return "Your {$brandName} password reset OTP is: {$otp}. Valid for 5 minutes.";
            
            default:
                return "Your {$brandName} OTP is: {$otp}. Valid for 5 minutes.";
        }
    }

    /**
     * Format phone number for SMS
     */
    private function formatPhoneNumber(string $phone): string
    {
        // Remove all non-numeric characters
        $clean = preg_replace('/[^0-9]/', '', $phone);
        
        // Add country code if missing (assuming India +91)
        if (strlen($clean) === 10) {
            $clean = '91' . $clean;
        }
        
        return $clean;
    }

    /**
     * Mask phone number for logging
     */
    private function maskPhoneNumber(string $phone): string
    {
        $clean = $this->formatPhoneNumber($phone);
        if (strlen($clean) > 6) {
            return substr($clean, 0, 3) . '****' . substr($clean, -3);
        }
        return '****';
    }

    /**
     * Test SMS configuration
     */
    public function testConfiguration(): array
    {
        $results = [];
        
        foreach ($this->config as $provider => $config) {
            $results[$provider] = [
                'configured' => !empty(array_filter($config)),
                'config' => $config
            ];
        }

        return [
            'current_provider' => $this->provider,
            'providers' => $results,
            'recommendation' => $this->getRecommendation()
        ];
    }

    /**
     * Get provider recommendation
     */
    private function getRecommendation(): string
    {
        if (!empty($this->config['fast2sms']['api_key'])) {
            return 'Fast2SMS (50 free SMS daily)';
        } elseif (!empty($this->config['textlocal']['api_key'])) {
            return 'TextLocal (10 free SMS for testing)';
        } elseif (!empty($this->config['msg91']['auth_key'])) {
            return 'MSG91 (Free trial credits)';
        } else {
            return 'Development mode (logs only)';
        }
    }
}
