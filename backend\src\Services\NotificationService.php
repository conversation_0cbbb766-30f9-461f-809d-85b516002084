<?php

namespace Wolffoxx\Services;

use Wolffoxx\Models\Database;

/**
 * Notification Service
 * 
 * Handles SMS and Email notifications with queue support
 */
class NotificationService
{
    private string $smsApiKey;
    private string $smsApiUrl;
    private string $smtpHost;
    private string $smtpUsername;
    private string $smtpPassword;
    private int $smtpPort;

    public function __construct()
    {
        // SMS Configuration (using Fast2SMS or similar)
        $this->smsApiKey = $_ENV['SMS_API_KEY'] ?? '';
        $this->smsApiUrl = $_ENV['SMS_API_URL'] ?? 'https://fast2sms.com/dev/bulkV2';

        // Email Configuration
        $this->smtpHost = $_ENV['SMTP_HOST'] ?? 'smtp.gmail.com';
        $this->smtpUsername = $_ENV['SMTP_USERNAME'] ?? '';
        $this->smtpPassword = $_ENV['SMTP_PASSWORD'] ?? '';
        $this->smtpPort = (int)($_ENV['SMTP_PORT'] ?? 587);
    }

    /**
     * Send order confirmation SMS (async)
     */
    public function sendOrderConfirmationSMS(array $order, array $user): bool
    {
        try {
            $message = $this->generateOrderSMSMessage($order);
            
            // Queue SMS for async processing
            $this->queueSMS($user['phone'], $message, 'order_confirmation', $order['id']);
            
            return true;

        } catch (\Exception $e) {
            error_log('SMS queue failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send order confirmation email (async)
     */
    public function sendOrderConfirmationEmail(array $order, array $user, array $orderItems): bool
    {
        try {
            $subject = "Order Confirmation - {$order['order_number']}";
            $htmlContent = $this->generateOrderEmailHTML($order, $user, $orderItems);
            $textContent = $this->generateOrderEmailText($order, $user, $orderItems);

            // Queue email for async processing
            $this->queueEmail(
                $user['email'],
                $subject,
                $htmlContent,
                $textContent,
                'order_confirmation',
                $order['id']
            );

            return true;

        } catch (\Exception $e) {
            error_log('Email queue failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate tax invoice PDF and send via email
     */
    public function sendTaxInvoiceEmail(array $order, array $user, array $orderItems): bool
    {
        try {
            // Generate PDF invoice
            $pdfPath = $this->generateTaxInvoicePDF($order, $user, $orderItems);
            
            if (!$pdfPath) {
                error_log('Failed to generate tax invoice PDF');
                return false;
            }

            $subject = "Tax Invoice - {$order['order_number']}";
            $htmlContent = $this->generateInvoiceEmailHTML($order, $user);
            
            // Queue email with attachment
            $this->queueEmailWithAttachment(
                $user['email'],
                $subject,
                $htmlContent,
                $pdfPath,
                'tax_invoice',
                $order['id']
            );

            return true;

        } catch (\Exception $e) {
            error_log('Tax invoice email failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Queue SMS for async processing
     */
    private function queueSMS(string $phone, string $message, string $type, int $orderId): void
    {
        $sql = "INSERT INTO notification_queue (
            type, recipient, content, notification_type, order_id, status, created_at
        ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())";

        Database::execute($sql, ['sms', $phone, $message, $type, $orderId]);
    }

    /**
     * Queue email for async processing
     */
    private function queueEmail(string $email, string $subject, string $htmlContent, string $textContent, string $type, int $orderId): void
    {
        $emailData = [
            'subject' => $subject,
            'html' => $htmlContent,
            'text' => $textContent
        ];

        $sql = "INSERT INTO notification_queue (
            type, recipient, content, notification_type, order_id, status, created_at
        ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())";

        Database::execute($sql, ['email', $email, json_encode($emailData), $type, $orderId]);
    }

    /**
     * Queue email with attachment
     */
    private function queueEmailWithAttachment(string $email, string $subject, string $htmlContent, string $attachmentPath, string $type, int $orderId): void
    {
        $emailData = [
            'subject' => $subject,
            'html' => $htmlContent,
            'attachment' => $attachmentPath
        ];

        $sql = "INSERT INTO notification_queue (
            type, recipient, content, notification_type, order_id, status, created_at
        ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())";

        Database::execute($sql, ['email', $email, json_encode($emailData), $type, $orderId]);
    }

    /**
     * Generate order SMS message
     */
    private function generateOrderSMSMessage(array $order): string
    {
        $estimatedDelivery = $order['estimated_delivery_date'] 
            ? date('M j', strtotime($order['estimated_delivery_date']))
            : '3-5 days';

        return "Order confirmed! #{$order['order_number']} - ₹{$order['total_amount']}. " .
               "Estimated delivery: {$estimatedDelivery}. Track: wolffoxx.com/track/{$order['order_number']}";
    }

    /**
     * Generate order confirmation email HTML
     */
    private function generateOrderEmailHTML(array $order, array $user, array $orderItems): string
    {
        $itemsHTML = '';
        foreach ($orderItems as $item) {
            $itemTotal = $item['quantity'] * $item['unit_price'];
            $itemsHTML .= "
                <tr>
                    <td style='padding: 10px; border-bottom: 1px solid #eee;'>
                        {$item['product_name']}<br>
                        <small>Color: {$item['selected_color']}, Size: {$item['selected_size']}</small>
                    </td>
                    <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: center;'>{$item['quantity']}</td>
                    <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>₹{$item['unit_price']}</td>
                    <td style='padding: 10px; border-bottom: 1px solid #eee; text-align: right;'>₹{$itemTotal}</td>
                </tr>";
        }

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <title>Order Confirmation</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #3b82f6;'>Order Confirmation</h1>
                
                <p>Dear {$user['first_name']},</p>
                
                <p>Thank you for your order! Your order has been confirmed and is being processed.</p>
                
                <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <h3>Order Details</h3>
                    <p><strong>Order Number:</strong> {$order['order_number']}</p>
                    <p><strong>Order Date:</strong> " . date('M j, Y', strtotime($order['created_at'])) . "</p>
                    <p><strong>Total Amount:</strong> ₹{$order['total_amount']}</p>
                </div>
                
                <h3>Items Ordered</h3>
                <table style='width: 100%; border-collapse: collapse;'>
                    <thead>
                        <tr style='background: #f8f9fa;'>
                            <th style='padding: 10px; text-align: left;'>Product</th>
                            <th style='padding: 10px; text-align: center;'>Qty</th>
                            <th style='padding: 10px; text-align: right;'>Price</th>
                            <th style='padding: 10px; text-align: right;'>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        {$itemsHTML}
                    </tbody>
                </table>
                
                <div style='margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;'>
                    <h3>Shipping Address</h3>
                    <p>
                        {$order['shipping_address_line1']}<br>
                        " . ($order['shipping_address_line2'] ? $order['shipping_address_line2'] . '<br>' : '') . "
                        {$order['shipping_city']}, {$order['shipping_state']} {$order['shipping_postal_code']}<br>
                        {$order['shipping_country']}
                    </p>
                </div>
                
                <p style='margin-top: 30px;'>
                    You can track your order status at: 
                    <a href='https://wolffoxx.com/track/{$order['order_number']}'>Track Order</a>
                </p>
                
                <p>Thank you for shopping with Wolffoxx!</p>
                
                <hr style='margin: 30px 0;'>
                <p style='font-size: 12px; color: #666;'>
                    This is an automated email. Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>";
    }

    /**
     * Generate order confirmation email text version
     */
    private function generateOrderEmailText(array $order, array $user, array $orderItems): string
    {
        $itemsText = '';
        foreach ($orderItems as $item) {
            $itemTotal = $item['quantity'] * $item['unit_price'];
            $itemsText .= "- {$item['product_name']} (Color: {$item['selected_color']}, Size: {$item['selected_size']}) x{$item['quantity']} = ₹{$itemTotal}\n";
        }

        return "Order Confirmation\n\n" .
               "Dear {$user['first_name']},\n\n" .
               "Thank you for your order! Your order has been confirmed and is being processed.\n\n" .
               "Order Details:\n" .
               "Order Number: {$order['order_number']}\n" .
               "Order Date: " . date('M j, Y', strtotime($order['created_at'])) . "\n" .
               "Total Amount: ₹{$order['total_amount']}\n\n" .
               "Items Ordered:\n{$itemsText}\n" .
               "Shipping Address:\n" .
               "{$order['shipping_address_line1']}\n" .
               ($order['shipping_address_line2'] ? $order['shipping_address_line2'] . "\n" : '') .
               "{$order['shipping_city']}, {$order['shipping_state']} {$order['shipping_postal_code']}\n" .
               "{$order['shipping_country']}\n\n" .
               "Track your order: https://wolffoxx.com/track/{$order['order_number']}\n\n" .
               "Thank you for shopping with Wolffoxx!";
    }

    /**
     * Generate invoice email HTML
     */
    private function generateInvoiceEmailHTML(array $order, array $user): string
    {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='utf-8'>
            <title>Tax Invoice</title>
        </head>
        <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
            <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                <h1 style='color: #3b82f6;'>Tax Invoice</h1>
                
                <p>Dear {$user['first_name']},</p>
                
                <p>Please find attached the tax invoice for your order #{$order['order_number']}.</p>
                
                <div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;'>
                    <p><strong>Order Number:</strong> {$order['order_number']}</p>
                    <p><strong>Invoice Date:</strong> " . date('M j, Y') . "</p>
                    <p><strong>Total Amount:</strong> ₹{$order['total_amount']}</p>
                </div>
                
                <p>Thank you for your business!</p>
                
                <hr style='margin: 30px 0;'>
                <p style='font-size: 12px; color: #666;'>
                    This is an automated email. Please do not reply to this email.
                </p>
            </div>
        </body>
        </html>";
    }

    /**
     * Generate tax invoice PDF (simplified version)
     */
    private function generateTaxInvoicePDF(array $order, array $user, array $orderItems): ?string
    {
        // For now, return a placeholder path
        // In production, you would use a PDF library like TCPDF or DOMPDF
        $invoiceDir = __DIR__ . '/../../storage/invoices/';
        
        if (!is_dir($invoiceDir)) {
            mkdir($invoiceDir, 0755, true);
        }
        
        $filename = "invoice_{$order['order_number']}.pdf";
        $filepath = $invoiceDir . $filename;
        
        // Create a simple text-based invoice for now
        $invoiceContent = $this->generateInvoiceText($order, $user, $orderItems);
        file_put_contents($filepath, $invoiceContent);
        
        return $filepath;
    }

    /**
     * Generate invoice text content
     */
    private function generateInvoiceText(array $order, array $user, array $orderItems): string
    {
        $itemsText = '';
        foreach ($orderItems as $item) {
            $itemTotal = $item['quantity'] * $item['unit_price'];
            $itemsText .= "{$item['product_name']} x{$item['quantity']} = ₹{$itemTotal}\n";
        }

        return "TAX INVOICE\n\n" .
               "Wolffoxx Store\n" .
               "GST No: 29XXXXX1234X1ZX\n\n" .
               "Invoice No: {$order['order_number']}\n" .
               "Date: " . date('Y-m-d') . "\n\n" .
               "Bill To:\n" .
               "{$user['first_name']} {$user['last_name']}\n" .
               "{$order['billing_address_line1']}\n" .
               "{$order['billing_city']}, {$order['billing_state']} {$order['billing_postal_code']}\n\n" .
               "Items:\n{$itemsText}\n" .
               "Subtotal: ₹{$order['subtotal']}\n" .
               "Tax: ₹{$order['tax_amount']}\n" .
               "Total: ₹{$order['total_amount']}\n";
    }
}
