# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=wolffoxx_ecommerce
DB_USERNAME=root
DB_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=3600
JWT_REFRESH_EXPIRY=604800

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="Wolffoxx"

# OTP Configuration
OTP_LENGTH=6
OTP_EXPIRY=300
OTP_MAX_ATTEMPTS=3

# SMS Configuration (Choose one provider)
SMS_PROVIDER=fast2sms

# Fast2SMS (FREE 50 SMS daily) - Recommended for budget clients
FAST2SMS_API_KEY=your_fast2sms_api_key
FAST2SMS_SENDER_ID=WOLFFOXX

# TextLocal (FREE 10 SMS for testing)
TEXTLOCAL_API_KEY=your_textlocal_api_key
TEXTLOCAL_SENDER=WOLFFOXX

# MSG91 (FREE trial credits)
MSG91_AUTH_KEY=your_msg91_auth_key
MSG91_SENDER_ID=WOLFFOXX
MSG91_ROUTE=4

# Twilio (Paid but reliable)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Image Storage Configuration
GITHUB_IMAGES_REPO=username/wolffoxx-images
GITHUB_TOKEN=your_github_token

# Cloudinary (FREE 25GB)
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name

# Application Configuration
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173

# File Upload Configuration
UPLOAD_MAX_SIZE=5242880
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,gif,webp

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Security
BCRYPT_ROUNDS=12
SESSION_LIFETIME=86400

# API Configuration
API_VERSION=v1
API_PREFIX=/api/v1

# Logging
LOG_LEVEL=debug
LOG_FILE=storage/logs/app.log
