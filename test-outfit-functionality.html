<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Outfit Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Outfit Functionality Test</h1>
    
    <div class="test-section">
        <h2>1. Test Backend API Connection</h2>
        <button class="test-button" onclick="testBackendConnection()">Test Backend</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Outfit Creation</h2>
        <button class="test-button" onclick="testOutfitCreation()">Create Test Outfit</button>
        <div id="outfit-creation-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Outfit Fetching</h2>
        <button class="test-button" onclick="testOutfitFetching()">Fetch Outfits</button>
        <div id="outfit-fetch-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Add Item to Outfit</h2>
        <button class="test-button" onclick="testAddItemToOutfit()">Add Item to Outfit</button>
        <div id="add-item-result" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        async function testBackendConnection() {
            const resultDiv = document.getElementById('backend-result');
            try {
                const response = await fetch(`${API_BASE}/products?limit=1`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Backend connected successfully! Found ${data.data?.pagination?.total || 0} products.`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Backend connection failed: ${error.message}`;
            }
        }

        async function testOutfitCreation() {
            const resultDiv = document.getElementById('outfit-creation-result');
            try {
                const outfitData = {
                    name: `Test Outfit ${Date.now()}`,
                    description: 'Test outfit created from functionality test',
                    occasion: 'casual',
                    season: 'all',
                    is_public: false,
                    items: [
                        {
                            product_id: 1,
                            selected_color: 'Black',
                            selected_size: 'M',
                            category_type: 'top',
                            is_primary: false
                        }
                    ]
                };

                const response = await fetch(`${API_BASE}/outfits`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(outfitData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Outfit created successfully! ID: ${data.data?.outfit?.id}, Name: "${data.data?.outfit?.name}"`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Outfit creation failed: ${error.message}`;
            }
        }

        async function testOutfitFetching() {
            const resultDiv = document.getElementById('outfit-fetch-result');
            try {
                const response = await fetch(`${API_BASE}/outfits`);
                const data = await response.json();
                
                if (response.ok) {
                    const outfits = data.data?.outfits || [];
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Fetched ${outfits.length} outfits successfully!<br>`;
                    
                    if (outfits.length > 0) {
                        const firstOutfit = outfits[0];
                        resultDiv.innerHTML += `<br>First outfit: "${firstOutfit.name}" with ${firstOutfit.items?.length || 0} items, Total: $${firstOutfit.total_sale_price || firstOutfit.total_price || '0.00'}`;
                        
                        if (firstOutfit.items && firstOutfit.items.length > 0) {
                            const firstItem = firstOutfit.items[0];
                            resultDiv.innerHTML += `<br>First item: "${firstItem.product_name}" (${firstItem.selected_color}, ${firstItem.selected_size})`;
                            if (firstItem.product_image) {
                                resultDiv.innerHTML += `<br>Has image: ${firstItem.product_image}`;
                            }
                        }
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Outfit fetching failed: ${error.message}`;
            }
        }

        async function testAddItemToOutfit() {
            const resultDiv = document.getElementById('add-item-result');
            try {
                // First get outfits to find one to add to
                const outfitsResponse = await fetch(`${API_BASE}/outfits`);
                const outfitsData = await outfitsResponse.json();
                
                if (!outfitsResponse.ok || !outfitsData.data?.outfits?.length) {
                    throw new Error('No outfits found to add item to. Create an outfit first.');
                }

                const outfitId = outfitsData.data.outfits[0].id;
                
                const itemData = {
                    product_id: 2, // Different product
                    selected_color: 'White',
                    selected_size: 'L',
                    category_type: 'top',
                    is_primary: false
                };

                const response = await fetch(`${API_BASE}/outfits/${outfitId}/items`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(itemData)
                });

                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Item added to outfit successfully!`;
                } else {
                    throw new Error(`HTTP ${response.status}: ${data.message || 'Unknown error'}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Add item to outfit failed: ${error.message}`;
            }
        }

        // Auto-test backend connection on page load
        window.onload = function() {
            testBackendConnection();
        };
    </script>
</body>
</html>
