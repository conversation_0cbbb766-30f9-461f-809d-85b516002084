/**
 * Performance Monitoring Service
 * Tracks and optimizes website performance metrics
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      cumulativeLayoutShift: 0,
      firstInputDelay: 0,
      timeToInteractive: 0
    };
    
    this.observers = [];
    this.isMonitoring = false;
    
    // Initialize monitoring if browser supports it
    if (typeof window !== 'undefined' && 'performance' in window) {
      this.initializeMonitoring();
    }
  }

  /**
   * Initialize performance monitoring
   */
  initializeMonitoring() {
    if (this.isMonitoring) return;
    this.isMonitoring = true;

    // Monitor Core Web Vitals
    this.observeLCP();
    this.observeFID();
    this.observeCLS();
    this.observeFCP();
    
    // Monitor page load
    this.observePageLoad();
    
    // Monitor resource loading
    this.observeResourceTiming();
  }

  /**
   * Observe Largest Contentful Paint (LCP)
   */
  observeLCP() {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.metrics.largestContentfulPaint = lastEntry.startTime;
      
      // Log warning if LCP is poor (> 2.5s)
      if (lastEntry.startTime > 2500) {
        console.warn(`Poor LCP: ${lastEntry.startTime}ms. Target: <2500ms`);
        this.suggestLCPOptimizations();
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint'] });
    this.observers.push(observer);
  }

  /**
   * Observe First Input Delay (FID)
   */
  observeFID() {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        this.metrics.firstInputDelay = entry.processingStart - entry.startTime;
        
        // Log warning if FID is poor (> 100ms)
        if (this.metrics.firstInputDelay > 100) {
          console.warn(`Poor FID: ${this.metrics.firstInputDelay}ms. Target: <100ms`);
          this.suggestFIDOptimizations();
        }
      });
    });

    observer.observe({ entryTypes: ['first-input'] });
    this.observers.push(observer);
  }

  /**
   * Observe Cumulative Layout Shift (CLS)
   */
  observeCLS() {
    if (!('PerformanceObserver' in window)) return;

    let clsValue = 0;
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.metrics.cumulativeLayoutShift = clsValue;
        }
      });

      // Log warning if CLS is poor (> 0.1)
      if (clsValue > 0.1) {
        console.warn(`Poor CLS: ${clsValue}. Target: <0.1`);
        this.suggestCLSOptimizations();
      }
    });

    observer.observe({ entryTypes: ['layout-shift'] });
    this.observers.push(observer);
  }

  /**
   * Observe First Contentful Paint (FCP)
   */
  observeFCP() {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        if (entry.name === 'first-contentful-paint') {
          this.metrics.firstContentfulPaint = entry.startTime;
          
          // Log warning if FCP is poor (> 1.8s)
          if (entry.startTime > 1800) {
            console.warn(`Poor FCP: ${entry.startTime}ms. Target: <1800ms`);
          }
        }
      });
    });

    observer.observe({ entryTypes: ['paint'] });
    this.observers.push(observer);
  }

  /**
   * Observe page load timing
   */
  observePageLoad() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        if (navigation) {
          this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
          this.metrics.timeToInteractive = navigation.domInteractive - navigation.fetchStart;
          
          // Log performance summary
          this.logPerformanceSummary();
        }
      }, 0);
    });
  }

  /**
   * Observe resource timing for optimization opportunities
   */
  observeResourceTiming() {
    if (!('PerformanceObserver' in window)) return;

    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      entries.forEach((entry) => {
        // Check for slow images
        if (entry.initiatorType === 'img' && entry.duration > 1000) {
          console.warn(`Slow image loading: ${entry.name} took ${entry.duration}ms`);
        }
        
        // Check for large resources
        if (entry.transferSize > 1000000) { // > 1MB
          console.warn(`Large resource: ${entry.name} is ${(entry.transferSize / 1024 / 1024).toFixed(2)}MB`);
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });
    this.observers.push(observer);
  }

  /**
   * Log performance summary
   */
  logPerformanceSummary() {
    console.group('🚀 Performance Summary');
    console.log(`Page Load Time: ${this.metrics.pageLoadTime}ms`);
    console.log(`First Contentful Paint: ${this.metrics.firstContentfulPaint}ms`);
    console.log(`Largest Contentful Paint: ${this.metrics.largestContentfulPaint}ms`);
    console.log(`Time to Interactive: ${this.metrics.timeToInteractive}ms`);
    console.log(`Cumulative Layout Shift: ${this.metrics.cumulativeLayoutShift}`);
    console.log(`First Input Delay: ${this.metrics.firstInputDelay}ms`);
    console.groupEnd();
  }

  /**
   * Suggest LCP optimizations
   */
  suggestLCPOptimizations() {
    console.group('💡 LCP Optimization Suggestions');
    console.log('• Optimize images with WebP format and proper sizing');
    console.log('• Preload critical resources');
    console.log('• Reduce server response times');
    console.log('• Eliminate render-blocking resources');
    console.groupEnd();
  }

  /**
   * Suggest FID optimizations
   */
  suggestFIDOptimizations() {
    console.group('💡 FID Optimization Suggestions');
    console.log('• Break up long JavaScript tasks');
    console.log('• Use code splitting and lazy loading');
    console.log('• Optimize third-party scripts');
    console.log('• Use web workers for heavy computations');
    console.groupEnd();
  }

  /**
   * Suggest CLS optimizations
   */
  suggestCLSOptimizations() {
    console.group('💡 CLS Optimization Suggestions');
    console.log('• Set explicit dimensions for images and videos');
    console.log('• Reserve space for dynamic content');
    console.log('• Avoid inserting content above existing content');
    console.log('• Use CSS transforms instead of changing layout properties');
    console.groupEnd();
  }

  /**
   * Get current metrics
   */
  getMetrics() {
    return { ...this.metrics };
  }

  /**
   * Cleanup observers
   */
  cleanup() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.isMonitoring = false;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions
export const startPerformanceMonitoring = () => performanceMonitor.initializeMonitoring();
export const getPerformanceMetrics = () => performanceMonitor.getMetrics();
export const stopPerformanceMonitoring = () => performanceMonitor.cleanup();

export default performanceMonitor;
