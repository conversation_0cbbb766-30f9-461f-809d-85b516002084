import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import logo45 from '../assets/logo30.svg';

const LoadingAnimation = ({ onComplete }) => {
  const [showContent, setShowContent] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const [startExit, setStartExit] = useState(false);

  useEffect(() => {
    // Prevent scrolling during animation
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // Show content after initial delay
    const contentTimer = setTimeout(() => setShowContent(true), 400);

    // Auto-complete after showing the brand for a while
    const completeTimer = setTimeout(() => {
      setStartExit(true);
      // Complete the animation after the slide-up
      setTimeout(() => {
        setIsComplete(true);
        // Re-enable scrolling after animation completes
        setTimeout(() => {
          document.body.style.overflow = 'unset';
          document.documentElement.style.overflow = 'unset';
          onComplete?.();
        }, 500);
      }, 1800); // Duration of slide-up animation
    }, 3100); // Show brand for 3.1 seconds (reduced by 0.4 seconds from original)

    return () => {
      clearTimeout(contentTimer);
      clearTimeout(completeTimer);
      // Ensure scrolling is re-enabled on cleanup
      document.body.style.overflow = 'unset';
      document.documentElement.style.overflow = 'unset';
    };
  }, [onComplete]);

  // Custom easing curve
  const customEasing = [0.16, 1, 0.3, 1];

  return (
    <AnimatePresence mode="wait">
      {!isComplete && (
        <motion.div
          initial={{ y: 0 }}
          animate={{
            y: startExit ? '-100vh' : 0,
          }}
          exit={{ y: '-100vh' }}
          transition={{
            duration: 1.8,
            ease: customEasing,
            type: "tween"
          }}
          className="fixed inset-0 z-[9999] flex flex-col items-center justify-center overflow-hidden"
          style={{
            backgroundColor: '#000000',
            touchAction: 'none',
            userSelect: 'none',
            WebkitUserSelect: 'none',
            MozUserSelect: 'none',
            msUserSelect: 'none'
          }}
        >
          {/* Enhanced Background with Subtle Effects */}
          <div className="absolute inset-0">
            {/* Subtle noise/grain effect */}
            <div
              className="absolute inset-0 opacity-[0.015]"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
              }}
            />

            {/* Enhanced gradient overlay for depth */}
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#1a1a1a]/5 to-transparent" />
            <div className="absolute inset-0 bg-gradient-radial from-[#2a2a2a]/3 via-transparent to-transparent" />

            {/* More prominent ambient elements - Mobile Optimized */}
            <motion.div
              animate={{
                scale: [1, 1.08, 1],
                opacity: [0.12, 0.25, 0.12]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="absolute top-1/4 left-1/6 w-72 h-72 sm:w-96 sm:h-96 md:w-[500px] md:h-[500px] lg:w-[600px] lg:h-[600px] bg-gradient-to-r from-[#2a2a2a]/8 to-[#404040]/8 rounded-full blur-3xl"
            />
            <motion.div
              animate={{
                scale: [1.08, 1, 1.08],
                opacity: [0.25, 0.12, 0.25]
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 4
              }}
              className="absolute bottom-1/4 right-1/6 w-72 h-72 sm:w-96 sm:h-96 md:w-[500px] md:h-[500px] lg:w-[600px] lg:h-[600px] bg-gradient-to-r from-[#404040]/8 to-[#2a2a2a]/8 rounded-full blur-3xl"
            />
          </div>

          {/* Main Brand Container - Mobile Optimized */}
          <div className="relative z-10 flex flex-col items-center px-4 w-full max-w-screen-xl mx-auto">

            {/* Logo Section - Mobile Responsive */}
            <motion.div
              initial={{ opacity: 0, y: 60, scale: 0.8 }}
              animate={{
                opacity: showContent ? 1 : 0,
                y: showContent ? 0 : 60,
                scale: showContent ? 1 : 0.8,
                // Logo slides up with the background
                y: startExit ? -80 : (showContent ? 0 : 60)
              }}
              transition={{
                opacity: { duration: 1.2, ease: customEasing },
                y: startExit
                  ? { duration: 1.8, ease: customEasing }
                  : { duration: 1.2, ease: customEasing, delay: 0.3 },
                scale: { duration: 1.2, ease: customEasing, delay: 0.2 }
              }}
              className="mb-2 sm:mb-6 md:mb-8 lg:mb-10"
            >
              {/* Logo Container - Mobile First Sizing */}
              <motion.div
                animate={{
                  scale: [1, 1.05, 1],
                  filter: ["brightness(1)", "brightness(1.1)", "brightness(1)"]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="w-24 h-24 xs:w-28 xs:h-28 sm:w-36 sm:h-36 md:w-44 md:h-44 lg:w-52 lg:h-52 xl:w-60 xl:h-60 2xl:w-72 2xl:h-72 mb-6 sm:mb-8 flex items-center justify-center"
                style={{
                  filter: 'drop-shadow(0 0 15px rgba(106, 106, 106, 0.1))'
                }}
              >
                {/* Logo45.svg from assets */}
                <img
                  src={logo45}
                  alt="Wolffoxx Logo"
                  className="w-full h-full object-contain"
                  style={{
                    filter: 'brightness(1.1) contrast(1.1)'
                  }}
                />
              </motion.div>
            </motion.div>

            {/* Enhanced Brand Text - Mobile Optimized */}
            <motion.div
              initial={{ opacity: 0, y: 40, scale: 0.9 }}
              animate={{
                opacity: showContent ? 1 : 0,
                y: showContent ? 0 : 40,
                scale: showContent ? 1 : 0.9,
                // Text slides up with the background
                y: startExit ? -60 : (showContent ? 0 : 40)
              }}
              transition={{
                opacity: { duration: 1.0, ease: customEasing },
                y: startExit
                  ? { duration: 1.8, ease: customEasing }
                  : { duration: 1.0, ease: customEasing, delay: 0.5 },
                scale: { duration: 1.0, ease: customEasing, delay: 0.4 }
              }}
              className="text-center w-full"
            >
              <motion.h1
                animate={{
                  letterSpacing: showContent ? "0.3em" : "0.2em", // Reduced for mobile
                  textShadow: [
                    "0 0 0px rgba(106, 106, 106, 0)",
                    "0 0 20px rgba(106, 106, 106, 0.08)",
                    "0 0 0px rgba(106, 106, 106, 0)"
                  ]
                }}
                transition={{
                  letterSpacing: { duration: 1.2, ease: customEasing, delay: 0.8 },
                  textShadow: { duration: 3, repeat: Infinity, ease: "easeInOut" }
                }}
                className="text-white text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl xl:text-9xl 2xl:text-10xl font-light px-2 sm:px-4 tracking-[0.2em] sm:tracking-[0.3em] mb-4 sm:mb-6 leading-tight"
                style={{
                  fontFamily: "'Inter', 'Helvetica Neue', sans-serif",
                  fontWeight: 100,
                  textRendering: 'optimizeLegibility',
                  wordBreak: 'keep-all',
                  whiteSpace: 'nowrap'
                }}
              >
                {"WOLFFOXX".split("").map((letter, index) => (
                  <motion.span
                    key={index}
                    initial={{ opacity: 0, y: 30, filter: 'blur(4px)' }}
                    animate={{
                      opacity: showContent ? 1 : 0,
                      y: showContent ? 0 : 30,
                      filter: showContent ? 'blur(0px)' : 'blur(4px)'
                    }}
                    transition={{
                      delay: 0.7 + index * 0.08, // Slightly faster on mobile
                      duration: 0.6,
                      ease: customEasing
                    }}
                    className="inline-block"
                  >
                    {letter}
                  </motion.span>
                ))}
              </motion.h1>

              {/* Optional tagline or subtitle - Mobile Responsive */}
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{
                  opacity: showContent ? 0.7 : 0,
                  y: showContent ? 0 : 15,
                  y: startExit ? -25 : (showContent ? 0 : 15)
                }}
                transition={{
                  opacity: { delay: 1.5, duration: 0.8, ease: customEasing },
                  y: startExit
                    ? { duration: 1.8, ease: customEasing }
                    : { delay: 1.5, duration: 0.8, ease: customEasing }
                }}
                className="text-white/60 text-xs xs:text-sm sm:text-base md:text-lg font-light tracking-[0.15em] sm:tracking-[0.2em] uppercase px-4"
                style={{ fontFamily: "'Inter', sans-serif" }}
              >
                {/* Add your tagline here if needed */}
                {/* <span>Your Tagline Here</span> */}
              </motion.div>
            </motion.div>
          </div>

          {/* Minimalist Bottom Indicator */}
          <motion.div
            className="absolute bottom-12 left-1/2 transform -translate-x-1/2"
            animate={{
              y: startExit ? 40 : 0,
              opacity: startExit ? 0 : 1
            }}
            transition={{
              duration: 1.2,
              ease: customEasing
            }}
          >
            <motion.div
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.7, 0.3],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              className="w-2 h-2 bg-[#6a6a6a]/30 rounded-full"
            />
          </motion.div>

        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingAnimation;