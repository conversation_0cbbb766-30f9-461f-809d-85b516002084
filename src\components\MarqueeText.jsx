import Marquee from 'react-fast-marquee';

export default function MarqueeText({ 
  text, 
  speed = 40, 
  className = '', 
  reverse = false,
  separator = '•',
  repeat = 4
}) {
  const repeatedText = Array(repeat).fill(text).join(` ${separator} `);
  
  return (
    <Marquee
      speed={speed}
      direction={reverse ? 'right' : 'left'}
      gradient={false}
      className={`py-3 bg-gray-900 overflow-hidden ${className}`}
    >
      <div className="text-gray-300 font-medium whitespace-nowrap px-4">
        {repeatedText}
      </div>
    </Marquee>
  );
}