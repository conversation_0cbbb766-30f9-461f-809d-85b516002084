<?php
// Database connection
$conn = new mysqli('localhost', 'root', '', 'wolffoxx');

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Query to get products on sale
$sql = "SELECT id, name, price, sale_price, is_sale FROM products WHERE is_sale = 1";
$result = $conn->query($sql);

echo "Checking products on sale...\n";

if ($result->num_rows > 0) {
    echo "Found " . $result->num_rows . " products on sale:\n";
    
    // Output data of each row
    while($row = $result->fetch_assoc()) {
        echo "ID: " . $row["id"] . ", Name: " . $row["name"] . 
             ", Price: " . $row["price"] . ", Sale Price: " . $row["sale_price"] . 
             ", Is Sale: " . $row["is_sale"] . "\n";
    }
} else {
    echo "No products on sale found\n";
    
    // Let's check if there are any products at all
    $sql = "SELECT COUNT(*) as total FROM products";
    $result = $conn->query($sql);
    $row = $result->fetch_assoc();
    echo "Total products in database: " . $row["total"] . "\n";
    
    // Check the structure of the products table
    echo "\nTable structure:\n";
    $sql = "DESCRIBE products";
    $result = $conn->query($sql);
    while($row = $result->fetch_assoc()) {
        echo $row["Field"] . " - " . $row["Type"] . "\n";
    }
}

// Close connection
$conn->close();
?>