💳 RAZORPAY PAYMENT INTEGRATION GUIDE FOR WOLFFOXX ECOMMERCE
================================================================

📋 OVERVIEW:
- Complete Razorpay payment gateway integration
- Secure payment processing with signature verification
- Order creation after successful payment
- Payment failure handling
- Test mode for development, live mode for production

🚀 STEP 1: RAZORPAY ACCOUNT SETUP
=================================

1. Go to: https://razorpay.com/
2. Sign up for a business account
3. Complete KYC verification (for live mode)
4. Get your API credentials:

**Test Mode Credentials:**
- Key ID: rzp_test_xxxxxxxxxx
- Key Secret: xxxxxxxxxxxxxxxxxx

**Live Mode Credentials (after KYC):**
- Key ID: rzp_live_xxxxxxxxxx  
- Key Secret: xxxxxxxxxxxxxxxxxx

🔧 STEP 2: ENVIRONMENT CONFIGURATION
====================================

Create/Update: backend/.env
```
# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_your_key_id
RAZORPAY_KEY_SECRET=your_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret

# For production, use live keys:
# RAZORPAY_KEY_ID=rzp_live_your_key_id
# RAZORPAY_KEY_SECRET=your_live_key_secret
```

🛠️ STEP 3: FRONTEND INTEGRATION
================================

Add Razorpay script to your frontend:

```html
<!-- Add to your HTML head -->
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
```

Frontend payment flow:
```javascript
// 1. Create Razorpay order
const createOrder = async (orderData) => {
    const response = await fetch('/api/v1/payments/create-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({
            amount: orderData.total * 100, // Convert to paise
            currency: 'INR'
        })
    });
    return response.json();
};

// 2. Open Razorpay checkout
const initiatePayment = async (orderData) => {
    try {
        const razorpayOrder = await createOrder(orderData);
        
        const options = {
            key: razorpayOrder.data.key_id,
            amount: razorpayOrder.data.amount,
            currency: razorpayOrder.data.currency,
            name: 'Wolffoxx Store',
            description: 'Order Payment',
            order_id: razorpayOrder.data.razorpay_order_id,
            handler: function(response) {
                verifyPayment(response, orderData);
            },
            prefill: {
                name: user.name,
                email: user.email,
                contact: user.phone
            },
            theme: {
                color: '#3b82f6'
            },
            modal: {
                ondismiss: function() {
                    handlePaymentFailure('Payment cancelled by user');
                }
            }
        };
        
        const rzp = new Razorpay(options);
        rzp.open();
        
    } catch (error) {
        console.error('Payment initiation failed:', error);
        handlePaymentFailure('Failed to initiate payment');
    }
};

// 3. Verify payment and create order
const verifyPayment = async (paymentResponse, orderData) => {
    try {
        const response = await fetch('/api/v1/payments/verify', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${userToken}`
            },
            body: JSON.stringify({
                razorpay_payment_id: paymentResponse.razorpay_payment_id,
                razorpay_order_id: paymentResponse.razorpay_order_id,
                razorpay_signature: paymentResponse.razorpay_signature,
                order_data: orderData
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Payment successful - redirect to success page
            window.location.href = `/order-success/${result.data.order_id}`;
        } else {
            handlePaymentFailure(result.error);
        }
        
    } catch (error) {
        console.error('Payment verification failed:', error);
        handlePaymentFailure('Payment verification failed');
    }
};

// 4. Handle payment failure
const handlePaymentFailure = async (error) => {
    await fetch('/api/v1/payments/failure', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({ error })
    });
    
    // Show error message and redirect to cart
    alert('Payment failed: ' + error);
    window.location.href = '/cart';
};
```

📋 STEP 4: PAYMENT FLOW
========================

**Complete Payment Process:**

1. **User clicks "Pay Now"**
   - Frontend calls `/api/v1/payments/create-order`
   - Backend creates Razorpay order
   - Returns order ID and amount

2. **Razorpay Checkout Opens**
   - User enters payment details
   - Razorpay processes payment
   - Returns payment response

3. **Payment Verification**
   - Frontend calls `/api/v1/payments/verify`
   - Backend verifies signature
   - Creates order in database
   - Clears user's cart

4. **Success/Failure Handling**
   - Success: Redirect to order confirmation
   - Failure: Log error and redirect to cart

🔒 STEP 5: SECURITY FEATURES
============================

**Implemented Security Measures:**

1. **Signature Verification**
   - Every payment is verified using HMAC SHA256
   - Prevents payment tampering

2. **Amount Validation**
   - Backend validates payment amount matches cart total
   - Prevents amount manipulation

3. **User Authentication**
   - All payment endpoints require valid JWT token
   - Prevents unauthorized payments

4. **Order Integrity**
   - Orders created only after successful payment
   - Cart cleared only after order creation

💰 STEP 6: TESTING
===================

**Test Cards (Razorpay Test Mode):**

```
Success Card:
Card Number: 4111 1111 1111 1111
CVV: Any 3 digits
Expiry: Any future date

Failure Card:
Card Number: 4000 0000 0000 0002
CVV: Any 3 digits
Expiry: Any future date

UPI Testing:
UPI ID: success@razorpay
UPI ID: failure@razorpay
```

**Test Payment Flow:**
1. Add products to cart
2. Proceed to checkout
3. Use test card details
4. Verify order creation
5. Check payment status

🚀 STEP 7: PRODUCTION DEPLOYMENT
=================================

**Before Going Live:**

1. **Complete KYC** on Razorpay dashboard
2. **Update credentials** to live keys
3. **Test with small amounts** first
4. **Set up webhooks** for payment status updates
5. **Enable required payment methods**

**Webhook Setup (Optional but Recommended):**
```
Webhook URL: https://yourdomain.com/api/v1/webhooks/razorpay
Events: payment.captured, payment.failed, order.paid
```

📊 STEP 8: MONITORING & ANALYTICS
==================================

**Razorpay Dashboard provides:**
- Real-time payment analytics
- Success/failure rates
- Settlement reports
- Refund management
- Customer payment behavior

**Backend Logging:**
- All payment attempts logged
- Failed payments tracked
- Order creation monitored

🎯 STEP 9: SUPPORTED PAYMENT METHODS
====================================

**Razorpay supports:**
- Credit/Debit Cards (Visa, Mastercard, RuPay)
- Net Banking (50+ banks)
- UPI (Google Pay, PhonePe, Paytm)
- Wallets (Paytm, Mobikwik, etc.)
- EMI options
- International cards

💡 STEP 10: BEST PRACTICES
===========================

1. **Always verify payments** on backend
2. **Use HTTPS** for all payment pages
3. **Store minimal payment data** (never store card details)
4. **Implement retry logic** for failed API calls
5. **Set up proper error handling**
6. **Monitor payment success rates**
7. **Keep Razorpay SDK updated**

🚀 READY FOR PAYMENTS!
=======================

Your payment system includes:
✅ Secure Razorpay integration
✅ Payment verification
✅ Order creation after payment
✅ Failure handling
✅ Test mode support
✅ Production ready

**Total setup time: ~2 hours**
**Go live time: After KYC approval (1-2 days)**
