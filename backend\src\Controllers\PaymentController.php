<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Order;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\User;
use Wolffoxx\Services\NotificationService;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Auth;

/**
 * Payment Controller
 * 
 * Handles Razorpay payment integration
 */
class PaymentController
{
    private Order $orderModel;
    private Cart $cartModel;
    private User $userModel;
    private NotificationService $notificationService;
    private string $razorpayKeyId;
    private string $razorpayKeySecret;

    public function __construct()
    {
        $this->orderModel = new Order();
        $this->cartModel = new Cart();
        $this->userModel = new User();
        $this->notificationService = new NotificationService();

        // Razorpay credentials (move to environment variables)
        $this->razorpayKeyId = $_ENV['RAZORPAY_KEY_ID'] ?? 'rzp_test_your_key_id';
        $this->razorpayKeySecret = $_ENV['RAZORPAY_KEY_SECRET'] ?? 'your_key_secret';
    }

    /**
     * Create Razorpay order
     * POST /api/v1/payments/create-order
     */
    public function createRazorpayOrder(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate required fields
            if (empty($input['amount']) || empty($input['currency'])) {
                Response::error('Amount and currency are required', 400);
                return;
            }

            // Get user's cart to validate amount
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                Response::error('Cart is empty', 400);
                return;
            }

            // Validate amount matches cart total
            $expectedAmount = (int)($cart['total_amount'] * 100); // Convert to paise
            if ((int)$input['amount'] !== $expectedAmount) {
                Response::error('Amount mismatch', 400);
                return;
            }

            // Create Razorpay order
            $razorpayOrder = $this->createRazorpayOrderAPI([
                'amount' => $input['amount'],
                'currency' => $input['currency'],
                'receipt' => 'order_' . time(),
                'notes' => [
                    'user_id' => $user['id'],
                    'cart_total' => $cart['total_amount']
                ]
            ]);

            if ($razorpayOrder) {
                Response::success([
                    'razorpay_order_id' => $razorpayOrder['id'],
                    'amount' => $razorpayOrder['amount'],
                    'currency' => $razorpayOrder['currency'],
                    'key_id' => $this->razorpayKeyId
                ]);
            } else {
                Response::error('Failed to create payment order', 500);
            }

        } catch (\Exception $e) {
            error_log('Create Razorpay order failed: ' . $e->getMessage());
            Response::error('Payment order creation failed', 500);
        }
    }

    /**
     * Verify payment and create order
     * POST /api/v1/payments/verify
     */
    public function verifyPayment(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Validate required fields
            $required = ['razorpay_payment_id', 'razorpay_order_id', 'razorpay_signature', 'order_data'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    Response::error("Missing required field: {$field}", 400);
                    return;
                }
            }

            // Verify payment signature
            $isValidSignature = $this->verifyRazorpaySignature(
                $input['razorpay_order_id'],
                $input['razorpay_payment_id'],
                $input['razorpay_signature']
            );

            if (!$isValidSignature) {
                Response::error('Invalid payment signature', 400);
                return;
            }

            // Get user's cart
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                Response::error('Cart is empty', 400);
                return;
            }

            // Create order with payment details
            $orderData = $input['order_data'];
            $orderData['user_id'] = $user['id'];
            $orderData['customer_email'] = $user['email'];
            $orderData['customer_phone'] = $user['phone'];
            $orderData['customer_name'] = $orderData['customer_name'] ?? $user['name'] ?? '';
            $orderData['payment_method'] = 'razorpay';
            $orderData['subtotal'] = $cart['subtotal'];
            $orderData['tax_amount'] = $cart['tax_amount'] ?? 0;
            $orderData['discount_amount'] = $cart['discount_amount'] ?? 0;
            $orderData['total_amount'] = $cart['total_amount'];
            $orderData['coupon_code'] = $cart['coupon_code'] ?? null;

            $orderId = $this->orderModel->createOrder($orderData, $cart['items']);

            if ($orderId) {
                // Update order with payment details
                $this->orderModel->updatePaymentStatus($orderId, 'paid', [
                    'payment_id' => $input['razorpay_payment_id'],
                    'signature' => $input['razorpay_signature']
                ]);

                // Update order status to confirmed
                $this->orderModel->updateOrderStatus($orderId, 'confirmed');

                // Clear cart
                $this->cartModel->clearCart($user['id']);

                // Get created order details
                $order = $this->orderModel->getOrderById($orderId);
                $orderItems = $this->orderModel->getOrderItems($orderId);
                $userDetails = $this->userModel->findById($user['id']);

                // Send notifications asynchronously (only after payment is confirmed)
                $this->sendOrderNotifications($order, $userDetails, $orderItems);

                Response::success([
                    'order_id' => $orderId,
                    'order_number' => $order['order_number'],
                    'payment_status' => 'paid',
                    'total_amount' => $order['total_amount'],
                    'message' => 'Payment successful and order created'
                ]);
            } else {
                Response::error('Failed to create order after payment', 500);
            }

        } catch (\Exception $e) {
            error_log('Payment verification failed: ' . $e->getMessage());
            Response::error('Payment verification failed', 500);
        }
    }

    /**
     * Handle payment failure
     * POST /api/v1/payments/failure
     */
    public function handlePaymentFailure(): void
    {
        try {
            $user = Auth::getCurrentUser();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Log payment failure
            error_log('Payment failed for user: ' . $user['id'] . ', Error: ' . json_encode($input));

            Response::success([
                'message' => 'Payment failure recorded',
                'redirect_to_cart' => true
            ]);

        } catch (\Exception $e) {
            error_log('Handle payment failure error: ' . $e->getMessage());
            Response::error('Failed to handle payment failure', 500);
        }
    }

    /**
     * Get payment status
     * GET /api/v1/payments/{payment_id}/status
     */
    public function getPaymentStatus(array $params): void
    {
        try {
            $paymentId = $params['payment_id'];
            
            // Get payment details from Razorpay
            $paymentDetails = $this->getRazorpayPaymentDetails($paymentId);

            if ($paymentDetails) {
                Response::success([
                    'payment_id' => $paymentDetails['id'],
                    'status' => $paymentDetails['status'],
                    'amount' => $paymentDetails['amount'],
                    'currency' => $paymentDetails['currency'],
                    'created_at' => $paymentDetails['created_at']
                ]);
            } else {
                Response::error('Payment not found', 404);
            }

        } catch (\Exception $e) {
            error_log('Get payment status failed: ' . $e->getMessage());
            Response::error('Failed to get payment status', 500);
        }
    }

    /**
     * Create Razorpay order via API
     */
    private function createRazorpayOrderAPI(array $orderData): ?array
    {
        $url = 'https://api.razorpay.com/v1/orders';
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($orderData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Basic ' . base64_encode($this->razorpayKeyId . ':' . $this->razorpayKeySecret)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return json_decode($response, true);
        }

        error_log('Razorpay order creation failed: ' . $response);
        return null;
    }

    /**
     * Verify Razorpay payment signature
     */
    private function verifyRazorpaySignature(string $orderId, string $paymentId, string $signature): bool
    {
        $expectedSignature = hash_hmac('sha256', $orderId . '|' . $paymentId, $this->razorpayKeySecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get Razorpay payment details
     */
    private function getRazorpayPaymentDetails(string $paymentId): ?array
    {
        $url = "https://api.razorpay.com/v1/payments/{$paymentId}";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Basic ' . base64_encode($this->razorpayKeyId . ':' . $this->razorpayKeySecret)
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            return json_decode($response, true);
        }

        return null;
    }

    /**
     * Send order notifications (SMS, Email, Invoice)
     */
    private function sendOrderNotifications(array $order, array $user, array $orderItems): void
    {
        try {
            // Only send notifications if order status is 'confirmed' and payment is 'paid'
            if ($order['status'] !== 'confirmed' || $order['payment_status'] !== 'paid') {
                return;
            }

            // Send SMS confirmation (async)
            $this->notificationService->sendOrderConfirmationSMS($order, $user);

            // Send email confirmation (async)
            $this->notificationService->sendOrderConfirmationEmail($order, $user, $orderItems);

            // Send tax invoice email (async)
            $this->notificationService->sendTaxInvoiceEmail($order, $user, $orderItems);

            error_log("Order notifications queued for order: {$order['order_number']}");

        } catch (\Exception $e) {
            error_log("Failed to queue order notifications: " . $e->getMessage());
            // Don't throw exception as this shouldn't block the payment flow
        }
    }
}
