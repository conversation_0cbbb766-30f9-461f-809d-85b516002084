// upload-folder.js
import { v2 as cloudinary } from 'cloudinary';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import sharp from 'sharp';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cloudinary configuration
cloudinary.config({
  cloud_name: 'dsp0zmfcx',
  api_key: '336461674786964',
  api_secret: 'qAUvO-stA_YrE6f3e1hBmw4hpYc'
});

const folderPath = path.join(__dirname, 'ProductsImages');
const cloudFolder = 'ProductsImages';

// Supported image file extensions
const supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.tiff'];

// Function to check if file is an image
const isImageFile = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  return supportedExtensions.includes(ext);
};

// Function to get file size in MB
const getFileSizeMB = async (filePath) => {
  const stats = await fs.stat(filePath);
  return stats.size / (1024 * 1024);
};

// Function to compress image if it's too large
const compressImageIfNeeded = async (filePath, filename, maxSizeMB = 9.5) => {
  const sizeMB = await getFileSizeMB(filePath);

  if (sizeMB <= maxSizeMB) {
    return filePath; // No compression needed
  }

  console.log(`🔧 Compressing ${filename} (${sizeMB.toFixed(1)}MB -> target: ${maxSizeMB}MB)`);

  const ext = path.extname(filename).toLowerCase();
  const compressedDir = path.join(path.dirname(filePath), 'compressed');

  // Create compressed directory if it doesn't exist
  try {
    await fs.mkdir(compressedDir, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  const compressedPath = path.join(compressedDir, filename);

  try {
    let quality = 85; // Start with 85% quality
    let compressedSizeMB = sizeMB;

    // Try different quality levels until we get under the limit
    while (quality >= 20 && compressedSizeMB > maxSizeMB) {
      if (ext === '.png') {
        await sharp(filePath)
          .png({ quality, compressionLevel: 9 })
          .toFile(compressedPath);
      } else {
        await sharp(filePath)
          .jpeg({ quality, progressive: true })
          .toFile(compressedPath);
      }

      compressedSizeMB = await getFileSizeMB(compressedPath);

      if (compressedSizeMB > maxSizeMB) {
        quality -= 10; // Reduce quality and try again
      }
    }

    const finalSizeMB = await getFileSizeMB(compressedPath);
    console.log(`✅ Compressed ${filename}: ${sizeMB.toFixed(1)}MB -> ${finalSizeMB.toFixed(1)}MB (${quality}% quality)`);

    return compressedPath;
  } catch (error) {
    console.log(`⚠️  Compression failed for ${filename}, using original: ${error.message}`);
    return filePath;
  }
};

// Function to upload a single file with retry mechanism
const uploadFileWithRetry = async (filePath, filename, maxRetries = 3) => {
  // First, compress the image if needed
  const processedFilePath = await compressImageIfNeeded(filePath, filename);

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await cloudinary.uploader.upload(processedFilePath, {
        folder: cloudFolder,
        public_id: path.parse(filename).name, // Use exact filename without extension
        use_filename: true,
        unique_filename: false,
        overwrite: true, // Overwrite if exists
        resource_type: 'auto'
      });

      return {
        success: true,
        filename,
        url: result.secure_url,
        public_id: result.public_id,
        compressed: processedFilePath !== filePath
      };
    } catch (error) {
      if (attempt === maxRetries) {
        return {
          success: false,
          filename,
          error: error.message
        };
      }
      console.log(`⚠️  Retry ${attempt}/${maxRetries} for ${filename}...`);
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};

// Main upload function
const uploadAllImages = async () => {
  try {
    console.log('🚀 Starting Cloudinary upload process...');
    console.log(`📁 Source folder: ${folderPath}`);
    console.log(`☁️  Cloudinary folder: ${cloudFolder}`);
    console.log('─'.repeat(60));

    // Check if folder exists
    try {
      await fs.access(folderPath);
    } catch (error) {
      throw new Error(`ProductsImages folder not found at: ${folderPath}`);
    }

    // Read directory contents
    const files = await fs.readdir(folderPath);

    // Filter only image files
    const imageFiles = [];
    for (const file of files) {
      const filePath = path.join(folderPath, file);
      const stats = await fs.stat(filePath);

      if (stats.isFile() && isImageFile(file)) {
        imageFiles.push(file);
      }
    }

    if (imageFiles.length === 0) {
      console.log('❌ No image files found in the ProductsImages folder');
      return;
    }

    console.log(`📸 Found ${imageFiles.length} image files to upload:`);
    imageFiles.forEach((file, index) => {
      console.log(`   ${index + 1}. ${file}`);
    });
    console.log('─'.repeat(60));

    // Upload all files concurrently with progress tracking
    const uploadPromises = imageFiles.map(async (file, index) => {
      const filePath = path.join(folderPath, file);
      console.log(`⏳ [${index + 1}/${imageFiles.length}] Uploading: ${file}`);

      const result = await uploadFileWithRetry(filePath, file);

      if (result.success) {
        console.log(`✅ [${index + 1}/${imageFiles.length}] Success: ${file}${result.compressed ? ' (compressed)' : ''}`);
        console.log(`   📎 URL: ${result.url}`);
      } else {
        console.log(`❌ [${index + 1}/${imageFiles.length}] Failed: ${file}`);
        console.log(`   💥 Error: ${result.error}`);
      }

      return result;
    });

    // Wait for all uploads to complete
    console.log('⏳ Processing uploads...');
    const results = await Promise.all(uploadPromises);

    // Summary
    console.log('─'.repeat(60));
    console.log('📊 UPLOAD SUMMARY:');

    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    console.log(`✅ Successful uploads: ${successful.length}`);
    console.log(`❌ Failed uploads: ${failed.length}`);
    console.log(`📈 Success rate: ${((successful.length / results.length) * 100).toFixed(1)}%`);

    if (successful.length > 0) {
      console.log('\n🎉 Successfully uploaded files:');
      successful.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.filename} -> ${result.url}`);
      });
    }

    if (failed.length > 0) {
      console.log('\n💥 Failed uploads:');
      failed.forEach((result, index) => {
        console.log(`   ${index + 1}. ${result.filename} - ${result.error}`);
      });
    }

    console.log('─'.repeat(60));
    console.log(failed.length === 0 ? '🎊 All uploads completed successfully!' : '⚠️  Some uploads failed. Check errors above.');

  } catch (error) {
    console.error('💥 Fatal error during upload process:', error.message);
    process.exit(1);
  }
};

// Run the upload process
uploadAllImages();
